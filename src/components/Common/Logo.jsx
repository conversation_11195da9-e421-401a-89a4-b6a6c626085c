import { Link } from 'components/Common';
import { HostContext } from 'context/host';
import { useContext } from 'react';

const Logo = ({ theme }) => {
  const { marketingURL } = useContext(HostContext);
  return (
    <Link
      className={`logo ${theme || ''} ${process.env.NEXT_PUBLIC_IS_STAGING ? 'logo--staging' : ''}`}
      href={marketingURL}
      title="Yordar"
    >
      Yordar
    </Link>
  );
};

export default Logo;
