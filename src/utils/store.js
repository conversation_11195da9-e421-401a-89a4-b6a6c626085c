import { createStore, applyMiddleware } from 'redux';
import thunk from 'redux-thunk';
import { composeWithDevTools } from 'redux-devtools-extension';
import { HYDRATE, createWrapper } from 'next-redux-wrapper';
import rootReducer from '../reducers';

const initialState = {};
const middleware = [thunk];

const masterReducer = (state, action) => {
  if (action.type === HYDRATE) {
    const nextState = {
      ...state,
      ...action.payload,
    };
    return nextState;
  }
  return rootReducer(state, action);
};

// create a makeStore function
const makeStore = (context) =>
  createStore(masterReducer, initialState, composeWithDevTools(applyMiddleware(...middleware)));

// export an assembled wrapper
export const wrapper = createWrapper(makeStore);
