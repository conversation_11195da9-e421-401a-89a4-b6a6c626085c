import { create } from 'zustand';
import { produce } from 'immer';
import {
  DY<PERSON>MIC_UPDATE_ORDER_LINE_ENDPOINT,
  DYNAMIC_FETCH_DELIVERY_WINDOWS_ENDPOINT,
  DYNAMIC_CHECKOUT_SUBMIT_ENDPOINT,
  DYNAMIC_WOOLWORTHS_ORDER_VALIDATION_ENDPOINT,
  DYNAMIC_CHECKOUT_EDIT_ENDPOINT,
  DYNAMIC_UPDATE_ORDER_COMMISSION_ENDPOINT,
  DYNAMIC_LOCATION_ENDPOINT,
  ORDER_LINES_ENDPOINT,
} from 'api/endpoints';
import yordar from 'api/yordar';
import { getDateConfig } from 'utils/dateTime';
import moment from 'moment';

import { toast } from 'react-toastify';
import { defaultToastOptions, toastTypeOptions } from 'utils/toasterConfig';

const MANDATORY_FIELDS = {
  name: null,
  creditCardId: null,
  contactName: null,
  companyName: null,
  phone: null,
  deliveryAt: null,
  deliveryAddress: null,
};

const fieldLabels = {
  name: 'Order Name',
  numberOfPeople: 'Number of People',
  cpoId: 'PO number',
  gstFreeCpoId: 'PO Number for GST free items',
  deliveryAddress: 'Delivery Address',
  deliveryAt: 'Delivery DateTime',
  contactName: 'Contact Name',
  phone: 'Contact Phone',
  companyName: 'Company Name',
  departmentIdentity: 'Cost Centre ID',
  creditCardId: 'Valid Payment Option',
  pantryManager: 'Pantry Manager',
};

const useCheckoutStore = create((set, get) => ({
  activeOrderID: null,
  orderValidated: false,
  checkout: {
    saved_credit_cards: [],
    delivery_window_dates: [],
  },
  costCentreID: null,
  error: null,
  order: {
    checkoutErrors: {},
    deliveryWindowDate: null,
    submitNewCardWithMode: null,
  },
  orders: {},
  orderSuppliers: {},
  paymentMethod: null,
  purchaseOrder: null,
  validations: MANDATORY_FIELDS,
  inProgress: null,
  quoteDetails: {
    message: '',
    emails: '',
  },
  setActiveOrderID: (id) => set({ activeOrderID: id }),
  setOrder: (order) => {
    const { validations } = get();
    const { orders, order_suppliers, ...checkoutOrder } = order;
    const activeOrderID = Object.keys(orders)[0];
    let momentDate;
    let dateConfig;
    if (checkoutOrder?.deliveryAt) {
      momentDate = moment(checkoutOrder?.deliveryAt, 'YYYY-MM-DD hh:mm a').format('YYYY-MM-DD HH:mm');
    }
    if (momentDate) {
      dateConfig = getDateConfig(new Date(momentDate));
    }

    set({
      order: {
        ...checkoutOrder,
        ...(checkoutOrder?.deliveryAt && {
          deliveryAtDisplayTime: dateConfig.displayFormatted.time,
          deliveryAtDisplayDate: dateConfig.displayFormatted.date,
        }),
      },
      activeOrderID,
      orders,
      ...(order_suppliers && { orderSuppliers: order_suppliers }),
      validations: {
        ...validations,
        ...(order.name && { name: true }),
        ...(order.isCateringOrder && { numberOfPeople: order.numberOfPeople ? true : null }),
        ...(order.creditCardId && { creditCardId: true }),
        ...(order.contactName && { contactName: true }),
        ...(order.companyName && { companyName: true }),
        ...(order.phone && { phone: true }),
        ...(order.deliveryAt && { deliveryAt: true }),
        ...(order.deliveryAddress && { deliveryAddress: true }),
        ...(order.cpoId && { cpoId: true }),
        ...(order.departmentIdentity && { cpoId: true }),
      },
    });
  },
  setOrderValidated: (bool) => {
    set({ orderValidated: bool });
  },
  setValidationField: (field, bool, remove) => {
    const { validations } = get();
    if (remove) {
      const { [field]: removedField, ...filteredValidations } = validations;
      return set({ validations: filteredValidations });
    }
    set({ validations: { ...validations, [field]: bool } });
  },
  setAddress: (addressFields) => {
    const { order, validations } = get();
    const {
      deliveryAddress,
      deliveryAddressLevel,
      deliveryInstruction,
      deliverySuburbId,
      deliverySuburbLabel,
    } = addressFields;
    set({
      order: {
        ...order,
        deliveryAddress,
        deliveryAddressLevel,
        deliveryInstruction,
        deliverySuburbId,
        deliverySuburbLabel,
      },
      validations: { ...validations, deliveryAddress: true },
    });
  },
  setDate: (dateFields) => {
    const { order, validations } = get();
    const { deliveryDate, deliveryTime, deliveryAt, deliveryAtDisplayTime, deliveryAtDisplayDate } = dateFields;
    set({
      order: {
        ...order,
        deliveryDate,
        deliveryTime,
        deliveryAt,
        deliveryAtDisplayTime,
        deliveryAtDisplayDate,
      },
      validations: {
        ...validations,
        deliveryAt: true,
      },
    });
  },
  setPurchaseOrder: (purchaseOrder, costCentreID) => set({ purchaseOrder, costCentreID }),
  setCheckout: (checkout) => {
    const {
      validations,
      order: { cpoId, departmentIdentity, pantryManager },
    } = get();

    set({
      checkout,
      validations: {
        ...validations,
        ...(checkout.requires_purchase_order && { cpoId: cpoId ? true : null }),
        ...(checkout.required_department_identity_format && { departmentIdentity: departmentIdentity ? true : null }),
        ...(!checkout.can_pay_on_account &&
          !checkout.saved_credit_cards.length && { creditCardNum: null, creditCardName: null }),
        ...(checkout.pantry_managers && { pantryManager: pantryManager ? true : null }),
        ...(checkout.requires_billing_details && {
          billing_name: checkout.billing_details.name ? true : null,
          billing_phone: checkout.billing_details.phone ? true : null,
          billing_email: checkout.billing_details.email ? true : null,
          billing_suburb_id: null,
          billing_address: checkout.billing_details.address ? true : null,
        }),
      },
    });
  },
  setOrderDetail: (field, value) => {
    const { order, validations } = get();
    const isValidEntry = !!value;
    set({
      order: { ...order, [field]: value },
      ...(Object.keys(validations).includes(field) && { validations: { ...validations, [field]: isValidEntry } }),
    });
  },
  setBillingDetail: (field, value) => {
    const { checkout, validations } = get();
    const isValidEntry = !!value;
    const validationField = `billing_${field}`;
    set({
      checkout: {
        ...checkout,
        billing_details: { ...checkout.billing_details, [field]: value },
      },
      ...(Object.keys(validations).includes(validationField) && {
        validations: { ...validations, [validationField]: isValidEntry },
      }),
    });
  },
  updateOrderCommission: async ({ orderId, commission }) => {
    try {
      const {
        data: { order: responseOrder },
      } = await yordar({
        method: 'PUT',
        url: DYNAMIC_UPDATE_ORDER_COMMISSION_ENDPOINT(orderId),
        data: {
          order: {
            commission,
            id: orderId,
          },
        },
        withCredentials: true,
      });
      set(
        produce((draft) => {
          const updatedOrder = draft.orders[responseOrder.id];
          updatedOrder.commission = responseOrder.commission;
          updatedOrder.totals = responseOrder.totals;
          updatedOrder.locations = responseOrder.locations;
        })
      );
    } catch (err) {
      // do nothing
    }
  },
  attachCoupon: ({ orderId, couponCode, totals }) => {
    set(
      produce((draft) => {
        const updatedOrder = draft.orders[orderId];
        updatedOrder.coupon_code = couponCode;
        updatedOrder.totals = totals;
      })
    );
  },
  updateOrderSupplier: ({ orderID, orderSupplier, totals }) => {
    set(
      produce((draft) => {
        const updatedOrder = draft.orders[orderID];
        updatedOrder.orderSuppliers[orderSupplier.supplier_profile_id] = orderSupplier;
        updatedOrder.totals = totals;
      })
    );
  },
  updateTotals: ({ orderId, totals }) => {
    set(
      produce((draft) => {
        const updatedOrder = draft.orders[orderId];
        updatedOrder.totals = totals;
      })
    );
  },
  addOrderLine: async ({ orderLine, locationID }) => {
    const { activeOrderID } = get();

    const { data } = await yordar(ORDER_LINES_ENDPOINT, {
      method: 'post',
      data: {
        order_id: activeOrderID,
        location_id: locationID,
        order_lines: [orderLine],
      },
      withCredentials: true,
    });

    set(
      produce((draft) => {
        const updatedOrder = draft.orders[data.order.id];
        updatedOrder.totals = data.order.totals;
        const updatedOrderLines = updatedOrder.locations[data.location.id].suppliers?.[data.supplier.id]?.order_lines;
        data.order_lines.forEach((orderline) => {
          updatedOrderLines[orderline.id] = orderline;
        });
      })
    );
  },
  updateOrderLine: async ({ id, order_id, location_id, quantity, note, supplier_id, category_id, custom_item }) => {
    const sanitizedOrderLine = {
      id,
      order_id,
      location_id,
      quantity,
      note,
      category_id,
      custom_item,
    };
    const {
      data: { order_line: responseOrderLine, order: responseOrder },
    } = await yordar(DYNAMIC_UPDATE_ORDER_LINE_ENDPOINT(id), {
      method: 'PUT',
      withCredentials: true,
      data: sanitizedOrderLine,
    });
    set({ orderValidated: false });
    set(
      produce((draft) => {
        const updatedOrder = draft.orders[order_id];
        const orderLineSupplier = updatedOrder.locations[location_id].suppliers[supplier_id];
        orderLineSupplier.order_lines[responseOrderLine.id] = responseOrderLine;
        updatedOrder.totals = responseOrder.totals;
        if (draft.order.isWoolworthsOrder) {
          draft.order.checkoutErrors.order = ['Cart Items Updated. Validate again!'];
        }
      })
    );
  },
  removeOrderLine: async ({ id, order_id, location_id, supplier_id }) => {
    try {
      const {
        data: { order: responseOrder },
      } = await yordar(DYNAMIC_UPDATE_ORDER_LINE_ENDPOINT(id), {
        method: 'DELETE',
        withCredentials: true,
        data: { order_id, location_id },
      });
      set({ orderValidated: false });
      set(
        produce((draft) => {
          const updatedOrder = draft.orders[order_id];
          const orderLineSupplier = updatedOrder.locations[location_id].suppliers[supplier_id];
          const { [id]: removedOrderLine, ...remainingOrderLines } = orderLineSupplier.order_lines;
          orderLineSupplier.order_lines = remainingOrderLines;
          updatedOrder.totals = responseOrder.totals;
          if (draft.order.isWoolworthsOrder) {
            draft.order.checkoutErrors.order = ['Cart Items Updated. Validate again!'];
          }
        })
      );
    } catch (err) {
      if (err.response?.status === 422 && err.response?.data?.errors?.length) {
        err.response.data.errors.forEach((error) => {
          toast.error(error, { ...defaultToastOptions, ...toastTypeOptions.info });
        });
      }
      throw err;
    }
  },
  fetchDeliveryWindows: async (id) => {
    try {
      const {
        data: { delivery_window_dates },
      } = await yordar.get(DYNAMIC_FETCH_DELIVERY_WINDOWS_ENDPOINT(id));
      set((state) => ({
        checkout: {
          ...state.checkout,
          delivery_window_dates,
        },
      }));
    } catch (err) {
      alert('We were unable to fetch delivery window. Please try again!');
    }
  },
  setQuoteDetails: (quoteDetails) => {
    set((state) => ({
      quoteDetails: { ...state.quoteDetails, ...quoteDetails },
    }));
  },
  setInProgress: (status) => set({ inProgress: status }),
  validateWoolworthsOrder: async (isSequentialValidation) => {
    const { order } = get();

    const { data: validatedOrder } = await yordar.get(DYNAMIC_WOOLWORTHS_ORDER_VALIDATION_ENDPOINT(order.id), {
      withCredentials: true,
    });
    const { orders, checkoutErrors } = validatedOrder;
    set((state) => ({
      orders,
      order: {
        ...state.order,
        checkoutErrors,
      },
    }));
    let validationErrors = Object.values(checkoutErrors).flat();
    if (isSequentialValidation && validationErrors.length) {
      if (checkoutErrors.order.length) {
        validationErrors = [...validationErrors, 'Has Order Item Errors'];
      }
      throw validationErrors.map((error) => new Error(error));
    }
  },
  submitOrder: async ({ mode }) => {
    const { order, quoteDetails, validations, setOrderDetail, setValidationField, checkout } = get();

    const invalidFields = Object.keys(validations)?.filter((validation) => !validations[validation]);
    if (invalidFields.length) {
      invalidFields.forEach((invalidField) => setValidationField(invalidField, false));
      setOrderDetail('submitNewCardWithMode', null);
      const errors = invalidFields.map((field) => new Error(`${fieldLabels[field]} is missing`));
      throw errors;
    }

    const sanitizedOrder = {
      // delivery details
      delivery_address_level: order.deliveryAddressLevel,
      delivery_address: order.deliveryAddress,
      delivery_suburb_id: order.deliverySuburbId,
      delivery_at: order.deliveryAt,
      delivery_instruction: order.deliveryInstruction,
      delivery_type: order.deliveryType,

      // order details
      name: order.name,
      number_of_people: order.numberOfPeople,
      contact_name: order.contactName,
      company_name: order.companyName,
      phone: order.phone,
      skip: order.skip,

      // payment details
      credit_card_id: order.creditCardId,
      invoice_individually: order.invoiceIndividually,

      // invoice details
      cpo_id: order.cpoId,
      gst_free_cpo_id: order.gstFreeCpoId,
      department_identity: order.departmentIdentity,
      pantry_manager_id: order.pantryManager,
    };
    if (order.woolworthsOrderId && order.woolworthsOrderID) {
      sanitizedOrder.associated_woolworths_order_attributes = {
        id: order.woolworthsOrderId,
        woolworths_order_id: order.woolworthsOrderID,
      };
    }
    if (order.mealPlanId) {
      sanitizedOrder.meal_plan_id = order.mealPlanId;
    }

    const sanitizedBillingDetails = checkout.requires_billing_details ? checkout.billing_details : null;

    const endpoint = ['draft', 'quoted'].includes(order.status)
      ? DYNAMIC_CHECKOUT_SUBMIT_ENDPOINT(order.id)
      : DYNAMIC_CHECKOUT_EDIT_ENDPOINT(order.id);

    const { data: successOrder } = await yordar(endpoint, {
      method: 'post',
      data: {
        order: sanitizedOrder,
        ...(['quote', 'save-quote', 'save-for-later', 'one-off', 'subsequent'].includes(mode) && { mode }),
        ...(!!quoteDetails && { quote_emails: quoteDetails.emails, quote_message: quoteDetails.message }),
        billing_details: sanitizedBillingDetails,
      },
      withCredentials: true,
    });

    if (order.status !== 'draft') {
      return successOrder.redirect_url;
    }
    set({
      order: {
        ...order,
        id: successOrder.id,
        status: successOrder.status,
        orderLink: successOrder.order_link,
        geoCoordinates: successOrder.geo_coordinates,
        supplierIndexPageUrl: successOrder.supplier_index_page_url,
        ...(successOrder.quote_mode && { quoteMode: successOrder.quote_mode }),
      },
    });
    return '';
  },

  // custom order related
  setLocation: async ({ id = '', name, orderID, method, isCustomOrder = false }) => {
    try {
      const { data } = await yordar(DYNAMIC_LOCATION_ENDPOINT(id), {
        method,
        data: {
          ...(name && { location: { details: name } }),
          ...(orderID && { order_id: orderID }),
          ...(isCustomOrder && { is_custom_order: true }),
        },
        withCredentials: true,
      });
      if (method === 'delete') {
        set(
          produce((draft) => {
            const targetOrder = draft.orders[data.order.id];
            if (targetOrder?.locations[id]) {
              draft.activeLocationID = null;
              delete targetOrder.locations[id];
            }
          })
        );
        return;
      }
      set(
        produce((draft) => {
          const updatedOrder = draft.order;
          if (!updatedOrder.id) updatedOrder.id = data.order.id;
          const targetOrder = draft.orders[data.order.id];
          const targetLocation = targetOrder?.locations[data.id];
          if (!targetOrder) {
            draft.orders[data.order.id] = {
              ...data.order,
              locations: { [data.id]: { id: data.id, name: data.details, suppliers: {} } },
            };
            draft.activeLocationID = data.id;
            draft.activeOrderID = data.order.id;
            return;
          }
          draft.activeLocationID = data.id;
          draft.activeOrderID = data.order.id;
          if (!targetLocation) {
            targetOrder.locations[data.id] = { id: data.id, name: data.details, suppliers: {} };
            return;
          }
          targetOrder.locations[data.id].name = data.details;
        })
      );
    } catch (error) {
      console.error('Failed to add/update location', error);
    }
  },
  addSupplier: ({ orderID, locationID, supplier }) => {
    set(
      produce((draft) => {
        const targetOrder = draft.orders[orderID];
        const targetLocation = targetOrder?.locations[locationID];
        draft.orderSuppliers[supplier.id] = {
          id: supplier.id,
          name: supplier.name,
        };
        targetOrder.orderSuppliers ||= {};
        targetOrder.orderSuppliers[supplier.id] = {
          supplier_name: supplier.name,
          supplier_profile_id: supplier.id,
        };
        targetLocation.suppliers[supplier.id] = {
          ...supplier,
          order_lines: {},
        };
      })
    );
  },
  removeSupplier: ({ orderID, locationID, supplierID }) => {
    set(
      produce((draft) => {
        const targetOrder = draft.orders[orderID];
        const targetLocation = targetOrder?.locations[locationID];
        delete draft.orderSuppliers[supplierID];
        delete targetLocation.suppliers[supplierID];
      })
    );
  },
  setError: (error) => set({ error }),
}));

export default useCheckoutStore;
