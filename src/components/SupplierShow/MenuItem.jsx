import { memo } from 'react';
import { useDispatch } from 'react-redux';
import yordar from 'api/yordar';

import { DYNAMIC_FAVOURITE_ITEM_ENDPOINT } from 'api/endpoints';
import { setFavouriteItem } from 'actions';
import { FavouriteHeart, Image } from 'components/Common';
import { Dietary } from 'components/SupplierShow';
import { truncateText } from 'utils/menuItemHelper';
import useModalStore from 'store/useModalStore';
import useMenuItemStore from 'store/useMenuItemStore';

const MenuItem = memo(function MenuItemCard({ item, searchedMenuItem, user }) {
  const {
    id,
    description,
    dietaries,
    image_id: imageId,
    minimum_quantity: minimumQuantity,
    name,
    display_price: displayPrice,
    serving_sizes: servings,
    discount,
    has_promotion: hasPromotion,
    is_favourite: isFavourite,
    over_budget: overBudget,
  } = item;
  const menuItemNotFoundInSearch = searchedMenuItem && !name.toLowerCase().includes(searchedMenuItem.toLowerCase());
  const dispatch = useDispatch();
  const setModalOpen = useModalStore((state) => state.setModalOpen);
  const setMenuItem = useMenuItemStore((state) => state.setMenuItem);
  if (menuItemNotFoundInSearch) return null;
  const servingsHaveDiscount = servings?.some((serving) => serving.discount);

  return (
    <li
      className={`menu-section-item${overBudget ? ' over-budget' : ''}`}
      onClick={() => {
        setModalOpen(true);
        setMenuItem(item);
      }}
      role="presentation"
    >
      <div className="menu-section-item-title-and-add-btn">
        <h4>{truncateText(name, 40)}</h4>
        {user && (
          <FavouriteHeart
            id={id}
            isFavourite={isFavourite}
            changeFavouriteStatus={changeFavouriteStatus}
            dispatch={dispatch}
          />
        )}
      </div>
      <div className="menu-section-item-description-and-image">
        <div className="menu-section-item-description">
          <p>{description}</p>
          <div className="dietaries-container">
            {dietaries?.map((dietary) => (
              <Dietary dietary={dietary} />
            ))}
          </div>
        </div>
        {imageId && (
          <Image
            url={imageId}
            className="menu-section-item-image"
            width={100}
            height={100}
            quality={2}
            transform="fit"
          />
        )}
      </div>
      <div className="menu-section-item-price-and-quantity">
        {servings?.length ? (
          <>
            <span className={`${servingsHaveDiscount ? 'rate-card-price' : ''}`}>
              {servingsHaveDiscount ? 'Custom Pricing' : displayPrice}
            </span>
          </>
        ) : (
          <>
            <span className={discount ? 'rate-card-price strike' : ''}>{displayPrice}</span>
            {discount && hasPromotion && (
              <span>
                <span className="special-label">SPECIAL</span>
                <span className="special-price">${discount}</span>
              </span>
            )}
            {discount && !hasPromotion && <span>Your Price: ${discount}</span>}
          </>
        )}
        {minimumQuantity > 1 && <span>Min Qty: {minimumQuantity}</span>}
      </div>
    </li>
  );
});

export default MenuItem;

async function changeFavouriteStatus({ e, id, method, dispatch, setBeatingHeart }) {
  e.stopPropagation();
  setBeatingHeart(true);
  await yordar(DYNAMIC_FAVOURITE_ITEM_ENDPOINT(id), {
    method,
    withCredentials: true,
  });
  setBeatingHeart(false);
  return dispatch(setFavouriteItem({ id, isFavourite: method === 'put' }));
}
