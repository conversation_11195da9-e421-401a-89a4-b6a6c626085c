import { useContext, useState } from 'react';
import yordar from 'api/yordar';

import { UserContext } from 'context/user';
import { FavouriteHeart } from 'components/Common';
import { DYNAMIC_FAVOURITE_SUPPLIER_ENDPOINT } from 'api/endpoints';
import { setFavouriteSupplier } from 'actions';

const SupplierDetails = ({ supplier, setActiveCard }) => (
  <div className="listing-grid-item-body">
    <SupplierNameAndHeart id={supplier.id} name={supplier.name} isFavourite={supplier.is_favourite} />
    <SupplierMeta delivery={supplier.delivery_fee} leadTime={supplier.lead_time} />
    <SupplierAbout supplier={supplier} setActiveCard={setActiveCard} />
  </div>
);

const SupplierNameAndHeart = ({ id, name, isFavourite }) => {
  const { user } = useContext(UserContext);

  return (
    <div className="listing-grid-item-name-and-heart">
      <span className="listing-grid-item-name">{name}</span>
      {user && <FavouriteHeart id={id} isFavourite={isFavourite} changeFavouriteStatus={changeFavouriteStatus} />}
    </div>
  );
};

const SupplierMeta = ({ delivery, leadTime }) => {
  if (!delivery && (!leadTime || leadTime === 'Unknown')) return null;
  return (
    <div className="listing-grid-item-meta">
      {delivery && <span className="listing-grid-item-meta-item">{delivery}</span>}
      {leadTime && leadTime !== 'Unknown' && <span className="listing-grid-item-meta-item">Lead Time {leadTime}</span>}
    </div>
  );
};

const SupplierAbout = ({ supplier }) => {
  const { user } = useContext(UserContext);
  const isQuantiumCompany = user?.profile?.company_id === 10878 || user?.profile?.company_id === 10890;
  const hideRatings = isQuantiumCompany && supplier.name === 'Woolworths';
  return (
    <div className="listing-grid-item-about">
      <div className="listing-grid-item-about-ratings">
        {!hideRatings && (
          <>
            <span className="icon icon-large icon-smiley supplier-rating-card" />
            {supplier.rating > 0 && <span className="rating">{supplier.rating.toFixed(1)} </span>}
            <span>{supplier.rating_count} Ratings</span>
          </>
        )}
      </div>
      <div className="listing-grid-item-about-hours-and-info">
        {supplier.operating_hours && (
          <SupplierPanel type="hours" info={`${supplier.operating_days} | ${supplier.operating_hours}`} />
        )}
        {supplier.description && <SupplierPanel type="info" info={supplier.description} />}
      </div>
    </div>
  );
};

const SupplierPanel = ({ type, info }) => {
  const [tooltipActive, setTooltipActive] = useState(false);
  return (
    // eslint-disable-next-line
    <span
      onMouseEnter={() => setTooltipActive(true)}
      onMouseLeave={() => setTooltipActive(false)}
      onClick={(e) => e.preventDefault()} // Prevent clicking from following parent anchor link
      className="listing-grid-item-about-tooltip"
    >
      {type.titleize()}
      {tooltipActive && <p className="supplier-tooltip">{info}</p>}
    </span>
  );
};

async function changeFavouriteStatus({ e, id, method, dispatch, setBeatingHeart }) {
  e.preventDefault();
  setBeatingHeart(true);
  await yordar(DYNAMIC_FAVOURITE_SUPPLIER_ENDPOINT(id), {
    method,
    withCredentials: true,
  });
  setBeatingHeart(false);
  return dispatch(setFavouriteSupplier({ id, isFavourite: method === 'put' }));
}

export default SupplierDetails;
