import { Image, Link, Spinner } from 'components/Common';
import useModalStore from 'store/useModalStore';
import useMenuItemStore from 'store/useMenuItemStore';
import useSearchResultsStore from 'store/useSearchResultsStore';
import useFilterStore from 'store/useFilterStore';

const SearchResults = ({ searchResults, searchValue, majorSupplier = false, showSpinner }) => {
  const toggleFilter = useFilterStore((state) => state.toggleFilter);
  const setModalOpen = useModalStore((state) => state.setModalOpen);
  const setMenuItem = useMenuItemStore((state) => state.setMenuItem);
  const { showInMenu, setShowInMenu } = useSearchResultsStore((state) => ({
    showInMenu: state.showInMenu,
    setShowInMenu: state.setShowInMenu,
  }));

  function caseInsensitiveRegex(str) {
    return new RegExp(str, 'gi');
  }

  const handleSearchItemClick = (e, menuItem) => {
    e.preventDefault();
    setMenuItem(menuItem);
    setModalOpen(true);
  };

  const handleShowInMenu = (e) => {
    e.preventDefault();
    if (showInMenu) {
      return setShowInMenu(false);
    }
    return setShowInMenu(true);
  };

  return (
    <>
      {searchResults?.categories?.length > 0 && !majorSupplier && (
        <>
          <p className="search-result-title">Categories ({searchResults.categories.length})</p>
          {searchResults.categories.map((category) => (
            <div className="search-result-category">
              <span
                onClick={() => toggleFilter(category)}
                dangerouslySetInnerHTML={{
                  __html: category.replace(caseInsensitiveRegex(searchValue), (str) => `<strong>${str}</strong>`),
                }}
              />
            </div>
          ))}
        </>
      )}
      {searchResults?.suppliers?.length > 0 && !majorSupplier && (
        <>
          <p className="search-result-title">Suppliers ({searchResults.suppliers.length})</p>
          {searchResults.suppliers.map((supplier) => (
            <Link key={supplier.id} href={`/show/${supplier.slug}`} className="search-result-container">
              <Image
                url={supplier.image_id}
                width={44}
                height={44}
                quality={2}
                className="circle-icon no-shrink"
                alt={`${supplier.name} logo`}
                noContainer
              />
              <div className="search-result-container">
                <span
                  className="search-result-info"
                  dangerouslySetInnerHTML={{
                    __html: supplier.name.replace(
                      caseInsensitiveRegex(searchValue),
                      (str) => `<strong>${str}</strong>`
                    ),
                  }}
                />
              </div>
            </Link>
          ))}
        </>
      )}
      {searchResults?.menuItems?.length > 0 && (
        <>
          <div className="search-result-title">
            <div>
              Menu Items ({searchResults.menuItems.length >= 100 ? '100+' : searchResults.menuItems.length})
              {showSpinner && <Spinner size="small" color="primary" />}
            </div>
            {majorSupplier && (
              <a className="in-menu-button" onClick={handleShowInMenu}>
                {showInMenu ? 'Remove from Menu' : 'Show In Menu'}
              </a>
            )}
          </div>

          {searchResults.menuItems.map((menuItem) => (
            <div className="search-result-container">
              {menuItem.image_id ? (
                <Image
                  url={menuItem.image_id}
                  width={44}
                  height={44}
                  className="circle-icon no-shrink"
                  quality={2}
                  alt={`${menuItem.name}`}
                  transform="pad"
                  noContainer
                />
              ) : (
                <span className="circle-icon no-image no-margin">{menuItem.name[0]}</span>
              )}
              <div className="search-result-info">
                <a
                  href={`/show/${menuItem.supplier_slug}?menu_item_id=${menuItem.id}`}
                  onClick={majorSupplier ? (e) => handleSearchItemClick(e, menuItem) : null}
                >
                  <span
                    className="search-result-name"
                    dangerouslySetInnerHTML={{
                      __html: menuItem.name.replace(
                        caseInsensitiveRegex(searchValue),
                        (str) => `<strong>${str}</strong>`
                      ),
                    }}
                  />
                </a>
                <p className="search-result-label">
                  {menuItem.discount && menuItem.has_promotion && (
                    <span>
                      <span className="special-label">SPECIAL</span>
                      <span className="special-price">${menuItem.discount}</span>
                    </span>
                  )}
                  {!menuItem.discount && <span>{menuItem.display_price}</span>}
                  {!majorSupplier && (
                    <>
                      {' - '}
                      <Link href={`/show/${menuItem.supplier_slug}`}>{menuItem.supplier_name}</Link>
                    </>
                  )}
                </p>
              </div>
            </div>
          ))}
        </>
      )}
      {!searchResults?.menuItems?.length && !searchResults?.suppliers?.length && !searchResults?.categories.length && (
        <div className="search-result-no-results">
          <p>No Results Found</p>
        </div>
      )}
    </>
  );
};

export default SearchResults;
