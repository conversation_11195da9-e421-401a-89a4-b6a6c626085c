const NumberControl = ({ value, setValue, isInvalid }) => (
  <div className="checkout-detail-number">
    <button
      type="button"
      className={`number-control${isInvalid ? ' invalid--number-button left' : ''}${!value ? ' empty' : ''}`}
      onClick={() => value > 1 && setValue(value - 1)}
    >
      -
    </button>
    <input
      type="number"
      value={value}
      style={{ borderRadius: 0 }}
      onChange={(e) => setValue(e.target.value)}
      min="1"
      placeholder="People"
      className={`number-of-people checkout-input ${!value ? ' empty' : ''}${
        isInvalid ? ' invalid--input__no-sides' : ''
      }`}
    />
    <button
      type="button"
      className={`number-control${isInvalid ? ' invalid--number-button right' : ''}${!value ? ' empty' : ''}`}
      onClick={() => setValue(value + 1)}
    >
      +
    </button>
  </div>
);

export default NumberControl;
