import { useContext } from 'react';

import { UserContext } from 'context/user';
import useCheckoutStore from 'store/useCheckoutStore';

const CheckoutSuccessComponent = ({ order }) => {
  const { quoteDetails } = useCheckoutStore();
  const {
    user: { first_name: customerName },
  } = useContext(UserContext);

  return (
    <div className="checkout-form" style={{ borderRadius: '6px' }}>
      <div className="checkout-thank-you">
        <svg className="success-checkmark" version="1.1" viewBox="-6 -6 142.2 142.2" xmlns="http://www.w3.org/2000/svg">
          <circle
            className="path circle"
            cx="65.1"
            cy="65.1"
            fill="none"
            r="62.1"
            strokeMiterlimit="10"
            strokeWidth="12"
            stroke="#5ec9bd"
          />
          <polyline
            className="path check"
            fill="none"
            points="100.2,40.2 51.5,88.8 29.8,67.5 "
            strokeLinecap="round"
            strokeMiterlimit="10"
            strokeWidth="12"
            stroke="#5ec9bd"
          />
        </svg>
        <div className="success-info">
          <p className="success-message">
            {order.status === 'quoted' && 'The order has been saved as a Quote!'}
            {order.status === 'saved' && 'The order has been saved for later!'}
            {!order?.mealPlan?.uuid && !['quoted', 'saved'].includes(order.status) && ` Thank You ${customerName}!`}
            {order?.mealPlan?.uuid &&
              !['quoted', 'saved'].includes(order.status) &&
              ` Order successfully placed for ${order.mealPlan.name}`}
          </p>
        </div>
      </div>

      {order.status === 'saved' && <p>Please make sure to place the order when ready!</p>}

      {order.status === 'quoted' && order.quoteMode === 'quote' && !!quoteDetails.emails && (
        <p>
          Quote emails were sent to <strong>'{quoteDetails.emails.replace(';', ', ')}'</strong>
        </p>
      )}

      <div className="checkout-success-details">
        <div>
          <h4 style={{ marginBottom: '16px', fontSize: '20px', fontWeight: 'normal' }}>Your Details</h4>
          <p className="icon icon-user icon-large">{order?.contactName}</p>
          <p className="icon icon-company icon-large">{order?.companyName}</p>
          <p className="icon icon-phone icon-large">{order?.phone}</p>
        </div>
        <div>
          <h4 style={{ marginBottom: '16px', fontSize: '20px', fontWeight: 'normal' }}>Delivery Information</h4>
          {order?.isRecurrent ? (
            <p className="icon icon-recurring icon-large">Recurring Order</p>
          ) : (
            <p className="icon icon-one-off icon-large">One-Off Order</p>
          )}
          <p className="icon icon-calendar icon-large">{order?.deliveryAt}</p>
          <p className="icon icon-marker icon-large">
            {order?.deliveryAddress} {order?.deliverySuburbLabel}
          </p>
        </div>
      </div>
      {order?.deliveryInstruction && (
        <div style={{ borderBottom: '2px solid #eaeaea', paddingBottom: '8px' }}>
          <h4 style={{ marginBottom: '16px', fontSize: '20px', fontWeight: 'normal' }}>Delivery Instructions</h4>
          <p>{order.deliveryInstruction}</p>
        </div>
      )}
      {!!order.geoCoordinates && (
        <div className="order-map">
          <div className="instructions-container">
            <p className="instructions-reminder">
              Please ensure you've provided detailed delivery instructions if the address below is not the address to
              deliver to. You can change the instructions by editing the order from your dashboard
            </p>
          </div>
          <img
            alt="Map"
            src={`http://maps.googleapis.com/maps/api/staticmap?zoom=18&scale=2&size=700x300&markers=${order.geoCoordinates[0]},${order.geoCoordinates[1]}&key=${process.env.NEXT_PUBLIC_GOOGLE_STATIC_MAP_KEY}`}
          />
        </div>
      )}
    </div>
  );
};

export default CheckoutSuccessComponent;
