import React, { useRef, useEffect, useState } from 'react';

function Smiley({ value }) {
  const canvasRef = useRef(null);
  const [canvasSize, setCanvasSize] = useState(getCanvasSize());

  function getCanvasSize() {
    const size = Math.min(window.innerWidth, window.innerHeight) * 0.9;
    return Math.min(size, 600);
  }

  useEffect(() => {
    function handleResize() {
      setCanvasSize(getCanvasSize());
    }

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    const scaleFactor = canvasSize / 400; // because originally it was designed for 400x400
    const centerX = canvasSize / 2;
    const centerY = canvasSize / 2;

    const faceRadius = (canvasSize / 2) * 0.9; // Use 90% of half the canvas size for the face radius

    // Clear the canvas
    context.clearRect(0, 0, canvasSize, canvasSize);

    // Fill the circle with black
    context.fillStyle = 'black';
    context.beginPath();
    context.arc(centerX, centerY, faceRadius, 0, 2 * Math.PI);
    context.fill();

    // Compute the smile/frown
    const normalizedValue = (value - 50) / 50;
    const xOffset = 110 * scaleFactor;
    const yOffset = centerY + 60 * scaleFactor;
    const curveIntensity = 60 * scaleFactor;

    const cp1x = centerX - xOffset / 1.5;
    const cp1y = yOffset + normalizedValue * curveIntensity;
    const cp2x = centerX + xOffset / 1.5;
    const cp2y = yOffset + normalizedValue * curveIntensity;
    const xStart = centerX - xOffset;
    const yStart = yOffset;
    const xEnd = centerX + xOffset;
    const yEnd = yOffset;

    // Draw the mouth in white
    context.strokeStyle = 'white';
    context.lineWidth = 10 * scaleFactor;
    context.beginPath();
    context.moveTo(xStart, yStart);
    context.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, xEnd, yEnd);
    context.stroke();
  }, [value, canvasSize]);

  return <canvas ref={canvasRef} width={canvasSize} height={canvasSize} />;
}

export default Smiley;
