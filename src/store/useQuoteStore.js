import create from 'zustand';

const initialState = {
  step: 0,
  notListedCategory: null,
  noCategoryError: false,
  noEventRequirementsError: false,
  formFields: {
    location: { value: '', isError: false },
    date: { value: null, isError: false },
    time: { value: null, isError: false },
    estimatedAttendees: { value: '', isError: false },
    occasion: { value: '', isError: false },
    budget: { value: '', isError: false },
    budgetType: { value: 'total' },
    fullName: { value: '', isError: false },
    company: { value: '', isError: false },
    phone: { value: '', isError: false },
    email: { value: '', isError: false },
    officeAddress: { value: '', isError: false },
    staffSize: { value: '', isError: false },
    extraContactInformation: { value: '' },
    notListedAllergyOrDietary: { value: '' },
  },
  multiValueFields: {
    categories: [],
    allergies: [],
    dietaries: [],
    eventRequirements: [],
    eventStyles: [],
    serviceStyles: [],
  },
  boolFields: {
    individuallyPacked: false,
    cutlery: false,
    currentProductList: false,
    needsMerchandisingEquipment: false,
  },
  captchaResponse: null,
};

const toggleArrayItem = (array, item) => {
  if (array.includes(item)) {
    return array.filter((stateItem) => item !== stateItem);
  }
  return [...array, item];
};

const useQuoteStore = create((set) => ({
  ...initialState,
  updateStep: (increment = true) => set((state) => ({ step: state.step + (increment ? 1 : -1) })),
  setNotListedCategory: (notListedCategory) => set({ notListedCategory }),
  setNoCategoryError: (noCategoryError) => set({ noCategoryError }),
  setNoEventRequirementsError: (noEventRequirementsError) => set({ noEventRequirementsError }),
  setFormField: (field, value) =>
    set((state) => ({
      formFields: {
        ...state.formFields,
        [field]: {
          value,
          isError: !value,
        },
      },
    })),
  setMultiValueField: (field, value) =>
    set((state) => {
      const updatedField = toggleArrayItem(state.multiValueFields[field], value);
      return {
        multiValueFields: {
          ...state.multiValueFields,
          [field]: updatedField,
        },
      };
    }),
  setBoolField: (field, value) =>
    set((state) => ({
      boolFields: {
        ...state.boolFields,
        [field]: value,
      },
    })),
  setCaptchaResponse: (response) => set(() => ({ captchaResponse: response })),
}));

export default useQuoteStore;
