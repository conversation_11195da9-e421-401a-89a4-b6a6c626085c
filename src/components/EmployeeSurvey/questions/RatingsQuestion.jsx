const RatingsQuestion = ({ question, answer, setAnswer }) => {
  const handleClick = (event) => {
    setAnswer((state) => ({ ...state, value: event.target.value }));
  };

  const computeBackgroundColor = (num) => `rgba(${255 - (255 * num) / 10}, ${(255 * num) / 10}, 0, 0.1)`;
  return (
    <div className="ratings-container">
      {[...Array(11).keys()].map((num) => {
        const isSelected = Number(answer?.value) === num;
        return (
          <label
            key={`question-${question}-rating-${num}`}
            className={`rating-selector ${isSelected ? 'checked' : ''}`}
            style={{ backgroundColor: computeBackgroundColor(num) }}
          >
            <input type="radio" value={num} checked={isSelected} onClick={handleClick} />
            {num}
          </label>
        );
      })}
    </div>
  );
};

export default RatingsQuestion;
