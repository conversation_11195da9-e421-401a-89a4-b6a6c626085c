import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';
import { Link } from 'components/Common';

import { fetchSupplierMenuSection } from 'actions';

const DropDown = (props) => {
  const {
    options,
    label,
    showCount,
    dropdownClass = '',
    chevronColor = false,
    inline = false,
    scrollSection = false,
    hover = false,
    scrollIntoView = false,
    setAisleAndSection,
    isAisle = false,
  } = props;

  const router = useRouter();
  const dispatch = useDispatch();

  useEffect(() => {
    document.addEventListener('click', handleClick, false);
    return () => {
      document.removeEventListener('click', handleClick, false);
    };
  }, []);

  useEffect(() => {
    const activeDropdowns = document.querySelectorAll('.label.active');
    activeDropdowns.forEach((dropdown) => dropdown.classList.remove('active'));
  }, [router.asPath]);

  // Show and hide drop down onClick
  const toggleDropDown = (event) => {
    event.preventDefault();
    if (event.target.className === 'label active') {
      return event.target.classList.remove('active');
    }
    return event.target.classList.add('active');
  };

  const hoverToggleDropDown = (event, state = false) => {
    const categoryDropdown = document.querySelector('.category-group-dropdown > a');
    if (state === 'on') {
      categoryDropdown.classList.add('active');
    } else {
      categoryDropdown.classList.remove('active');
    }
  };

  // Handle click elsewhere on the screen including another dropdown label
  const handleClick = (event) => {
    if (!event || !event.target.className) return null;
    const selectedElementsCollection = document.getElementsByClassName('label active');
    const selectedElementsArr = Array.from(selectedElementsCollection);
    if (selectedElementsArr.length > 0) {
      selectedElementsArr.filter((item) => item !== event.target).map((item) => item.classList.remove('active'));
    }
    return null;
  };

  const handleAisleAndSectionSelection = async ({ sectionID, sectionNum = 0 }) => {
    setAisleAndSection((state) => {
      if (isAisle) return { activeAisle: sectionNum, activeSection: 0 };
      return { ...state, activeSection: sectionNum };
    });
    await dispatch(fetchSupplierMenuSection({ id: sectionID }));
  };

  const handleSectionClick = (e, option) => {
    e.preventDefault();
    if (!scrollSection) return null;
    const sectionName = document.querySelector(option.href);
    if (setAisleAndSection) {
      handleAisleAndSectionSelection({ sectionID: option.id, sectionNum: option.index });
    }
    if (scrollIntoView) {
      // ie. non-major supplier
      sectionName.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    // eslint-disable-next-line
    <div className={`dropdown-wrapper ${dropdownClass}`} tabIndex="1" style={{ ...(inline && { display: 'inline-block' }) }} onMouseEnter={(e) => hover ? toggleDropDown(e, 'on') : null}
      onMouseLeave={(e) => (hover ? hoverToggleDropDown(e) : null)}
    >
      <a href="#toggle" className="label" onClick={toggleDropDown}>
        {label ? (
          <>
            {label}{' '}
            <span className={`icon ${chevronColor ? `icon-chevron-down-${chevronColor}` : 'icon-chevron-down'}`} />
          </>
        ) : (
          <span className="icon icon-dots" />
        )}
        {showCount && (
          <div className="badge">
            <span className="number">{showCount}</span>
          </div>
        )}
      </a>
      <ul className="dropdown">
        {options.map((option) => (
          <li
            className={`dropdown-item ${option.className || ''}`}
            key={option.label}
            onClick={(e) => handleSectionClick(e, option)}
            role="presentation"
          >
            <Link href={option.href} target={option.target || null} attr={option.label}>
              {option.label}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default DropDown;
