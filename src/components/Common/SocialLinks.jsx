import Image from 'next/image';

const FacebookIcon = require('images/social/facebook.svg');
const InstagramIcon = require('images/social/instagram.svg');
const LinkedInIcon = require('images/social/linkedin.svg');
const PinterestIcon = require('images/social/pinterest.svg');

const socialIcons = {
  Facebook: FacebookIcon,
  Instagram: InstagramIcon,
  LinkedIn: LinkedInIcon,
  Pinterest: PinterestIcon,
};

const socialLinks = [
  {
    url: 'https://www.facebook.com/yordarcorporatecatering',
    platform: 'Facebook',
  },
  {
    url: 'https://www.instagram.com/yordar/',
    platform: 'Instagram',
  },
  {
    url: 'https://www.linkedin.com/company/yordar/',
    platform: 'LinkedIn',
  },
  {
    url: 'https://www.pinterest.com.au/yordar/',
    platform: 'Pinterest',
  },
];

const SocialLinks = () => (
  <div className="social-links">
    <h5>Follow Us</h5>
    <nav className="social-links-nav">
      {socialLinks.map((socialLink) => (
        <a
          key={socialLink.url}
          href={socialLink.url}
          target="_blank"
          rel="noreferrer noopener"
          aria-label={socialLink.platform}
        >
          <Image src={socialIcons[socialLink.platform]} alt="Instagram" />
        </a>
      ))}
    </nav>
  </div>
);

export default SocialLinks;
