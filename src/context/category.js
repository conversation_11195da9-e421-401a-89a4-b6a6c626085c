import { useState, useEffect, createContext } from 'react';
import { useRouter } from 'next/router';

const defaultCategoryContext = 'office-catering';
export const CategoryContext = createContext(defaultCategoryContext);

export const CategoryProviderComponent = ({ children }) => {
  const router = useRouter();
  const [category, setCategory] = useState(defaultCategoryContext);
  useEffect(() => setCategory((state) => router?.query?.category || state), [router.query]);

  return <CategoryContext.Provider value={category}>{children}</CategoryContext.Provider>;
};

export default CategoryProviderComponent;
