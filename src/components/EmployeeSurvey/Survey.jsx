import { Question } from 'components/EmployeeSurvey';
import useEmployeeSurveyStore from 'store/useEmployeeSurveyStore';
import { shallow } from 'zustand/shallow';
import Image from 'next/image';

const Survey = () => {
  const { step, survey, isSubmitted } = useEmployeeSurveyStore(
    (state) => ({
      step: state.step,
      survey: state.survey,
      isSubmitted: state.isSubmitted,
      updateStep: state.updateStep,
    }),
    shallow
  );

  if (!survey) return null;

  if (isSubmitted) {
    return (
      <div>
        <h2 className="survey-submit-heading">Thanks for your feedback</h2>
        <Image width={360} height={360} src="/every-plate-illustration.png" />
      </div>
    );
  }

  const question = survey.questions[step];

  return (
    <div className="question">
      <Question
        key={`question-${question.id || step}`}
        stepCount={survey.questions.length - 1}
        step={step}
        question={question}
      />
    </div>
  );
};

export default Survey;
