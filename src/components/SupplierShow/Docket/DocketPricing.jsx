const DocketPricing = ({ hasOrder, orderTotals, isTeamOrder }) => {
  if (!hasOrder) return <p className="docket-empty-cart-heading">Your cart is empty</p>;

  if (isTeamOrder) {
    return (
      <div className="docket-pricing">
        <h4 className="order-total total-only">
          <span>Total</span>
          <span>{orderTotals.total}</span>
        </h4>
      </div>
    );
  }

  return (
    <div className="docket-pricing">
      <p>
        <span>Subtotal</span>
        <span>{orderTotals.subtotal}</span>
      </p>
      <p>
        <span>Delivery</span>
        <span>{orderTotals.delivery}</span>
      </p>
      <p>
        <span>GST</span>
        <span>{orderTotals.gst}</span>
      </p>
      {orderTotals.topup && (
        <p>
          <span>Topup</span>
          <span>{orderTotals.topup}</span>
        </p>
      )}
      <h4 className="order-total">
        <span>Total</span>
        <span>{orderTotals.total}</span>
      </h4>
    </div>
  );
};

export default DocketPricing;
