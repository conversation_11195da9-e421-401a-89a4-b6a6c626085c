import useCheckoutStore from 'store/useCheckoutStore';

const DeliveryInstruction = ({ isWoolworthsOrder, handleButtonClick }) => {
  const { deliveryInstruction } = useCheckoutStore((state) => ({
    deliveryInstruction: state.order.deliveryInstruction,
  }));
  return (
    <div className="modal-section">
      <div className="checkout-row icon icon-extra-large icon-truck-filled">
        <p>{deliveryInstruction || 'Add delivery instructions (optional)'}</p>
      </div>
      <button
        type="button"
        className={`checkout-button button white rounded ${isWoolworthsOrder ? 'disabled' : ''}`}
        onClick={() => !isWoolworthsOrder && handleButtonClick('instructions')}
      >
        {deliveryInstruction ? 'Change' : 'Add'}
      </button>
    </div>
  );
};

export default DeliveryInstruction;
