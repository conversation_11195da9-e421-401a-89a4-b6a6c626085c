import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import moment from 'moment';

import yordar from 'api/yordar';
import useDeliveryDateStore from 'store/useDeliveryDateStore';
import useDocketStore from 'store/useDocketStore';
import useModalStore from 'store/useSidebarStore';
import { MAXIMUM_MODAL_HEIGHT } from 'utils/constants';
import { calculateMenuItemTotal, getUniqueSupplierIds } from 'utils/order';
import { CHECKOUT_DATE_VALIDATION } from 'api/endpoints';

const MenuItemModalFooter = ({
  selectedMenuItems,
  extras,
  orderLines,
  setSelectedMenuItems,
  setExtras,
  modalRef,
  closeModal,
}) => {
  const { suppliers } = useSelector((state) => state);
  const { addOrderLineToCart, isWoolworthsOrder, isRecurrent, editingOrder, orders } = useDocketStore((state) => ({
    addOrderLineToCart: state.addOrderLineToCart,
    isWoolworthsOrder: state.order?.isWoolworthsOrder,
    isRecurrent: state.order?.isRecurrent,
    editingOrder: state.order?.status && state.order?.status !== 'draft',
    orders: state.orders,
  }));

  const setModalOpen = useModalStore((state) => state.setModalOpen);
  const {
    setOpenDeliveryModal,
    setDateValidated,
    deliveryAt,
    dateValidated,
    setModalIsClosable,
  } = useDeliveryDateStore((state) => ({
    setOpenDeliveryModal: state.setOpenDeliveryModal,
    setDateValidated: state.setDateValidated,
    deliveryAt: state.delivery_at,
    dateValidated: state.dateValidated,
    setModalIsClosable: state.setModalIsClosable,
  }));

  const totalPrice = calculateMenuItemTotal({
    menuItems: selectedMenuItems,
    extras,
  });
  const [loading, setLoading] = useState(false);
  async function submitMenuItem(e) {
    e.preventDefault();

    const waitForModal = (errors) =>
      new Promise((resolve, reject) => {
        setOpenDeliveryModal(true, errors);
        setModalIsClosable(false);
        // Listen for the modalResponse event
        const handleModalResponse = async (event) => {
          if (event.detail.success) {
            setLoading(true);
            addOrderLineToCart(orderLines);
            setLoading(false);
            closeModal(setSelectedMenuItems, setExtras, setModalOpen);
            resolve();
          } else {
            reject(new Error('Modal closed without valid input.'));
          }
        };

        window.addEventListener('modalResponse', handleModalResponse, { once: true });
      });

    try {
      if (!deliveryAt && !editingOrder && !isWoolworthsOrder && !isRecurrent) {
        await waitForModal();
      } else if (deliveryAt && !dateValidated && !editingOrder && !isWoolworthsOrder && !isRecurrent) {
        let isoDateString = '';
        const momentObj = moment(deliveryAt, 'h:mm a, D/M/YYYY');
        isoDateString = momentObj.toISOString();
        let supplierIDs = [...getUniqueSupplierIds(orders), suppliers.listing.id];
        supplierIDs = [...new Set(supplierIDs)];

        const { data: validationData } = await yordar.get(CHECKOUT_DATE_VALIDATION, {
          params: {
            order_delivery_at: isoDateString,
            supplier_ids: supplierIDs,
          },
          withCredentials: true,
        });

        if (!validationData.can_process_lead_time) {
          await waitForModal({ leadTime: validationData.formatted_lead_time });
        } else if (validationData.is_closed || validationData.outside_operating_hours) {
          await waitForModal({
            isOutsideOperatingHours: validationData.outside_operating_hours,
            isClosed: validationData.is_closed,
            closureSuppliers: validationData.supplier_closure_dates,
          });
        } else {
          setDateValidated(true);
          setLoading(true);
          addOrderLineToCart(orderLines);
          setLoading(false);
          closeModal(setSelectedMenuItems, setExtras, setModalOpen);
        }
      } else {
        setLoading(true);
        addOrderLineToCart(orderLines);
        setLoading(false);
        closeModal(setSelectedMenuItems, setExtras, setModalOpen);
      }
    } catch (error) {
      console.error('Error submitting menu item:', error);
    }
  }

  useEffect(() => {
    const listener = (e) => {
      if (e.code === 'Enter' || e.code === 'NumpadEnter') {
        submitMenuItem(e);
      }
    };
    document.addEventListener('keydown', listener);
    return () => {
      document.removeEventListener('keydown', listener);
    };
  }, [orderLines]);

  return (
    <div className={`menu-item-modal-add${modalRef?.current?.scrollHeight > MAXIMUM_MODAL_HEIGHT ? ' shadow' : ''}`}>
      <button
        className={`button ${totalPrice === 0 || loading ? 'disabled' : ''}`}
        type="button"
        onClick={submitMenuItem}
      >
        <span>Add to Cart -</span>
        {totalPrice > 0 ? (
          <span className="menu-item-modal-add-price">{` $${totalPrice}`}</span>
        ) : (
          <span className="menu-item-modal-add-price"> $0.00</span>
        )}
      </button>
    </div>
  );
};

export default MenuItemModalFooter;
