import React, { useState, useEffect, useRef } from 'react';
import { generateTimeSlots } from 'utils/dateTime';

const TimeSlotList = ({ onChange, value, clearErrors }) => {
  const [options, setOptions] = useState([]);
  const selectedRef = useRef(null);
  const listRef = useRef(null);
  const isInitialMount = useRef(true);

  useEffect(() => {
    if (isInitialMount.current) {
      setTimeSlots();
    }
  }, []);

  const setTimeSlots = () => {
    const timeSlots = generateTimeSlots();
    setOptions(timeSlots.slots);
    if (!value) {
      onChange({
        display: timeSlots.closestFutureSlot.time,
        hours: timeSlots.closestFutureSlot.hours,
        minutes: timeSlots.closestFutureSlot.minutes,
      });
    }
  };

  useEffect(() => {
    if (selectedRef.current && listRef.current && isInitialMount.current) {
      const list = listRef.current;
      const selected = selectedRef.current;
      list.scrollTop = selected.offsetTop - list.offsetTop;
      isInitialMount.current = false;
    }
  }, [value, options]);

  return (
    <div ref={listRef} className="time-slot-list">
      {options.map(({ display, hours, minutes }) => (
        <div
          key={display}
          ref={display === value ? selectedRef : null}
          onClick={() => {
            clearErrors();
            onChange({ display, hours, minutes });
          }}
          className={`time-slot-list__item ${value === display ? 'active' : ''}`}
        >
          {display}
        </div>
      ))}
    </div>
  );
};

export default TimeSlotList;
