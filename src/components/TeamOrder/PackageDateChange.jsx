import { useState } from 'react';
import { useRouter } from 'next/router';
import moment from 'moment';
import DatePicker from '@hassanmojab/react-modern-calendar-datepicker';
import '@hassanmojab/react-modern-calendar-datepicker/lib/DatePicker.css';

import yordar from 'api/yordar';
import { DYNAMIC_ATTENDEE_PACKAGE_ENDPOINT } from 'api/endpoints';

moment.locale('en-gb');

const PackageDateChange = ({ dates, setDates, setPackageOrders }) => {
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const router = useRouter();
  const { uuid } = router.query;

  const fetchPackageOrders = async (fromDate) => {
    try {
      const { data: packageOrdersResponse } = await yordar.get(DYNAMIC_ATTENDEE_PACKAGE_ENDPOINT(uuid), {
        params: {
          scoped_time: fromDate,
        },
        withCredentials: true,
      });
      setPackageOrders(packageOrdersResponse);
    } catch (error) {
      console.error('Error fetching package orders:', error);
    }
  };

  const handleDateArrow = (direction) => {
    const newFromDate = dates.from_date.add(direction, 'weeks').startOf('isoWeek');
    const newToDate = dates.to_date.add(direction, 'weeks').endOf('isoWeek');
    setDates({
      from_date: newFromDate,
      to_date: newToDate,
    });
    fetchPackageOrders(newFromDate);
  };

  const handleDateChange = (selectedDay) => {
    if (!selectedDay) return;

    const selectedDate = new Date(selectedDay.year, selectedDay.month - 1, selectedDay.day);
    const newFromDate = moment(selectedDate).startOf('isoWeek');
    const newToDate = moment(selectedDate).endOf('isoWeek');

    setDates({
      from_date: newFromDate,
      to_date: newToDate,
    });

    setIsCalendarOpen(false);
    fetchPackageOrders(newFromDate);
  };

  function getDisplayDate() {
    return `${dates.from_date.format('MMMM')} ${dates.from_date.format('DD')} - ${dates.to_date.format(
      'DD'
    )}, ${dates.from_date.format('YYYY')}`;
  }

  const renderCustomDay = (day) => {
    const date = new Date(day.year, day.month - 1, day.day);
    const isNotMonday = date.getDay() !== 1;
    const dayStyle = isNotMonday ? { color: '#ccc', backgroundColor: '#e9e9e9' } : {};
    return <div style={dayStyle}>{day.day}</div>;
  };

  return (
    <div className="team-attendee-date-change">
      <div className="button calendar-date-change" onClick={() => handleDateArrow(-1)} />
      <div className="admin-order-calendar-container" onClick={() => setIsCalendarOpen(!isCalendarOpen)}>
        <DatePicker
          value={null}
          onChange={handleDateChange}
          formatInputText={() => getDisplayDate()}
          renderCustomDay={renderCustomDay}
          shouldHighlightWeekends
        />
      </div>
      <div className="button calendar-date-change forward" onClick={() => handleDateArrow(1)} />
    </div>
  );
};

export default PackageDateChange;
