import { useState, useEffect } from 'react';
import {
  TextQuestion,
  RatingsQuestion,
  ToggleQuestion,
  SingleSelectQuestion,
  OverallRatingsQuestion,
  NameQuestion,
} from 'components/EmployeeSurvey';
import useEmployeeSurveyStore from 'store/useEmployeeSurveyStore';

const Question = ({ step, question }) => {
  const { answers, saveAnswer } = useEmployeeSurveyStore((state) => ({
    answers: state.answers,
    saveAnswer: state.saveAnswer,
  }));

  const getInitialAnswerValue = (inputType) => {
    switch (inputType) {
      case 'ratings':
        return undefined;
      case 'overall-ratings':
        return 50;
      default:
        return '';
    }
  };

  const initialAnswer = {
    survey_question_id: question.id,
    value: getInitialAnswerValue(question.input_type),
  };

  const [answer, setAnswer] = useState(answers[step] || initialAnswer);

  useEffect(() => {
    saveAnswer({
      step,
      answer,
    });
  }, [answer.value]);

  return (
    <>
      <h2 className="question-title">
        {step + 1}. {question.label}
        {!!question.optional && <span>(optional)</span>}
      </h2>
      {question.input_type === 'text' && <TextQuestion question={question} answer={answer} setAnswer={setAnswer} />}
      {question.input_type === 'ratings' && (
        <RatingsQuestion question={question} answer={answer} setAnswer={setAnswer} />
      )}
      {question.input_type === 'toggle' && <ToggleQuestion question={question} answer={answer} setAnswer={setAnswer} />}
      {question.input_type === 'single-select' && (
        <SingleSelectQuestion question={question} answer={answer} setAnswer={setAnswer} />
      )}
      {question.input_type === 'overall-ratings' && <OverallRatingsQuestion setAnswer={setAnswer} answer={answer} />}
      {question.input_type === 'name' && <NameQuestion />}
    </>
  );
};

export default Question;
