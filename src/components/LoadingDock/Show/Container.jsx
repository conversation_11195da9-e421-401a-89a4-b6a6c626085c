import { FilePreview } from 'components/LoadingDock';

const Container = ({ order, loadingDock }) => {
  return (
    <div className="loading-dock-request-container">
      <h3 style={{ marginBottom: '20px' }}>Loading Dock Code for - #{order.id}</h3>

      <p>
        The Loading Dock Code for the order #{order.id} to be delivered on {order.delivery_at} is:
      </p>

      {!!loadingDock.code && (
        <h3>
          Code: {loadingDock.code}
        </h3>
      )}
      
      {!!loadingDock.file_url && (
        <p style={{ marginTop: '1rem' }}>
          <strong>Loading Dock File</strong>
          <br />
          <FilePreview fileURL={loadingDock.file_url} name={`loading-dock-${order.id}`} />
        </p>
      )}
    </div>
  );
}  

export default Container;