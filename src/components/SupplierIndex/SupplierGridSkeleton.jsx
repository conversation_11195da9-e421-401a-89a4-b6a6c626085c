import keyGenerator from 'utils/keyGenerator';

const SupplierGridSkeleton = () => (
  <section className="listing-grid">
    <section className="listing-grid-items loading">
      {new Array(16).fill().map(() => (
        <div key={keyGenerator('listing-grid-item')} className="listing-grid-item">
          <div className="listing-grid-card" />
          <div className="listing-grid-item-body">
            <div className="listing-grid-item-meta" />
            <div className="listing-grid-item-about" />
          </div>
        </div>
      ))}
    </section>
  </section>
);

export default SupplierGridSkeleton;
