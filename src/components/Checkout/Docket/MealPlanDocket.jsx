import { useEffect } from 'react';
import moment from 'moment';

import useCheckoutStore from 'store/useCheckoutStore';
import { getDateConfig, parseDate } from 'utils/dateTime';

const MealPlanDocket = ({ checkoutPanel, setCheckoutPanel, isEditPage }) => {
  let localStorageDate;
  let localStorageTime;

  const { order, setDate } = useCheckoutStore((state) => ({
    order: state.order,
    setDate: state.setDate,
  }));

  const { mealPlan, deliveryAt } = order;

  if (typeof window !== 'undefined') {
    localStorageDate = localStorage.getItem('deliveryDate');
    localStorageTime = localStorage.getItem('deliveryTime');
  }

  // set order date from localStorage
  useEffect(async () => {
    if (localStorageDate && localStorageTime && !isEditPage) {
      const dateTimeString = `${localStorageTime} ${localStorageDate}`;
      const dateTime = moment(dateTimeString, 'h:mm a D/M/YYYY');
      const dateConfig = getDateConfig(dateTime.toDate());
      setDate({
        deliveryDate: parseDate(localStorageDate),
        deliveryTime: localStorageTime,
        deliveryAt: dateConfig.orderFormatted,
        deliveryAtDisplayTime: dateConfig.displayFormatted.time,
        deliveryAtDisplayDate: dateConfig.displayFormatted.date,
      });
    }
  }, []);

  if (!deliveryAt) return null;

  const getFormattedDeliveryTime = () => {
    // this mess is for the different formats of dates that come through

    if (moment(deliveryAt, 'YYYY-MM-DD h:mm a', true).isValid()) {
      return moment(deliveryAt, 'YYYY-MM-DD h:mm a').format('h:mm a, ddd DD MMM YYYY');
    }

    if (moment(deliveryAt, 'h:mm a D/M/YYYY', true).isValid()) {
      return moment(deliveryAt, 'h:mm a D/M/YYYY').format('h:mm a, ddd DD MMM YYYY');
    }

    if (moment(deliveryAt, 'h:mm a, DD/MM/YYYY', true).isValid()) {
      return moment(deliveryAt, 'h:mm a, DD/MM/YYYY').format('h:mm a, ddd DD MMM YYYY');
    }

    // Fallback - try to parse the date without a specific format
    return moment(deliveryAt).format('h:mm a, ddd DD MMM YYYY');
  };

  return (
    <div className="meal-plan-docket">
      <h3>Meal Plan</h3>
      <div>
        <p>
          <span>{mealPlan?.name}</span> @ {getFormattedDeliveryTime()}
        </p>
        {checkoutPanel === 'details' && (
          <a className="edit-details" onClick={() => setCheckoutPanel('order')}>
            Order Summary
          </a>
        )}
      </div>
    </div>
  );
};

export default MealPlanDocket;
