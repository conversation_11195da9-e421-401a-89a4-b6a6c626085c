import { useEffect, useState } from 'react';

import useDocketStore from 'store/useDocketStore';
import useModalStore from 'store/useSidebarStore';
import { MAXIMUM_MODAL_HEIGHT } from 'utils/constants';
import { calculateMenuItemTotal } from 'utils/order';

const MenuItemModalFooter = ({
  selectedMenuItems,
  extras,
  orderLines,
  setSelectedMenuItems,
  setExtras,
  modalRef,
  closeModal,
}) => {
  const { addOrderLineToCart, teamAttendee, budget, orderTotal, teamAdmin } = useDocketStore((state) => ({
    addOrderLineToCart: state.addOrderLineToCart,
    teamAttendee: state.teamAttendee,
    budget: state.order?.team_order_budget,
    orderTotal: state.orders[state.activeOrderID]?.totals.total_number,
    teamAdmin: state.teamAttendee?.is_team_admin,
  }));

  const setModalOpen = useModalStore((state) => state.setModalOpen);

  const totalPrice = calculateMenuItemTotal({
    menuItems: selectedMenuItems,
    extras,
  });
  const [loading, setLoading] = useState(false);
  const overBudget = Number(totalPrice) > Number(budget) || Number(totalPrice) + Number(orderTotal) > Number(budget);

  async function submitMenuItem(e) {
    e.preventDefault();
    if (!teamAdmin && overBudget) {
      alert('over budget');
      return;
    }
    try {
      setLoading(true);
      addOrderLineToCart(orderLines, teamAttendee.id);
      setLoading(false);
      closeModal(setSelectedMenuItems, setExtras, setModalOpen);
    } catch (error) {
      console.error('Error submitting menu item:', error);
    }
  }

  useEffect(() => {
    const listener = (e) => {
      if (e.code === 'Enter' || e.code === 'NumpadEnter') {
        if (overBudget && !teamAdmin) return;
        submitMenuItem(e);
      }
    };
    document.addEventListener('keydown', listener);
    return () => {
      document.removeEventListener('keydown', listener);
    };
  }, [orderLines]);

  return (
    <div className={`menu-item-modal-add${modalRef?.current?.scrollHeight > MAXIMUM_MODAL_HEIGHT ? ' shadow' : ''}`}>
      <button
        className={`button ${totalPrice === 0 || loading || (overBudget && !teamAdmin) ? 'disabled' : ''} ${overBudget && teamAdmin ? 'over-budget' : ''
          }`}
        type="button"
        onClick={overBudget && !teamAdmin ? null : submitMenuItem}
      >
        <span>{overBudget ? 'Over Budget -' : 'Add to Cart -'}</span>
        {totalPrice > 0 ? (
          <span className="menu-item-modal-add-price">{` $${totalPrice}`}</span>
        ) : (
          <span className="menu-item-modal-add-price"> $0.00</span>
        )}
      </button>
    </div>
  );
};

export default MenuItemModalFooter;
