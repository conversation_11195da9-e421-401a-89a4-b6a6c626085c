import { useState } from 'react';
import { useRouter } from 'next/router';
import { DYNAMIC_ORDER_MINIMUM_CHECK_ENDPOINT } from 'api/endpoints';
import { CheckoutModal, OrderMinimumError, QuoteButtons } from 'components/Checkout';
import useCheckoutStore from 'store/useCheckoutStore';
import yordar from 'api/yordar';

const OrderValidateButton = ({ checkoutPanel, moveToDetails }) => {
  const {
    order,
    setOrderValidated,
    validateWoolworthsOrder,
    submitOrder,
    inProgress,
    setInProgress,
  } = useCheckoutStore((state) => ({
    order: state.order,
    setOrderValidated: state.setOrderValidated,
    validateWoolworthsOrder: state.validateWoolworthsOrder,
    submitOrder: state.submitOrder,
    inProgress: state.inProgress,
    setInProgress: state.setInProgress,
  }));
  const [modalOpen, setModalOpen] = useState(false);
  const [modalInfo, setModalInfo] = useState(null);
  const [isChecking, setIsChecking] = useState(false);
  const hasErrors = order.checkoutErrors[checkoutPanel]?.length;
  const router = useRouter();

  const hasMealPlan = !!order?.mealPlan?.uuid;

  let buttonText = '';
  const buttonInProgress = ['non-quote', 'one-off'].includes(inProgress);
  if (hasErrors) {
    buttonText = buttonInProgress ? 'Validating...' : 'Validate';
  } else if (hasMealPlan) {
    buttonText = buttonInProgress ? 'Placing Order...' : 'Place Order';
  } else {
    buttonText = buttonInProgress ? 'Confirming...' : 'Confirm';
  }

  const submitMealPlanOrder = async ({ mode }) => {
    setInProgress(mode);
    try {
      await submitOrder({ mode });
      router.push('checkout/success');
    } catch (err) {
      setInProgress(null);
    }
  };

  const checkOrderMinimums = async ({ andSubmit, mode }) => {
    andSubmit ? setInProgress(mode) : setIsChecking(true);
    try {
      const { data: response } = await yordar.get(DYNAMIC_ORDER_MINIMUM_CHECK_ENDPOINT(order.id));
      if (response.is_under) {
        setModalOpen(true);
        setModalInfo(
          <OrderMinimumError
            order={order}
            response={response}
            setModalOpen={setModalOpen}
            moveToDetails={moveToDetails}
            submitMealPlanOrder={andSubmit && hasMealPlan ? (() => submitMealPlanOrder({ mode })) : null}
          />
        );
        andSubmit ? setInProgress(null) : setIsChecking(false);
      } else if (andSubmit && hasMealPlan) {
        submitMealPlanOrder({ mode });
      } else {
        setOrderValidated(true);
        moveToDetails();
        andSubmit ? setInProgress(null) : setIsChecking(false);
      }
    } catch (err) {
      alert(err);
      andSubmit ? setInProgress(null) : setIsChecking(false);
    }
  };

  async function handleValidateOrder({ andSubmit, mode }) {
    if (inProgress || isChecking) return;

    if (hasErrors) {
      await validateWoolworthsOrder(false);
    } else {
      await checkOrderMinimums({ andSubmit, mode });
    }
  }

  return (
    <>
      <button className="button checkout-button confirm primary" onClick={() => handleValidateOrder({ andSubmit: true, mode: 'non-quote' })}>
        {buttonText}
      </button>
      {hasMealPlan && (
        <>
          <QuoteButtons handleSubmit={handleValidateOrder} />
          <button className="button checkout-button confirm tertiary" onClick={() => handleValidateOrder({ andSubmit: false, mode: 'non-quote' })}>
            {isChecking ? 'Checking Order...' : 'Edit Details'}
          </button>
        </>
      )}
      <CheckoutModal open={modalOpen} setModalOpen={setModalOpen} dimensionOptions={{ width: '500px' }}>
        {modalInfo}
      </CheckoutModal>
    </>
  );
};

export default OrderValidateButton;
