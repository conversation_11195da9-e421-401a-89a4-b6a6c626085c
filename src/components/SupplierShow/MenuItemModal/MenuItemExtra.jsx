import classNames from 'classnames';

const MenuItemExtra = ({ extraDetails, maxLimit }) => {
  const { extra, extras, extraCount } = extraDetails;
  const extraAlreadySelected = extras.some((each) => each.id === extra.id);
  const extrasDisabled = extraCount === maxLimit && !extraAlreadySelected;
  const menuExtraClass = classNames('menu-item-modal-extra-name', 'button', 'black', 'small', 'contrast', {
    outline: !extraAlreadySelected,
    disabled: extrasDisabled,
  });
  return (
    <>
      <span
        className={menuExtraClass}
        onClick={() => handleMenuExtraClick(extraDetails, extraAlreadySelected, extrasDisabled)}
        role="presentation"
      >
        {extra.name}
        {Number(extra.price) > 0 && <span className="menu-item-modal-extra-price">+${extra.price}</span>}
      </span>
    </>
  );
};

const handleMenuExtraClick = (extraDetails, extraAlreadySelected, extrasDisabled) => {
  const { extra, extras, extraCount, setExtraCount, setExtras } = extraDetails;
  let updatedExtras;
  if (extraAlreadySelected) {
    updatedExtras = extras.filter((each) => each.id !== extra.id);
    setExtraCount(extraCount - 1);
  } else {
    updatedExtras = [...extras, extra];
    setExtraCount(extraCount + 1);
  }
  if (extrasDisabled) return;
  setExtras(updatedExtras);
};

export default MenuItemExtra;
