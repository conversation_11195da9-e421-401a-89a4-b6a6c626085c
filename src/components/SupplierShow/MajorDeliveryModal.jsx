import { useContext, useEffect, useRef, useState } from 'react';
import { Modal } from 'react-responsive-modal';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

import { GoogleAddressSearch, ClearCartModal } from 'components/Common';
import { HostContext } from 'context/host';
import useDocketStore from 'store/useDocketStore';
import { orderHasOtherSuppliers, sanitizedDeliveryAddress } from 'utils/order';
import { calculateDate } from 'utils/dateTime';

const MajorDeliveryModal = ({ setShowModal, previousDeliveryInstructions = '', showModal }) => {
  const { order, orders, setWoolworthsOrderFromModal } = useDocketStore((state) => ({
    order: state.order,
    orders: state.orders,
    setWoolworthsOrderFromModal: state.setWoolworthsOrderFromModal,
  }));

  const { appURL } = useContext(HostContext);
  const [deliveryLevel, setDeliveryLevel] = useState('');
  const [deliveryAddress, setDeliveryAddress] = useState('');
  const [deliveryInstructions, setDeliveryInstructions] = useState(previousDeliveryInstructions);
  const [deliveryDate, setDeliveryDate] = useState(null);

  const [showErrorMsg, setShowErrorMsg] = useState(false);
  const [loading, setLoading] = useState(false);

  const orderContainsOtherSuppliers = orderHasOtherSuppliers(orders);
  const MIN_TEXTAREA_HEIGHT = 40;
  const textareaRef = useRef(null);

  useEffect(() => {
    setDeliveryAddress(sanitizedDeliveryAddress(order));
    setDeliveryLevel(order.delivery_address_level);
    if (order.delivery_instruction) {
      setDeliveryInstructions(order.delivery_instruction);
    }
  }, [order.delivery_suburb]);

  useEffect(() => {
    if (textareaRef?.current) {
      // Reset height - important to shrink on delete
      textareaRef.current.style.height = 'inherit';
      // Set height
      textareaRef.current.style.height = `${Math.max(textareaRef.current.scrollHeight, MIN_TEXTAREA_HEIGHT)}px`;
    }
  }, [deliveryInstructions]);

  if (orderContainsOtherSuppliers) {
    let geoLocation;
    if (typeof window !== 'undefined') {
      geoLocation = JSON.parse(localStorage.getItem('geo-location'));
    }
    const backNavigationOptions = {
      path: geoLocation ? `/search/office-catering/${geoLocation.state}/${geoLocation.suburb}` : `${appURL}/c_profile`,
      value: 'Back To Suppliers',
    };
    return (
      <ClearCartModal
        message="Sorry, your cart contains other supplier's items so we cannot process a Woolworths order"
        backNavigationOptions={backNavigationOptions}
      />
    );
  }

  function handleKeyDown(e) {
    e.target.style.height = 'inherit';
    e.target.style.height = `${e.target.scrollHeight}px`;
  }

  function handleChange(datetime) {
    const formattedDate = new Intl.DateTimeFormat('sv-SE', { dateStyle: 'short' }).format(datetime).replace(/-/g, '/');
    const formattedTime = new Intl.DateTimeFormat('en', { timeStyle: 'short' }).format(datetime);
    setDeliveryDate(`${formattedDate} ${formattedTime}`);
  }
  async function submitDeliveryModal() {
    const deliveryDetails = { deliveryAddress, deliveryDate, deliveryInstructions, deliveryLevel };
    try {
      setLoading(true);
      await setWoolworthsOrderFromModal(deliveryDetails);
      setShowModal(false);
      setLoading(false);
    } catch {
      setLoading(false);
      setShowErrorMsg(true);
    }
  }

  return (
    <Modal
      open={showModal}
      center
      styles={{
        modal: { maxWidth: '500px', padding: 0, borderRadius: '10px' },
        closeButton: { cursor: 'pointer', marginLeft: '6px' },
      }}
      showCloseIcon={false}
      onClose={() => {}}
    >
      <div className="menu-modal">
        <h3 className="menu-modal__heading menu-modal__heading-woolworths">Woolworths Connection</h3>
        {showErrorMsg && (
          <p className="error">
            Something went wrong communicating with Woolworths. Please check your details and try again.
          </p>
        )}
        <form
          onSubmit={(e) => {
            e.preventDefault();
            submitDeliveryModal();
          }}
        >
          <label>
            Delivery Level Number (optional)
            <input
              type="number"
              name="Delivery Level"
              value={deliveryLevel}
              onChange={(e) => setDeliveryLevel(e.target.value)}
            />
          </label>
          <label>
            Street Address
            <GoogleAddressSearch
              placeholder="Delivery Address for your Woolworths order"
              deliveryAddress={deliveryAddress}
              setDeliveryAddress={setDeliveryAddress}
            />
          </label>
          <label>
            Delivery Instructions
            <textarea
              type="text"
              name="Delivery Instructions"
              value={deliveryInstructions}
              onChange={(e) => setDeliveryInstructions(e.target.value)}
              onKeyDown={handleKeyDown}
              ref={textareaRef}
              style={{
                minHeight: MIN_TEXTAREA_HEIGHT,
                resize: 'none',
              }}
              required
            />
          </label>
          <label>
            Date / Time
            <DatePicker
              selected={deliveryDate ? new Date(deliveryDate) : ''}
              onChange={handleChange}
              showTimeSelect
              timeIntervals={15}
              minDate={calculateDate(1)}
              maxDate={calculateDate(6)}
              name="delivery_at"
              dateFormat="yyyy-MM-dd h:mm aa"
              autoComplete="off"
              required
            />
          </label>
          <input
            type="submit"
            value={loading ? 'Connecting...' : 'Connect to Woolworths'}
            className="woolworths-form-submit"
          />
        </form>
      </div>
    </Modal>
  );
};

export default MajorDeliveryModal;
