import { useState } from 'react';
import { DYNAMIC_ATTENDEE_PACKAGE_ENDPOINT } from 'api/endpoints';
import Image from 'next/image';

import { FullHeightPage, Layout } from 'components/Common';
import { PackageDateChange, TeamAttendeePackage } from 'components/TeamOrder';
import yordar from 'api/yordar';
import moment from 'moment';

moment.locale('en-gb');

const YordarLogo = require('images/logo-dark.svg');

const AttendeePackage = ({ attendeePackage }) => {
  const initialDates = {
    from_date: attendeePackage.order_week_scope
      ? moment(attendeePackage.order_week_scope).startOf('isoWeek')
      : moment().startOf('isoWeek'),
    to_date: attendeePackage.order_week_scope
      ? moment(attendeePackage.order_week_scope).endOf('isoWeek')
      : moment().endOf('isoWeek'),
  };

  const [packageOrders, setPackageOrders] = useState(attendeePackage);
  const [dates, setDates] = useState(initialDates);

  return (
    <Layout hideHeaderAndFooter>
      <FullHeightPage />
      <div className="team-order-package-page">
        <div className="show-package team-attendee">
          <div>
            <div className="show-package-banner">
              <Image src={YordarLogo} alt="Yordar Logo" width={160} height={60} />
              <p>
                Hi <b>{packageOrders?.attendee?.name}</b>, please select your upcoming meals
              </p>
            </div>
            <PackageDateChange dates={dates} setDates={setDates} setPackageOrders={setPackageOrders} />
            <div />
            <div>
              <div className="team-attendee-week">
                {!!packageOrders?.orders?.length &&
                  packageOrders.orders.map((packageOrder) => (
                    <TeamAttendeePackage key={packageOrder.id} packageOrder={packageOrder} />
                  ))}
              </div>
              {!packageOrders?.orders?.length && (
                <p style={{ fontWeight: 'bold', fontSize: '20px', marginTop: '60px', textAlign: 'center' }}>
                  No orders for this week
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export const getServerSideProps = async ({ params: { uuid } }) => {
  try {
    const { data: attendeePackage } = await yordar.get(DYNAMIC_ATTENDEE_PACKAGE_ENDPOINT(uuid));

    return {
      props: {
        attendeePackage,
      },
    };
  } catch {
    return {
      notFound: true,
    };
  }
};

export default AttendeePackage;
