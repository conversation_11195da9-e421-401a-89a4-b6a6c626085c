.menu-modal {
  padding: 20px;
  label {
    color: #000;
    font-weight: bold;
    font-family: $heading-font;
  }
  input, textarea {
    background: white;
    padding: 10px;
    box-shadow: none;
    margin: 6px 0 14px;
    font-family: $body-font;
    &:focus {
      border-color: black;
    }
  }
  textarea {
    min-height: 60px;
    height: auto;
    padding: 4px 10px;
    overflow: hidden;
  }
  &__heading {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    
    &-woolworths {
      color: #000;
    }
  }
  .modal-button {
    padding: 6px 10px;
    background: black;
    border-radius: 3px;
    color: white;
    margin-right: 4px;
    cursor: pointer;
    &.primary {
      background: $primary;
    }
  }
  .woolworths-form-submit {
    background: white;
    color: white;
    margin-top: 10px;
    background: black;
    cursor: pointer;
    &:hover {
      background: lighten(black, 20%);
    }
  }
}

.react-datepicker {
  font-family: $body-font !important;
  box-shadow: 0px 0px 6px 3px rgba(40, 40, 40, 0.2);
  border: none !important;
  .react-datepicker__day-names {
    text-transform: uppercase;
  }
  .react-datepicker__header {
    background: white;
  }
  .react-datepicker__month-container .react-datepicker__header {
    border-bottom: none;
  }
  .react-datepicker__day--selected, .react-datepicker__time-list-item--selected {
    background: $black !important;
    border-radius: 0 !important;
  }
  .react-datepicker__navigation-icon--previous, .react-datepicker__navigation-icon--next {
    &:before {
      border-color: $black;
      border-width: 2px 2px 0 0;  
    }
  }
  .react-datepicker__day--keyboard-selected {
    background-color: inherit;
    color: inherit;
  }
  .react-datepicker__day--outside-month {
    color: #ccc;
  }
}

.google-address-search {
  position: relative;
  display: flex;
  font-family: $body-font;
  .autocomplete-dropdown-container {
    position: absolute;
    top: 40px;
    left: 0;
    z-index: 9;
    background: #ffffff;
    width: 100%;
    text-align: left;
    border: 1px solid rgba($black, 0.2);
    border-radius: 0 0 5px 5px;
    box-shadow: rgba($black, 0.1) 0 2px 3px;
  }
  .autocomplete-dropdown-item {
    display: grid;
    grid-template-columns: 25px auto;
    padding: 10px 15px;
    border-bottom: rgba($black, 0.2) 1px solid;
    text-transform: uppercase;
    color: rgba($textcolor, 0.8);
    font-size: 13px;
    font-weight: bold;
    line-height: 1.6em;
    cursor: pointer;
    &:hover {
      background: lighten($off-white, 3%);
      &::before {
        background: url('../../../images/icons/marker-highlight.svg') no-repeat center;
      }
    }
    &:last-of-type {
      border-bottom: none;
    }
    &:before {
      content: '';
      display: block;
      width: 16px;
      height: 16px;
      background: url('../../../images/icons/marker.svg') no-repeat center;
      background-size: contain;
      opacity: 1;
      margin: 4px 0 0;
    }
  }
}

.google-no-results {
  padding: 20px;
  color: black;
  font-weight: normal;
}

.error {
  color: #fe1112
}