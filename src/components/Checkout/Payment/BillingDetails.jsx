import { useContext } from 'react';
import useCheckoutStore from 'store/useCheckoutStore';

import yordar from 'api/yordar';
import { SUBURBS_ENDPOINT } from 'api/endpoints';
import AsyncSelect from 'react-select/async';
import { HostContext } from 'context/host';
import { PurchaseOrderDetails } from 'components/Checkout';

const BillingDetails = () => {
  const { locality: countryCode } = useContext(HostContext);
  const {
    checkout: {
      billing_details: billingDetails,
      requires_purchase_order: requiresPO,
      required_department_identity_format: requiredCostCentreIDFormat,
      requires_billing_details: requiresBillingDetails,
    },
    setBillingDetail,
    validations,
  } = useCheckoutStore();

  const optionalBillingDetails = requiresPO && !!requiredCostCentreIDFormat && requiresBillingDetails;

  const loadSuburbOptions = async (inputValue, callback) => {
    if (!inputValue || inputValue.length <= 3) return [];

    const { data: suburbResponse } = await yordar.get(SUBURBS_ENDPOINT, {
      params: { term: inputValue, country_code: countryCode },
    });
    callback(suburbResponse.map((suburb) => ({ label: suburb.label, value: suburb.id })));
  };

  const handleBillingChange = (e) => {
    setBillingDetail(e.target.name, e.target.value);
  };

  const handleSuburbSelection = (selectedOption) => {
    setBillingDetail('suburb_id', selectedOption ? selectedOption.value : null);
  };

  return (
    <>
      <div className="between-flex heading-block">
        <h3 className="section-heading" style={{ marginBottom: 0 }}>
          Billing Details{optionalBillingDetails ? ' (optional)' : ''}
        </h3>
      </div>
      <div className="invoice-details">
        {requiresBillingDetails && (
          <>
            <div className="checkout-detail">
              <p>Name</p>
              <input
                type="text"
                className={`checkout-input company${validations.billing_name === false ? ' invalid--input' : ''}`}
                placeholder="Billing Company Name"
                name="name"
                value={billingDetails.name}
                onChange={handleBillingChange}
              />
            </div>
            <div className="checkout-detail">
              <p>Phone</p>
              <input
                type="tel"
                className={`checkout-input phone${validations.billing_phone === false ? ' invalid--input' : ''}`}
                placeholder="Billing Phone"
                name="phone"
                value={billingDetails.phone}
                onChange={handleBillingChange}
              />
            </div>
            <div className="checkout-detail">
              <p>Street Address</p>
              <input
                type="text"
                className={`checkout-input marker${validations.billing_address === false ? ' invalid--input' : ''}`}
                placeholder="Street Address"
                name="address"
                value={billingDetails.address}
                onChange={handleBillingChange}
              />
            </div>
            <div className="checkout-detail">
              <p>Suburb</p>
              <AsyncSelect
                cacheOptions
                isClearable
                className={`marker${validations.billing_suburb_id === false ? ' invalid--input' : ''}`}
                loadOptions={loadSuburbOptions}
                placeholder="Billing Suburb"
                onChange={handleSuburbSelection}
              />
            </div>
            <div className="checkout-detail" style={{ gridColumn: 'span 2' }}>
              <p>Billing Email(s)</p>
              <input
                type="text"
                className={`email${validations.billing_email === false ? ' invalid--input' : ''}`}
                placeholder="If you have multiple billing email addresses separate them by semicolon (;)"
                name="email"
                value={billingDetails.email}
                onChange={handleBillingChange}
              />
            </div>
          </>
        )}
        <PurchaseOrderDetails />
      </div>
    </>
  );
};

export default BillingDetails;
