import useCustomOrderStore from 'store/useCustomOrderStore';
import useCheckoutStore from 'store/useCheckoutStore';
import { OrderLine, SupplierMenuModal } from 'components/Checkout';
import { useState } from 'react';

const SupplierSection = ({ location, supplier }) => {
  const { newOrderLine } = useCustomOrderStore((state) => ({
    newOrderLine: state.newOrderLine,
  }));

  const { activeOrderID, removeSupplier } = useCheckoutStore((state) => ({
    activeOrderID: state.activeOrderID,
    removeSupplier: state.removeSupplier,
  }));

  const [modalOpen, setModalOpen] = useState(false);

  const openSupplierMenu = () => {
    setModalOpen(true);
  };

  return (
    <div className="supplier-section" style={{ marginBottom: '16px' }}>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '12px' }}>
        <h6 className="supplier-name" style={{ margin: 0, fontSize: '16px', fontWeight: '600', color: '#555' }}>
          {supplier.name}
        </h6>
        <div style={{ display: 'flex', gap: '8px' }}>
          <button
            type="button"
            onClick={() => newOrderLine({ location, supplier })}
            className="button"
            style={{ fontSize: '12px', padding: '0.3em 1.2em' }}
          >
            + New Item
          </button>
          <button
            type="button"
            onClick={() => openSupplierMenu(supplier.id)}
            className="button"
            style={{ fontSize: '12px', padding: '0.3em 1.2em' }}
          >
            + From Menu
          </button>
          {!Object.keys(supplier.order_lines).length && (
            <button
              type="button"
              onClick={() =>
                removeSupplier({
                  orderID: activeOrderID,
                  locationID: location.id,
                  supplierID: supplier.id,
                })
              }
              style={{
                padding: '6px 8px',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                fontSize: '12px',
                cursor: 'pointer',
              }}
            >
              🗑️
            </button>
          )}
        </div>
      </div>

      <SupplierMenuModal
        supplier={supplier}
        location={location}
        modalOpen={modalOpen}
        closeModal={() => setModalOpen(false)}
      />

      {Object.keys(supplier.order_lines).map((orderLineID) => (
        <div key={orderLineID.id} style={{ marginBottom: '8px' }}>
          <OrderLine orderLine={supplier.order_lines[orderLineID]} customOrderConfig={{ location, supplier }} />
        </div>
      ))}
    </div>
  );
};

export default SupplierSection;
