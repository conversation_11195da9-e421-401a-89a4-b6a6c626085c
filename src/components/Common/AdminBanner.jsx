import { HostContext } from 'context/host';
import { useContext } from 'react';

const AdminBanner = ({ name }) => {
  const { appURL } = useContext(HostContext);
  return (
    <p className="admin-banner">
      You're logged in as {name}.{' '}
      <a href={`${appURL}/sign_out_as`} className="admin-logout">
        Click here when you're finished
      </a>
    </p>
  );
};

export default AdminBanner;
