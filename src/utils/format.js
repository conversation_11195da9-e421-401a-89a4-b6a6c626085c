import moment from 'moment';

export const formatCategory = (category) => {
  const formattedCategory = category.humanize();
  if (formattedCategory === 'Snacks Pantry') return 'Snacks & Pantry';
  return formattedCategory;
};

export const formatCategoryForApi = (category) => {
  const categories = {
    'office-snacks': 'kitchen-supplies',
    'office-catering': 'catering-services',
    'work-from-home': 'work-from-home',
  };
  return categories[category] || '404';
};

export const getSupplierIndexParams = (mealUUID, deliveryDate, deliveryTime) => {
  const params = new URLSearchParams();

  if (mealUUID) {
    params.append('mealUUID', mealUUID);
  }

  if (deliveryDate && deliveryTime) {
    const isoDate = deliveryDate.split('/').reverse().join('-');
    const isoTime = moment(deliveryTime, ['h:mm A']).format('HH:mm');
    params.append('for_date', `${isoDate}T${isoTime}`);
  }

  return params.toString() ? `?${params.toString()}` : '';
};
