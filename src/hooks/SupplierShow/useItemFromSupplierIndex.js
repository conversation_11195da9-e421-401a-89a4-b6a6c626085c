import { useEffect } from 'react';

import useModalStore from 'store/useModalStore';
import useMenuItemStore from 'store/useMenuItemStore';

export default function useItemFromSupplierIndex(itemFromSearch, menuSections, isWoolworths) {
  const setModalOpen = useModalStore((state) => state.setModalOpen);
  const setMenuItem = useMenuItemStore((state) => state.setMenuItem);

  useEffect(() => {
    if (itemFromSearch && !isWoolworths) {
      // First use the passed in id to find the section, then find the menu item in that section
      // Can probably be optimised to save 2 loops
      // Once item is found set activeMenuItem for modal and set modal to open
      const matchedSection = menuSections.find((section) =>
        section.menu_items.find((item) => item.id === Number(itemFromSearch))
      );

      const matchedItem = matchedSection?.menu_items.find((item) => item.id === Number(itemFromSearch));
      setMenuItem(matchedItem);
      setModalOpen(true);
    }
  }, [itemFromSearch]);
}
