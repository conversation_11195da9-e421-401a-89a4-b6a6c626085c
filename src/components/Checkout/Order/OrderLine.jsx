import { useState, useRef, useEffect } from 'react';

import { delay } from 'utils/delay';

import { OrderLineImage, OrderLineQuantity } from 'components/Checkout';
import useCheckoutStore from 'store/useCheckoutStore';
import useCustomOrderStore from 'store/useCustomOrderStore';

const OrderLine = ({ orderLine, customOrderConfig = false }) => {
  const isMounted = useRef(false);

  const { updateOrderLine, removeOrderLine, orders, activeOrderID, orderSuppliers } = useCheckoutStore((state) => ({
    updateOrderLine: state.updateOrderLine,
    removeOrderLine: state.removeOrderLine,
    orders: state.orders,
    activeOrderID: state.activeOrderID,
    orderSuppliers: state.orderSuppliers,
  }));
  const { editOrderLine, cloneOrderLine } = useCustomOrderStore((state) => ({
    editOrderLine: state.editOrderLine,
    cloneOrderLine: state.cloneOrderLine,
  }));
  const [localOrderLine, setLocalOrderLine] = useState(orderLine);
  const isBlankOrSameQuantity = orderLine.quantity === localOrderLine.quantity || localOrderLine.quantity === '';
  const isZeroQuantity = Number(localOrderLine.quantity) === 0;

  const handleUpdate = (event) => {
    setLocalOrderLine((state) => ({ ...state, [event.target.name]: event.target.value }));
  };

  const onBlur = async () => {
    updateOrderLine(localOrderLine);
  };

  // Update Zustand Order Line if the quantity is not 0 or the same as local state
  useEffect(async () => {
    if (isMounted.current) {
      if (isBlankOrSameQuantity) return;
      if (isZeroQuantity) {
        try {
          await removeOrderLine(orderLine);
        } catch (err) {
          if (localOrderLine.prevQuantity) {
            setLocalOrderLine((state) => ({ ...state, quantity: state.prevQuantity }));
          }
        }
        return;
      }
      return delay(function () {
        updateOrderLine(localOrderLine);
      }, 300);
    }
    isMounted.current = true;
  }, [localOrderLine.quantity]);

  const handleRemove = async () => {
    try {
      await removeOrderLine(orderLine);
    } catch (err) {
      // do nothing
    }
  };

  const handleEdit = () => {
    const { location, supplier } = customOrderConfig;
    editOrderLine({ orderLine, location, supplier });
  };

  const handleClone = () => {
    const { location, supplier } = customOrderConfig;
    cloneOrderLine({ orderLine, location, supplier });
  };

  // Get pricing calculation values
  const order = orders[activeOrderID];
  const markup = order?.commission || 0;
  const currentSupplier = customOrderConfig?.supplier;
  const supplierCommission = currentSupplier?.commission ? parseFloat(currentSupplier.commission) : 0;

  // Handle baseline price changes with pricing calculations
  const handleBaselineChange = (event) => {
    const inputValue = event.target.value;

    // Allow empty string for clearing the field
    if (inputValue === '') {
      setLocalOrderLine({
        ...localOrderLine,
        custom_item: {
          ...localOrderLine.custom_item,
          baseline: '',
          baseline_inc_gst: '',
          cost: '0.00',
          price: '0.00',
        },
        order_line_value: '0.00',
      });
      return;
    }

    const baseline = parseFloat(inputValue);

    // Only calculate if we have a valid number
    if (Number.isNaN(baseline)) return;

    // Calculate cost and price based on baseline
    const cost = localOrderLine.custom_item.baseline_inc_markdown
      ? (baseline * (1 - supplierCommission / 100)).toFixed(2)
      : baseline.toFixed(2);
    const price = (cost * (1 + markup / 100)).toFixed(2);

    const updatedOrderLine = {
      ...localOrderLine,
      custom_item: {
        ...localOrderLine.custom_item,
        baseline: inputValue, // Keep the raw input value
        baseline_inc_gst: (baseline * 1.1).toFixed(2),
        cost,
        price,
      },
      order_line_value: price,
    };

    setLocalOrderLine(updatedOrderLine);
  };

  const handleBaselineBlur = async () => {
    // Format the baseline value to 2 decimal places on blur
    const baseline = parseFloat(localOrderLine.custom_item.baseline);
    if (!Number.isNaN(baseline)) {
      const formattedOrderLine = {
        ...localOrderLine,
        custom_item: {
          ...localOrderLine.custom_item,
          baseline: baseline.toFixed(2),
        },
      };
      setLocalOrderLine(formattedOrderLine);
      await updateOrderLine(formattedOrderLine);
    } else {
      // If invalid, reset to original value
      setLocalOrderLine(orderLine);
    }
  };

  return (
    <div className={`orderline ${orderLine.errors ? 'errored' : ''} ${customOrderConfig ? 'custom-orderline' : ''}`}>
      <div className={`order-show-table-row ${customOrderConfig ? 'custom-order' : ''}`}>
        <div className="orderline-image-name">
          <OrderLineImage name={orderLine.name} quantity={orderLine.quantity} image={orderLine.image} />
          <div style={{ width: '100%' }}>
            <div className="orderline-name-container">
              <p className="orderline-name">{orderLine.name}</p>
              {orderLine.is_reduced_price && <span className="special-checkout-price">special</span>}
            </div>

            <div className="extras">
              {orderLine.extrasString && (
                <span className="grey" title={orderLine.extrasString}>
                  {orderLine.extrasString}
                </span>
              )}
            </div>
            {!customOrderConfig && (
              <input
                className="orderline-note grey"
                name="note"
                value={localOrderLine.note}
                onChange={handleUpdate}
                onBlur={onBlur}
                placeholder="Add a note"
              />
            )}
            {orderLine.errors && <p className="error-message">{orderLine.errors.join(',')}</p>}
          </div>
        </div>
        <p style={{ justifySelf: 'flex-end' }}>{`$${Number(orderLine.order_line_value).toFixed(2)}`}</p>
        <OrderLineQuantity
          orderLine={localOrderLine}
          originalQuantity={orderLine.quantity}
          updateItem={setLocalOrderLine}
          onBlur={onBlur}
        />
        <p className={orderLine.is_reduced_price ? ' reduced' : ''}>
          <strong>${(Number(orderLine.order_line_value) * orderLine.quantity).toFixed(2)}</strong>
        </p>
        {customOrderConfig && (
          <>
            <span
              className="orderline-clone icon icon-large icon-copy"
              role="presentation"
              onClick={handleClone}
              style={{
                cursor: 'pointer',
                fontSize: '16px',
                color: '#1f9e86',
                borderRadius: '4px',
              }}
              title="Clone item"
            />
            <span
              className="orderline-edit icon icon-large icon-pencil-black"
              role="presentation"
              onClick={handleEdit}
              style={{
                cursor: 'pointer',
                fontSize: '16px',
                color: '#1f9e86',
                borderRadius: '4px',
              }}
              title="Clone item"
            />
          </>
        )}
        <span className="orderline-delete icon icon-large icon-bin-dark" role="presentation" onClick={handleRemove} />
      </div>
      {customOrderConfig && orderLine.custom_item && (
        <div className="custom-orderline-pricing">
          <div className="pricing-fields-row">
            <div className="pricing-field">
              <div className="pricing-label">Price (exc GST)</div>
              <input
                type="number"
                step="0.01"
                min="0"
                className="pricing-input"
                value={localOrderLine.custom_item.baseline || ''}
                onChange={handleBaselineChange}
                onBlur={handleBaselineBlur}
                placeholder="0.00"
              />
            </div>
            <div className="pricing-field">
              <div className="pricing-label">Supplier Cost</div>
              <span className="pricing-value">${localOrderLine.custom_item.cost || '0.00'}</span>
            </div>
            <div className="pricing-field">
              <div className="pricing-label">Customer Price</div>
              <span className="pricing-value">${localOrderLine.custom_item.price || '0.00'}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderLine;
