/*=============================================>>>>>
= Placeholder =
===============================================>>>>>*/
@mixin placeholder {
  &::placeholder {
    @content;
  }
}

/*=============================================>>>>>
= Animations =
===============================================>>>>>*/

@mixin keyframes ($animation_name) {
  @keyframes $animation_name {
    @content;
  }
}

@mixin animation ($animation: bounceIn, $duration: 1s, $delay: 0s) {
  animation-delay: $delay;
  animation-duration: $duration;
  animation-name: $animation;
  animation-fill-mode: forwards; /* this prevents the animation from restarting! */
}

@mixin shimmer {
  background: #f6f7f8;
  background-image: linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
  background-repeat: no-repeat;
  background-size: 1000px 1000px;
  animation-duration: 1s;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
  animation-name: placeholderShimmer;
  animation-timing-function: linear;
}

@keyframes beating {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.2);
  }
}

/*=============================================>>>>>
= Ellipsis =
===============================================>>>>>*/

@mixin ellipsis($width: 100%) {
  display: inline-block;
  max-width: $width;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/*=============================================>>>>>
= Position =
===============================================>>>>>*/

@mixin vertical-align($position: absolute) {
  position: $position;
  top: 50%;
  transform: translateY(-50%);
}

@mixin horizontal-align($position: absolute) {
  position: $position;
  left: 50%;
  transform: translateX(-50%);
}

@mixin center-align($position: absolute) {
  position: $position;
  top: 50%;
  left: 50%;
  transform: translateY(-50%) translateX(-50%);
}

/*=============================================>>>>>
= Visibility =
===============================================>>>>>*/

@mixin visibility($visible: false) {
  @if $visible {
    opacity: 1;
    visibility: visible;
    height: auto;
    width: auto;
    overflow: inherit;
  } @else {
    opacity: 0;
    visibility: hidden;
    height: 0;
    width: 0;
    overflow: hidden;
  }
}

@mixin checkbox-style($margin-direction: null, $margin-size: 0px) {
  display: none;
  
  & + .custom-checkbox {
    width: 18px;
    height: 18px;
    border-radius: 2px;
    border: 1px solid rgb(209, 209, 209);
    position: relative;
    display: inline-block;

    // Add margin if specified
    @if $margin-direction == 'margin-left' {
      margin-left: $margin-size;
    }
    @else if $margin-direction == 'margin-right' {
      margin-right: $margin-size;
    }
  }

  &:checked + .custom-checkbox {
    border-radius: 3px;
    border-color: $primary;
  }

  &:checked + .custom-checkbox::after {
    content: '';
    background: url('../../images/icons/check.svg') no-repeat center center;
    background-size: 12px;
    background-color: $primary;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    border-radius: 2px;
  }
}
