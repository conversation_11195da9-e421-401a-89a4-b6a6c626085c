import { useState, useContext } from 'react';
import PlacesAutocomplete, { geocodeByPlaceId } from 'react-places-autocomplete';

import { HostContext } from 'context/host';

const GoogleAddressSearch = ({ placeholder = 'Set Address', deliveryAddress, setDeliveryAddress }) => {
  const cachedAddress =
    deliveryAddress?.street && deliveryAddress?.suburb && deliveryAddress?.state && deliveryAddress?.postcode
      ? `${deliveryAddress.street}, ${deliveryAddress.suburb} ${deliveryAddress.state}, ${deliveryAddress.postcode}`
      : '';

  const [value, setValue] = useState(cachedAddress);
  const [address, setAddress] = useState('');

  const { locality } = useContext(HostContext);

  // Function to correct known postcode issues
  const correctPostcode = (streetAddress, suburb, state, originalPostcode) => {
    // Handle specific address corrections
    const addressCorrections = [
      // Collins Street Melbourne CBD should be 3000, not 3008
      { pattern: '360 Collins Street', suburb: 'Melbourne', state: 'VIC', postcode: '3000' },
    ];

    // Check for exact street address matches
    if (streetAddress) {
      const matchingCorrection = addressCorrections.find(
        (correction) =>
          streetAddress.includes(correction.pattern) &&
          suburb?.toLowerCase().includes(correction.suburb.toLowerCase()) &&
          state === correction.state
      );

      if (matchingCorrection) {
        return matchingCorrection.postcode;
      }
    }

    return originalPostcode;
  };

  const onSearchSubmit = async (val, placeId) => {
    try {
      const [result] = await geocodeByPlaceId(placeId);

      const { address_components: addressComponents } = result;
      // Get values from google places address components array
      const streetNumType = 'street_number';
      const streetNameType = 'route';
      const subLocalityType = 'sublocality';
      const suburbType = 'locality';
      const stateType = 'administrative_area_level_1';
      const postcodeType = 'postal_code';
      let suburb = addressComponents
        .find((comp) => comp.types.includes(subLocalityType))
        ?.short_name?.replace(/\s/g, '-');
      suburb ||= addressComponents.find((comp) => comp.types.includes(suburbType)).short_name.replace(/\s/g, '-');
      const state = addressComponents.find((comp) => comp.types.includes(stateType)).short_name;
      const postcode = addressComponents.find((comp) => comp.types.includes(postcodeType)).short_name;
      const streetNumber = addressComponents.find((comp) => comp.types.includes(streetNumType))?.long_name;
      const streetName = addressComponents.find((comp) => comp.types.includes(streetNameType))?.long_name;

      let streetAddress = null;

      if (streetName) {
        streetAddress = streetNumber ? `${streetNumber} ${streetName}` : streetName;
      }

      // Apply postcode correction if needed
      const correctedPostcode = correctPostcode(streetAddress, suburb, state, postcode);

      console.log(correctedPostcode);
      setDeliveryAddress({ suburb, state, street: streetAddress, postcode: correctedPostcode });

      setValue(val);
    } catch (error) {
      console.log('Error fetching address', error);
    }
  };

  const handleChange = (val) => {
    setValue(val);
  };

  const handleFocus = () => {
    setValue('');
  };

  const handleBlur = () => {
    setValue(address || '');
  };

  const handleSelect = (val, placeId) => {
    setAddress(val);
    onSearchSubmit(val, placeId);
  };

  const searchOptions = {
    componentRestrictions: { country: locality },
    types: ['address'],
  };

  return (
    <div className="google-address-search">
      <PlacesAutocomplete
        searchOptions={searchOptions}
        onChange={handleChange}
        onSelect={handleSelect}
        value={value || ''}
      >
        {({ getInputProps, suggestions, getSuggestionItemProps, loading: loadingSuggestions }) => (
          <>
            <input
              onFocus={handleFocus}
              {...getInputProps({
                placeholder,
                className: 'location-search-input',
                onBlur: handleBlur,
              })}
              spellCheck={false}
              required
            />
            {(suggestions.length > 0 || loadingSuggestions) && (
              <div className="autocomplete-dropdown-container">
                {loadingSuggestions && <div className="autocomplete-dropdown-item">Loading...</div>}
                {suggestions.filter(
                  (suggestion) =>
                    suggestion.types.includes('street_address') ||
                    suggestion.types.includes('premise') ||
                    suggestion.types.includes('subpremise')
                ).length > 0 ? (
                  suggestions
                    .filter(
                      (suggestion) =>
                        suggestion.types.includes('street_address') ||
                        suggestion.types.includes('premise') ||
                        suggestion.types.includes('subpremise')
                    )
                    .map((suggestion) => {
                      const className = `autocomplete-dropdown-item ${suggestion.active ? 'active' : ''} `;
                      return (
                        <div {...getSuggestionItemProps(suggestion, { className })}>
                          <span>{suggestion.description}</span>
                        </div>
                      );
                    })
                ) : (
                  <div className="google-no-results">No results found. Did you type a street address?</div>
                )}
              </div>
            )}
          </>
        )}
      </PlacesAutocomplete>
    </div>
  );
};

export default GoogleAddressSearch;
