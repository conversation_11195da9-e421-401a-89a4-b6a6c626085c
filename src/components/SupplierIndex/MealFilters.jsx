import useFilterStore from 'store/useFilterStore';
import { mealPlanFilters } from 'utils/constants';

const MealFilters = () => {
  const { toggleFilter, filters } = useFilterStore((state) => ({
    toggleFilter: state.toggleFilter,
    filters: state.filters,
  }));

  return (
    <div className="meal-filters">
      <h3 className="filter-title">I'm looking for</h3>
      {mealPlanFilters.map((filter) => (
        <div className={`meal-filter${filters.includes(filter) ? ' active' : ''}`} onClick={() => toggleFilter(filter)}>
          {filter}
        </div>
      ))}
    </div>
  );
};

export default MealFilters;
