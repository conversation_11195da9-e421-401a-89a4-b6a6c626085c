import { useContext } from 'react';

import { <PERSON>, Hamburger } from 'components/Common';
import { GeneralSearch } from 'components/SupplierIndex';
import { SearchMenuItems, MajorSearchMenuItems } from 'components/SupplierShow';
import { UserContext } from 'context/user';
import { header as headerLinks } from 'utils/navigations';
import useFullScreen from 'hooks/Common/useFullScreen';
import { useRouter } from 'next/router';
import { HostContext } from 'context/host';

// Inline navigation link component
const HeaderNavigationLink = ({ className, to, text }) => (
  <li>
    <Link className={className || ''} href={to}>
      {text}
    </Link>
  </li>
);

const LogoutLink = () => {
  const { appURL } = useContext(HostContext);
  const logout = (e) => {
    e.preventDefault();
    document.getElementById('logout-form').submit();
  };
  return (
    <form id="logout-form" action={`${appURL}/logout`} method="post">
      <input name="_method" type="hidden" value="delete" />
      <li>
        <a className="icon icon-logout" href="logout" onClick={logout}>
          Logout
        </a>
      </li>
    </form>
  );
};
// Main component
const HeaderNavigation = () => {
  const [showNav, toggleNav] = useFullScreen();
  const { user } = useContext(UserContext);
  const { appURL, orderURL } = useContext(HostContext);
  const headerNav = headerLinks({ appURL, orderURL, isAdmin: user?.signed_in_as_admin });

  const {
    query: { category: categoryPage, supplier: menuPage },
  } = useRouter();

  const isMajorSupplier = menuPage === 'woolworths';

  return (
    <nav className="header-navigation" role="navigation">
      <ul className={`header-navigation-parent-nav ${showNav ? 'open' : 'closed'}`}>
        <li className="header-navigation-parent-link">
          {categoryPage && <GeneralSearch categoryPage={categoryPage} key={categoryPage} />}
          {menuPage && !isMajorSupplier && <SearchMenuItems />}
          {menuPage && isMajorSupplier && <MajorSearchMenuItems />}
        </li>
        <li className="header-navigation-parent-link large-hidden">
          <Link href="/quotes" className="button black outline compact">
            Get A Quote
          </Link>
          <span className="header-navigation-parent-link-toggle">{!!user && `Hi, ${user.first_name}`}</span>
          <div className="header-navigation-dropdown">
            <ul className="header-navigation-dropdown-links">
              {!!user && (
                <>
                  {headerNav.loggedInLinks.map((link) => (
                    <HeaderNavigationLink key={link.to + link.className} {...link} />
                  ))}
                  <LogoutLink />
                </>
              )}
              {!user &&
                headerNav.loggedOutLinks.map((link) => (
                  <HeaderNavigationLink key={link.to + link.className} {...link} />
                ))}
            </ul>
          </div>
        </li>
      </ul>
      <Hamburger active={showNav} onClick={toggleNav} />
    </nav>
  );
};

export default HeaderNavigation;
