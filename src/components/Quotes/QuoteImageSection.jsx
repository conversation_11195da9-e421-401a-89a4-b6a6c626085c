import useQuoteStore from 'store/useQuoteStore';
import { quoteSteps } from './utils/steps';
import { quoteInfo } from './utils/types';

const QuoteImageSection = ({ type, uuid, quoteCustomer }) => {
  const { step } = useQuoteStore((state) => ({
    step: state.step,
  }));

  const stepsCount = quoteSteps({ uuid })[type].length - 1;
  const stepsCompleted = step === stepsCount;

  return (
    <div className="auth-card__illustration quote">
      <div className="quote-image">
        <img src={quoteInfo?.[type]?.img} alt={`${type} quote`} />
      </div>
      {type !== 'start' && (
        <div className="progress-bar-container">
          <p className="progress-tag">{stepsCompleted ? 'Completed' : 'Details Needed'}</p>
          <progress value={step} max={stepsCount} />
        </div>
      )}

      <div className="quote-panel-info" style={type !== 'start' ? { paddingTop: 0 } : {}}>
        <h4 className="auth-card-title" style={{ fontWeight: 'bold', fontSize: '20px', marginBottom: '6px' }}>
          {quoteCustomer && (
            <>
              <span>Create a Quote For: </span>
              <p style={{ fontWeight: 900, color: '#FB35DC', margin: '4px 0' }}>{quoteCustomer.company_name}</p>
            </>
          )}
          {!quoteCustomer && quoteInfo?.[type]?.title}
        </h4>
        <p>{quoteInfo?.[type]?.description}</p>
      </div>
    </div>
  );
};

export default QuoteImageSection;
