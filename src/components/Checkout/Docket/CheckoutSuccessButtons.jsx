import { useContext } from 'react';
import { HostContext } from 'context/host';
import useCheckoutStore from 'store/useCheckoutStore';
import { shallow } from 'zustand/shallow';

const CheckoutSuccessButtons = () => {
  const { appURL } = useContext(HostContext);
  const { supplierIndexPageUrl, mealUUID, mealName } = useCheckoutStore(
    (state) => ({
      supplierIndexPageUrl: state.order.supplierIndexPageUrl,
      mealUUID: state.order?.mealPlan?.uuid,
      mealName: state.order?.mealPlan?.name,
    }),
    shallow
  );

  const ordersLink = mealUUID ? `${appURL}/c_profile/meal-plans?mealUUID=${mealUUID}` : `${appURL}/c_profile`;

  return (
    <>
      <a href={ordersLink} className="button checkout-button" style={{ marginTop: '16px' }}>
        {mealName ? `Back to ${mealName}` : 'Back to Orders'}
      </a>
      {supplierIndexPageUrl && !mealUUID && (
        <a href={supplierIndexPageUrl} className="button checkout-button confirm primary" style={{ marginTop: '16px' }}>
          Place Another Order
        </a>
      )}
    </>
  );
};

export default CheckoutSuccessButtons;
