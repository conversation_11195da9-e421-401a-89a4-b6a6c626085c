import { Smiley } from 'components/EmployeeSurvey';
import useEmployeeSurveyStore from 'store/useEmployeeSurveyStore';

const OverallRatingsQuestion = ({ answer, setAnswer }) => {
  const { updateSubmission } = useEmployeeSurveyStore((state) => ({
    updateSubmission: state.updateSubmission,
  }));

  function handleChange(e) {
    setAnswer((state) => ({ ...state, value: e.target.value }));
    updateSubmission(e);
  }

  return (
    <div className="overall-ratings-container">
      <input
        type="range"
        min="1"
        max="100"
        value={answer.value}
        onChange={handleChange}
        className="slider-color"
        name="overall_rating"
      />
      <Smiley value={answer.value} />
    </div>
  );
};

export default OverallRatingsQuestion;
