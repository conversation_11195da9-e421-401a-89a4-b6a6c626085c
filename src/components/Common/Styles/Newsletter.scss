.newsletter {
  position: relative;
  padding: 20px 0 0;
  .newsletter-title {
    margin-bottom: 20px;
    text-transform: uppercase;
    @include media-down(small-desktop) {
      font-size: 15px;
    }
  }
  .newsletter-inputs {
    display: flex;
    width: 100%;
    margin: 0 auto;
    @include media-down(tablet) {
      max-width: 100%;
    }
    label {
      flex: 1;
      input {
        width: 100%;
        height: 50px;
        border: none;
        border-radius: 3px 0 0 3px;
        font-size: 16px;
        padding: 0 20px;
        outline: none;
        border: none;
        box-shadow: none;
      }
    }
    button {
      border-radius: 0 3px 3px 0;
      margin: 0;
      background: $black;
      border: $white 5px solid;
    }
  }
}

.newsletter-success {
  span {
    font-size: 15px;
  }
}
