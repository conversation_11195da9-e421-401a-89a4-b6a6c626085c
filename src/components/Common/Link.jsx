import Link from 'next/link';

const LinkComponent = (props) => {
  const { children, className = '', href, as, title = null, attr = null, prefetch = false } = props;
  const isExternal = (href && href.includes('http')) || (href && href[0] === '#');
  if (isExternal) {
    return (
      <a href={href} className={className} data-content={attr} title={title} rel="nofollow noopener noreferrer">
        {children}
      </a>
    );
  }
  return (
    <Link href={href} as={as} prefetch={prefetch}>
      <a className={className || ''} title={title || null}>
        {children}
      </a>
    </Link>
  );
};

export default LinkComponent;
