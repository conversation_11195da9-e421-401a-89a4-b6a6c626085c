import { useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/router';

import { CategoryContext } from 'context/category';
import { Link } from 'components/Common';
import {
  DocketDate,
  DocketLocations,
  DocketCheckoutButton,
  DocketPricing,
  DocketRecurringDays,
  OrderEditWarningModal,
  RecurringOrderForm,
} from 'components/SupplierShow';
import useFullScreen from 'hooks/Common/useFullScreen';
import useDeliveryDateStore from 'store/useDeliveryDateStore';
import useDocketStore from 'store/useDocketStore';
import { getAddAnotherSupplierLink } from 'utils/order';

const Docket = () => {
  const router = useRouter();
  const { mealUUID, addMore } = router.query;
  const [showNav, toggleNav] = useFullScreen();
  const [isRecurringOrder, setIsRecurringOrder] = useState(false);
  const [isMealNoteExpanded, setIsMealNoteExpanded] = useState(true);
  const category = useContext(CategoryContext);

  const { deliveryAt, setDate, setTime, clearDate, deliveryDate, deliveryTime } = useDeliveryDateStore((state) => ({
    deliveryAt: state.delivery_at,
    setDate: state.setDate,
    setTime: state.setTime,
    clearDate: state.clearDate,
    deliveryDate: state.delivery_date,
    deliveryTime: state.delivery_time,
  }));
  const { activeOrder, order, mealPlanAdminNotes } = useDocketStore((state) => ({
    activeOrder: state.orders[state.activeOrderID],
    order: state.order,
    mealPlanAdminNotes: state.mealPlanAdminNotes,
  }));

  const hasOrder = !!Number(activeOrder?.totals?.total_number);
  const editingOrder = order?.status && order?.status !== 'draft';
  let geoLocation;
  if (typeof window !== 'undefined') {
    geoLocation = JSON.parse(localStorage.getItem('geo-location'));
  }
  const majorSupplier = order?.isWoolworthsOrder;
  const showDocketDate = !editingOrder && !majorSupplier && deliveryAt;
  const showRecurringOrderForm = !isRecurringOrder && !editingOrder && !majorSupplier;

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setDate(localStorage.getItem('deliveryDate'));
      setTime(localStorage.getItem('deliveryTime'));
    }
    if (editingOrder) clearDate();
  }, []);

  useEffect(() => {
    setIsRecurringOrder(order.isRecurrent);
  }, [order.isRecurrent]);

  // Handle scroll detection to auto-collapse meal plan note
  useEffect(() => {
    const handleScroll = () => {
      if (isMealNoteExpanded) {
        setIsMealNoteExpanded(false);
      }
    };

    // Add scroll listener to window
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Also add scroll listener to any scrollable containers within the page
    const scrollableElements = document.querySelectorAll('.docket-internal, .supplier-show-container, .main-content');
    scrollableElements.forEach((element) => {
      element.addEventListener('scroll', handleScroll, { passive: true });
    });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      scrollableElements.forEach((element) => {
        element.removeEventListener('scroll', handleScroll);
      });
    };
  }, [isMealNoteExpanded]);

  return (
    <>
      <div className="docket-container">
        <div className={`docket ${showNav ? 'open' : 'closed'} ${mealPlanAdminNotes ? 'with-meal-note' : ''}`}>
          <div className="mobile-filters-heading">
            <span>Cart</span>
            <button
              type="button"
              onClick={toggleNav}
              className="icon icon-close docket-close-btn"
              aria-label="Close Filter"
            />
          </div>
          {!!mealPlanAdminNotes && (
            <div className="docket-meal-plan-accordion">
              <button
                type="button"
                className={`docket-meal-plan-toggle ${isMealNoteExpanded ? 'expanded' : 'collapsed'}`}
                onClick={() => setIsMealNoteExpanded(!isMealNoteExpanded)}
                aria-expanded={isMealNoteExpanded}
                aria-controls="meal-plan-content"
              >
                <strong>Meal Plan Notes</strong>
                <span className="accordion-icon">{isMealNoteExpanded ? '−' : '+'}</span>
              </button>
              <div
                id="meal-plan-content"
                className={`docket-meal-plan-content ${isMealNoteExpanded ? 'expanded' : 'collapsed'}`}
                style={{
                  maxHeight: isMealNoteExpanded ? '200px' : '0',
                  overflow: 'hidden',
                  transition: 'max-height 0.3s ease-in-out',
                }}
              >
                <div
                  className="docket-meal-plan-note"
                  dangerouslySetInnerHTML={{
                    __html: mealPlanAdminNotes.replace(/\n/g, '<br/>'),
                  }}
                />
              </div>
            </div>
          )}
          {showDocketDate && <DocketDate />}
          <div className={`docket-wrapper ${mealPlanAdminNotes ? 'with-meal-note' : ''}`}>
            <div className="docket-internal">
              {editingOrder && <h5 className="docket-editing-order-heading">You are currently editing an order</h5>}
              {showRecurringOrderForm && <RecurringOrderForm />}
              <DocketRecurringDays recurrentOrderDays={order?.recurrentOrderDays} />
              <DocketLocations />
              <DocketPricing hasOrder={hasOrder} orderTotals={activeOrder?.totals} />
            </div>
            <div className="docket-checkout">
              <DocketCheckoutButton isActive={hasOrder} editingOrder={editingOrder} orderID={order?.id} />
              {geoLocation?.state && !majorSupplier && (
                <Link
                  className="docket-add-supplier"
                  href={getAddAnotherSupplierLink({
                    category,
                    geoLocation,
                    mealUUID,
                    dateOptions: { deliveryDate, deliveryTime },
                  })}
                >
                  Add Another Supplier
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>
      {hasOrder && (
        <button type="button" onClick={toggleNav} className="docket-open-btn" aria-label="Open Cart">
          View Order
        </button>
      )}
      {activeOrder?.name && <OrderEditWarningModal order={activeOrder} editingOrder={editingOrder && !addMore} />}
    </>
  );
};

export default Docket;
