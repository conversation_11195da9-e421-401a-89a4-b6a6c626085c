import DatePicker from 'react-datepicker';
import { RadioButtonGroup } from 'components/Quotes';
import useQuoteStore from 'store/useQuoteStore';

import 'react-datepicker/dist/react-datepicker.css';

const InputField = ({ type, label, className, isError, value, onChange, span }) => {
  const { budgetType, setFormField } = useQuoteStore((state) => ({
    budgetType: state.formFields.budgetType,
    setFormField: state.setFormField,
  }));
  const handleChange = (e) => {
    if (type === 'date') {
      const formattedDate = new Intl.DateTimeFormat('sv-SE', { dateStyle: 'short' }).format(e).replace(/-/g, '/');
      onChange(formattedDate);
    } else if (type === 'time') {
      onChange(e);
    } else {
      onChange(e.target.value);
    }
  };

  return (
    <div style={span ? { gridColumn: span } : {}} className="quote-input">
      <label className={className}>{label}</label>
      {type === 'date' && (
        <DatePicker
          selected={value ? new Date(value) : ''}
          onChange={handleChange}
          minDate={new Date()}
          name="delivery_at"
          dateFormat="dd/MM/yyyy"
          autoComplete="off"
          className={`form-input${isError ? ' error' : ''}`}
        />
      )}
      {type === 'time' && (
        <DatePicker
          selected={value || null}
          onChange={handleChange}
          showTimeSelect
          showTimeSelectOnly
          timeIntervals={15}
          name="delivery_at"
          dateFormat="h:mm aa"
          autoComplete="off"
          className={`form-input${isError ? ' error' : ''}`}
        />
      )}
      {type !== 'date' && type !== 'time' && (
        <input className={`form-input${isError ? ' error' : ''}`} onChange={handleChange} value={value} />
      )}
      {type === 'budget' && (
        <RadioButtonGroup
          name="export-option"
          options={[
            { id: 'Per Person', label: 'Per Person' },
            { id: 'total', label: 'Total' },
          ]}
          value={budgetType.value}
          onChange={(selectedOption) => setFormField('budgetType', selectedOption)}
          className="budget-type-radios"
        />
      )}
    </div>
  );
};

export default InputField;
