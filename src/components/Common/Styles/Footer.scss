.footer {
  // position: relative;
  background: $off-white;
  // border-top: darken($off-white, 5%) 1px solid;
  padding: 30px 0 20px;
  @include media-down(large-tablet) {
    padding-bottom: 0;
  }
  > .wrapper {
    display: grid;
    grid-template-columns: 74% 26%;
    padding: 20px;
    @include media-down(large-tablet) {
      grid: "a" auto "b" 1fr / 1fr;
      padding: 10px 20px;
    }
    .footer-right {
      @include media-between(small-tablet, large-tablet) {
        display: grid;
        align-items: center;
        grid-template:
        "left-col middle-col right-col"
        "left-col middle-col right-col";
        grid-template-columns: repeat(3, 1fr);
        padding-top: 30px;
        margin-top: 30px;
        border-top: rgba($black, 0.15) 1px solid;
      }
      @include media-down(small-tablet) {
        text-align: center;
      }
      @include media-down(large-mobile) {
        padding-top: 20px;
        text-align: left;
      }
      .logo {
        @include media-down(large-tablet) {
          grid-column: left-col / span 3;
          place-self: center;
        }
        @include media-down(small-tablet) {
          margin: 0 auto;
          width: 121px;
          height: 42px;
        }
        @include media-down(large-mobile) {
          margin: 0;
        }
      }
      .newsletter {
        margin-bottom: -1px;
        padding: 30px 0 40px;
        @include media-down(tablet) {
          grid-column: left-col / span 2;
          padding: 20px 0;
        }
        @include media-down(small-tablet) {
          padding: 30px 0;
        }
        .newsletter-inputs {
          max-width: 400px;
          @include media-down(tablet) {
            max-width: 100%;
          }
          label {
            input {
              height: 44px;
            }
          }
        }
      }
      .social-links {
        @include media-down(large-tablet) {
          place-self: center center;
        }
        @include media-down(tablet) {
          place-self: center end;
        }
        @include media-down(small-tablet) {
          display: grid;
          justify-content: center;
        }
        @include media-down(large-mobile) {
          justify-content: flex-start;
        }
      }
      .footer-sitemap {
        // text-align: right;
        @include media-down(tablet) {
          grid-column: left-col / span 3;
          text-align: center;
        }
        @include media-down(large-mobile) {
          text-align: left;
        }
      }
    }
  }
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  @include media-down(tablet) {
    grid-template-columns: repeat(3, 1fr);
  }
  @include media-down(small-tablet) {
    grid-template-columns: repeat(2, 1fr);
  }
  @include media-down(large-mobile) {
    grid-template-columns: 1fr;
  }
}

.footer-links-col {
  &:last-of-type {
    @include media-between(small-tablet, tablet) {
      display: none;
    }
    @include media-down(large-mobile) {
      display: none;
    }
  }
  &.something-else, &.about-help {
    @include media-down(large-mobile) {
      display: none;
    }
  }
}

.footer-links-nav {
  padding: 10px 0;
  h5 {
    font-family: $font-weight-bold;
    text-transform: uppercase;
    font-size: 16px;
    margin: 0 0 20px;
    @include media-down(small-desktop) {
      font-size: 15px;
    }
  }
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    li {
      padding: 0 0 8px;
      a {
        display: flex;
        align-items: center;
        font-size: 15px;
        &:before {
          width: 22px;
          height: 22px;
          margin: 0 10px 0 0;
          // display: none;
        }
      }
    }
  }
}

.footer-sitemap {
  padding: 25px 0 20px;
  font-size: 14px;
}

.footer-sitemap-nav {
  padding-bottom: 6px;
  a {
    margin: 0 6px;
    &:first-child {
      margin-left: 0;
    }
    &:last-child {
      margin-right: 0;
    }
  }
}
