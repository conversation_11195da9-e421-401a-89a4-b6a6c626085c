import ReCAPTC<PERSON> from 'react-google-recaptcha';
import useQuoteStore from 'store/useQuoteStore';

const QuoteRecaptcha = () => {
  const { setCaptchaResponse } = useQuoteStore((state) => ({
    setCaptchaResponse: state.setCaptchaResponse,
  }));

  function handleCaptchaResponse(response) {
    setCaptchaResponse(response);
  }
  return (
    <div className="quote-recaptcha">
      <ReCAPTCHA sitekey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY} onChange={handleCaptchaResponse} />
    </div>
  );
};

export default QuoteRecaptcha;
