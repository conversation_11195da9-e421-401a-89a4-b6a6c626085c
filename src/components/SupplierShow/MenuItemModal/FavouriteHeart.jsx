import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import yordar from 'api/yordar';
import useMenuItemStore from 'store/useMenuItemStore';
import useMajorFavouritesStore from 'store/useMajorFavouritesStore';
import { setFavouriteItem } from 'actions';
import { DYNAMIC_FAVOURITE_ITEM_ENDPOINT } from 'api/endpoints';

async function changeFavouriteStatus({ e, method, dispatch, setBeatingHeart, item, isMajorSupplier, setMenuItem }) {
  e.stopPropagation();
  setBeatingHeart(true);
  const type = method === 'put' ? 'add' : 'remove';
  await yordar(DYNAMIC_FAVOURITE_ITEM_ENDPOINT(item.id), {
    method,
    withCredentials: true,
  });
  setMenuItem({ ...item, is_favourite: !item.is_favourite });
  setBeatingHeart(false);
  if (isMajorSupplier) {
    const { updateMajorFavourites } = useMajorFavouritesStore.getState();
    updateMajorFavourites({ type, favourite: item.id });
    return;
  }
  return dispatch(setFavouriteItem({ id: item.id, isFavourite: method === 'put' }));
}

const FavouriteHeart = ({ item }) => {
  const [heart, setHeart] = useState('empty');
  const [beatingHeart, setBeatingHeart] = useState(false);
  const dispatch = useDispatch();
  const isMajorSupplier = useSelector((state) => state.suppliers.listing.slug === 'woolworths');
  const majorFavourites = useMajorFavouritesStore((state) => state.favourites);
  const inMajorFavourites = majorFavourites.includes(item.id);
  const setMenuItem = useMenuItemStore((state) => state.setMenuItem);

  let heartOptions;
  if (!isMajorSupplier) {
    heartOptions = item.is_favourite
      ? { currentStatus: 'full', oppositeStatus: 'empty', method: 'delete' }
      : { currentStatus: 'empty', oppositeStatus: 'full', method: 'put' };
  } else {
    heartOptions = inMajorFavourites
      ? { currentStatus: 'full', oppositeStatus: 'empty', method: 'delete' }
      : { currentStatus: 'empty', oppositeStatus: 'full', method: 'put' };
  }

  useEffect(() => {
    setHeart(heartOptions.currentStatus);
  }, [item.is_favourite, majorFavourites]);

  return (
    <span
      className={`icon icon-heart-${heart} favourite-heart ${beatingHeart ? 'beating-heart' : ''} icon-large`}
      onMouseEnter={() => setHeart(heartOptions.oppositeStatus)}
      onMouseLeave={() => setHeart(heartOptions.currentStatus)}
      onClick={(e) =>
        changeFavouriteStatus({
          e,
          method: heartOptions.method,
          dispatch,
          setBeatingHeart,
          item,
          isMajorSupplier,
          setMenuItem,
        })
      }
      role="presentation"
      style={{ marginLeft: '4px', cursor: 'pointer', transform: 'translateY(-4px)' }}
    />
  );
};

export default FavouriteHeart;
