import { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';

import { DropDown } from 'components/Common';
import useMenuNavDimensions from 'hooks/SupplierShow/useMenuNavDimensions';
import useVisibleMenuSection from 'store/useVisibleMenuSection';

const MenuScrollingCategories = ({ menuSections }) => {
  const container = useRef(null);
  const categoryRefs = useRef(new Array(menuSections.length).fill(null));
  const [overflownCategories, setOverflownCategories] = useState([]);
  const visibleMenuSection = useVisibleMenuSection((state) => state.visibleMenuSection);
  const hasFavouriteItems = useSelector((state) => state.hasFavouriteItems);
  let { width } = useMenuNavDimensions(container, visibleMenuSection);

  const currentOverflownCategory = overflownCategories.find((cat) => cat.name === visibleMenuSection?.name);

  const dropdownOptions = overflownCategories
    .sort((a, b) => (a.position < b.position ? -1 : 0))
    .map((category) => ({
      href: `#${category.href}`,
      label: category.name,
      ...(category.name === visibleMenuSection?.name && { className: 'active' }),
    }));

  const handleOverflowingCategories = () => {
    categoryRefs.current.forEach((item) => {
      if (!item?.clientWidth || width === null) return;

      width -= item.clientWidth;
      const currentCategory = {
        name: item.textContent,
        position: +item.dataset.position,
        href: `menu-section-${item.dataset.id}`,
      };
      const inOverflownArray = overflownCategories.find((cat) => cat.name === currentCategory.name);

      if (width < 0) {
        item.style.visibility = 'hidden';
        if (!inOverflownArray) {
          setOverflownCategories((arr) => [...arr, currentCategory]);
        }
      } else {
        item.style.visibility = 'visible';
        if (inOverflownArray) {
          setOverflownCategories(overflownCategories.filter((category) => category.name !== currentCategory.name));
        }
      }
    });
  };

  useEffect(() => handleOverflowingCategories(), [menuSections, visibleMenuSection, width]);

  const handleSectionClick = (e, name) => {
    e.preventDefault();
    const sectionName = document.querySelector(name);

    if (sectionName) {
      // Scroll into view first, aligning to the top smoothly
      sectionName.scrollIntoView({ behavior: 'smooth', block: 'start' });
      const headerOffset = 86;
      const elementPosition = sectionName.getBoundingClientRect().top + window.scrollY;
      const offsetPosition = elementPosition - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth',
      });
    }
  };

  return (
    <>
      <div className="supplier-categories" ref={container}>
        {hasFavouriteItems && (
          <li
            ref={(el) => {
              categoryRefs.current[0] = el;
            }}
            className={visibleMenuSection?.id === 'menu-section-favourites' ? 'active-menu-section' : ''}
          >
            <a
              href="#menu-section-favourites"
              aria-label="Scroll To Favourites"
              onClick={(e) => handleSectionClick(e, '#menu-section-favourites')}
            >
              Favourites
            </a>
          </li>
        )}

        {menuSections.map((group, i) => (
          <li
            key={group.name}
            ref={(el) => {
              if (hasFavouriteItems) {
                categoryRefs.current[i + 1] = el;
              } else {
                categoryRefs.current[i] = el;
              }
            }}
            className={group.name === visibleMenuSection?.name ? 'active-menu-section' : ''}
            data-position={group.position || 999}
            data-id={group.id}
          >
            <a
              href={`#menu-section-${group.id}`}
              aria-label={`Scroll to ${group.name}`}
              onClick={(e) => handleSectionClick(e, `#menu-section-${group.id}`)}
            >
              {group.name}
            </a>
          </li>
        ))}
      </div>
      {overflownCategories.length > 0 && (
        <DropDown
          label={currentOverflownCategory ? visibleMenuSection?.name : 'More'}
          options={dropdownOptions}
          dropdownClass={currentOverflownCategory ? 'active-menu-section' : ''}
          chevronColor={currentOverflownCategory && 'white'}
          scrollSection
          scrollIntoView
        />
      )}
    </>
  );
};

export default MenuScrollingCategories;
