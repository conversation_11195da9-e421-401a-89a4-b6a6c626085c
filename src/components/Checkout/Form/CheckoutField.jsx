import useCheckoutStore from 'store/useCheckoutStore';
import { NumberControl } from 'components/Checkout';
import { shallow } from 'zustand/shallow';

const inputClassNames = {
  name: 'input-icon order checkout-input',
  numberOfPeople: 'number-of-people',
  contactName: 'checkout-input name',
  phone: 'checkout-input phone',
  companyName: 'checkout-input company',
  pantryManager: 'checkout-input pantry-manager',
  woolworthsOrderID: 'input-icon order checkout-input',
};

const CheckoutField = ({ type, title, placeholder, numberControls, hint, fullWidth }) => {
  const { value, validation, pantryManagers, setOrderDetail } = useCheckoutStore(
    (state) => ({
      value: state.order[type],
      setOrderDetail: state.setOrderDetail,
      validation: state.validations[type],
      pantryManagers: state.checkout?.pantry_managers,
      order: state.order,
    }),
    shallow
  );

  const isInvalid = validation === false;

  const setValue = (newValue) => setOrderDetail(type, newValue);

  if (type === 'pantryManager') {
    return (
      <div className="pantry-manager">
        <p className="title">{title}</p>
        <select
          value={value}
          onChange={(e) => setValue(e.target.value)}
          className={`${inputClassNames[type]}${isInvalid ? ' invalid--input' : ''}`}
        >
          <option value="">Select Pantry Manager</option>
          {pantryManagers.map((manager) => (
            <option key={manager.id} value={manager.id}>
              {manager.name}
            </option>
          ))}
        </select>
      </div>
    );
  }

  return (
    <div className={`checkout-detail ${fullWidth ? ' full-width' : ''}`}>
      <p className="title">{title}</p>
      {numberControls ? (
        <NumberControl value={value} setValue={setValue} isInvalid={isInvalid} />
      ) : (
        <input
          type="text"
          value={value}
          onChange={(e) => setValue(e.target.value)}
          placeholder={placeholder}
          className={`${inputClassNames[type]}${isInvalid ? ' invalid--input' : ''}`}
        />
      )}
      {!!hint && <p>{hint}</p>}
    </div>
  );
};

export default CheckoutField;
