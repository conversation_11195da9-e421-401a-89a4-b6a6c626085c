import { MenuNav, MenuSections, MenuItemModal } from 'components/SupplierShow';
import { FilterTags } from 'components/SupplierIndex';
import useItemFromSupplierIndex from 'hooks/SupplierShow/useItemFromSupplierIndex';
import useMenuItemStore from 'store/useMenuItemStore';
import useFilterStore from 'store/useFilterStore';
import { dietaryCategories } from 'static/categories';

const TeamOrderMenu = ({ listing, itemFromSearch = null }) => {
  const activeMenuItem = useMenuItemStore((state) => state.menuItem);

  // Get dietary filters from the filter store
  const { filters } = useFilterStore((state) => ({
    filters: state.filters,
  }));

  // Filter out only dietary filters from all filters
  const selectedDietaryFilters = filters.filter((filter) => dietaryCategories.includes(filter));

  const isMajorSupplier = listing.slug === 'woolworths';

  useItemFromSupplierIndex(itemFromSearch, listing.section_grouped_menu_items, isMajorSupplier);

  return (
    <>
      <MenuNav
        menuSections={listing.section_grouped_menu_items}
        dualNav={isMajorSupplier}
        aisleAndSection={false}
        setAisleAndSection={false}
      />
      {!!selectedDietaryFilters.length && <FilterTags filters={selectedDietaryFilters} forMenu />}
      <MenuSections sections={listing.section_grouped_menu_items} />
      <MenuItemModal item={activeMenuItem} supplierID={listing.id} isTeamOrder />
    </>
  );
};

export default TeamOrderMenu;
