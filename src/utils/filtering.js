import slugify from 'slugify';
import moment from 'moment';
import { dietaryCategories, socialCategories } from 'static/categories';

function isNumberInRange(number, rangeStart, rangeEnd) {
  // Ensure rangeStart is less than or equal to rangeEnd
  const min = Math.min(rangeStart, rangeEnd);
  const max = Math.max(rangeStart, rangeEnd);

  return number >= min && number <= max;
}

function getSecondsFromMidnight(time) {
  const givenTime = moment(time, 'h:mm A');
  const startOfDay = moment().startOf('day');
  const secondsFromMidnight = givenTime.diff(startOfDay, 'seconds');

  return secondsFromMidnight;
}

function supplierIsOperating(operatingHoursStart, operatingHoursEnd, time) {
  const chosenTime = getSecondsFromMidnight(time);
  return isNumberInRange(chosenTime, operatingHoursStart, operatingHoursEnd);
}

const supplierFilter = {
  byHighestRated: (suppliers) => suppliers.sort((a, b) => b.rating - a.rating),

  byNextDayDelivery: (suppliers) =>
    suppliers.filter(({ lead_time: leadTime }) => leadTime?.includes('day prior') || Number(leadTime) <= 24),

  byMinOrder: (suppliers) => suppliers.filter(({ min_order: minOrder }) => Number(minOrder?.replace('$', '')) <= 100),

  byFreeDelivery: (suppliers) => suppliers.filter(({ delivery_fee: deliveryFee }) => deliveryFee === 'Free Delivery'),

  byBusinessCode: (suppliers, code) =>
    suppliers.filter(({ business_codes: businessCodes }) => businessCodes.includes(code)),

  bySupplierDietaries: (suppliers, dietary) => suppliers.filter(({ dietaries }) => dietaries.includes(dietary)),

  bySupplierCategories: (suppliers, category) =>
    suppliers.filter(
      ({ categories }) =>
        categories?.includes(slugify(category.replace('&', '').replace('/', ' '), { lower: true })) ||
        (category === 'Diwali' && categories?.includes('indian'))
    ),

  byClosureDate: (suppliers, date) =>
    suppliers.filter(({ closure_dates: closureDates }) => !closureDates.includes(date)),

  byOperatingHours: (suppliers, time) =>
    suppliers.filter(({ operating_hours_start: operatingHoursStart, operating_hours_end: operatingHoursEnd }) =>
      supplierIsOperating(operatingHoursStart, operatingHoursEnd, time)
    ),
};

export function filterSuppliers(suppliers, filters, noCumulation, deliveryDate) {
  if (!filters.length && !deliveryDate) return suppliers;

  const dateFilterRegex = /^\d{1,2}\/\d{1,2}\/\d{4}$/;
  const timeFilterRegex = /^(0?[1-9]|1[0-2]):[0-5][0-9] (am|pm|AM|PM)$/;

  let filteredSuppliers = [...suppliers];

  if (noCumulation && filters.length) {
    let allFilteredSuppliers = filters.reduce((result, filter) => {
      let matchingSuppliers = [];
      switch (filter) {
        case 'Highest Rated':
          matchingSuppliers = supplierFilter.byHighestRated(suppliers);
          break;
        case 'Next Day Delivery':
          matchingSuppliers = supplierFilter.byNextDayDelivery(suppliers);
          break;
        case '$0-100 Min Order':
          matchingSuppliers = supplierFilter.byMinOrder(suppliers);
          break;
        case 'Free Delivery':
          matchingSuppliers = supplierFilter.byFreeDelivery(suppliers);
          break;
        case ['Social Enterprise', 'Contactless Delivery', 'Eco-Friendly', ...socialCategories].find(
          (code) => filter === code
        ):
          matchingSuppliers = supplierFilter.byBusinessCode(suppliers, filter);
          break;
        case dietaryCategories.find((dietary) => filter === dietary):
          matchingSuppliers = supplierFilter.bySupplierDietaries(suppliers, filter);
          break;
        case dateFilterRegex.test(filter) && filter:
          matchingSuppliers = supplierFilter.byClosureDate(suppliers, filter);
          break;
        case timeFilterRegex.test(filter) && filter:
          matchingSuppliers = supplierFilter.byOperatingHours(suppliers, filter);
          break;
        default:
          matchingSuppliers = supplierFilter.bySupplierCategories(suppliers, filter);
      }
      return [...result, ...matchingSuppliers];
    }, []);
    if (deliveryDate) {
      allFilteredSuppliers = allFilteredSuppliers.filter(
        ({ closure_dates: closureDates }) => !closureDates.includes(deliveryDate)
      );
    }
    return [...new Set(allFilteredSuppliers)];
  }
  filters.forEach((filter) => {
    switch (filter) {
      case 'Highest Rated':
        filteredSuppliers = supplierFilter.byHighestRated(filteredSuppliers);
        break;
      case 'Next Day Delivery':
        filteredSuppliers = supplierFilter.byNextDayDelivery(filteredSuppliers);
        break;
      case '$0-100 Min Order':
        filteredSuppliers = supplierFilter.byMinOrder(filteredSuppliers);
        break;
      case 'Free Delivery':
        filteredSuppliers = supplierFilter.byFreeDelivery(filteredSuppliers);
        break;
      case ['Social Enterprise', 'Contactless Delivery', 'Eco-Friendly', ...socialCategories].find(
        (code) => filter === code
      ):
        filteredSuppliers = supplierFilter.byBusinessCode(filteredSuppliers, filter);
        break;
      case dietaryCategories.find((dietary) => filter === dietary):
        filteredSuppliers = supplierFilter.bySupplierDietaries(filteredSuppliers, filter);
        break;
      case dateFilterRegex.test(filter) && filter:
        filteredSuppliers = supplierFilter.byClosureDate(filteredSuppliers, filter);
        break;
      case timeFilterRegex.test(filter) && filter:
        filteredSuppliers = supplierFilter.byOperatingHours(filteredSuppliers, filter);
        break;
      default:
        filteredSuppliers = supplierFilter.bySupplierCategories(filteredSuppliers, filter);
    }
  });
  if (deliveryDate) {
    filteredSuppliers = filteredSuppliers.filter(
      ({ closure_dates: closureDates }) => !closureDates.includes(deliveryDate)
    );
  }
  return filteredSuppliers;
}
