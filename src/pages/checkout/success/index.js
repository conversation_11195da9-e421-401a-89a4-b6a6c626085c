import { Layout } from 'components/Common';
import { CheckoutDocket, CheckoutSuccessComponent } from 'components/Checkout';
import useCheckoutStore from 'store/useCheckoutStore';

const CheckoutSuccess = ({ user }) => {
  const { order } = useCheckoutStore();
  const forDash = user?.type === 'customer';

  return (
    <Layout
      seo={{ title: 'Yordar Checkout', description: 'Checkout With Your Order' }}
      bodyBackgroundColor="#f9f9f9"
      forDash={forDash}
      customClass={forDash ? 'checkout-dash' : ''}
      type="checkout-dash"
      hideFooter
      isMealPlan={order?.mealPlan?.uuid}
    >
      <div className="checkout">
        <div className="checkout-container">
          <CheckoutSuccessComponent order={order} />
          <CheckoutDocket order={order} checkoutPanel="success" />
        </div>
      </div>
    </Layout>
  );
};

export default CheckoutSuccess;
