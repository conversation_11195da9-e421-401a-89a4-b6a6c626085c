import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import keyGenerator from 'utils/keyGenerator';
import useDocketStore from 'store/useDocketStore';

const LocationOrderLine = ({ orderline }) => {
  const activeCartDay = useSelector((state) => state.activeCartDay);
  const { deleteOrderLine, updateOrderLine } = useDocketStore((state) => ({
    deleteOrderLine: state.deleteOrderLine,
    updateOrderLine: state.updateOrderLine,
  }));
  const [prevQuantity, setPrevQuantity] = useState('');
  const [quantity, setQuantity] = useState('');
  const [loading, setLoading] = useState(false);

  const hasErrors = orderline?.errors?.length > 0;

  useEffect(() => {
    if (!prevQuantity) setPrevQuantity(orderline.quantity);
    setQuantity(orderline.quantity);
  }, [orderline.quantity]);

  const handleChange = (event) => {
    if (quantity) setPrevQuantity(quantity);
    setQuantity(event.currentTarget.value);
  };

  const setOrderLine = async (status) => {
    setLoading(true);
    if (Number(quantity) > 0 && status !== 'delete') updateOrderLine({ orderline, quantity });
    else {
      try {
        await deleteOrderLine({
          activeCartDay,
          orderLineID: orderline.id,
          locationID: orderline.location_id,
          orderID: orderline.order_id,
        });
      } catch (err) {
        if (err.response?.status === 422 && err.response?.data?.errors) {
          alert(err.response.data.errors.join('. '));
        }
        if (prevQuantity) setQuantity(prevQuantity);
      }
    }
    setLoading(false);
  };

  return (
    <div className={`supplier-order-lines${loading ? ' loading' : ''}${hasErrors ? ' errored' : ''}`}>
      <input
        type="number"
        value={quantity}
        onChange={handleChange}
        onBlur={setOrderLine}
        onFocus={(e) => e.currentTarget.select()}
      />
      <div className="orderline-name-and-note">
        <p>{orderline.name}</p>
        <div className="orderline-note">
          <p>{orderline.note}</p>
          {!!orderline?.selected_extras?.length &&
            orderline.selected_extras.map((extra) => <p key={keyGenerator('extra')}>{extra}</p>)}
          {hasErrors && <p className="error-message">{orderline.errors.join('. ')}</p>}
        </div>
      </div>
      <span className="orderline-price">{`$${orderline.price}`}</span>
      <span
        className="orderline-delete icon icon-extra-large icon-bin-dark"
        onClick={() => setOrderLine('delete')}
        role="presentation"
      />
    </div>
  );
};

export default LocationOrderLine;
