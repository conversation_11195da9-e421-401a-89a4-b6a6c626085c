import useCheckoutStore from 'store/useCheckoutStore';
import { shallow } from 'zustand/shallow';

const DeliveryLoadingDock = ({ handleButtonClick }) => {
  const { deliveryType, setOrderDetail } = useCheckoutStore(
    (state) => ({
      deliveryType: state.order.deliveryType,
      setOrderDetail: state.setOrderDetail,
    }),
    shallow
  );

  const handleChange = (event) => {
    const seletedDeliveryType = event.target.checked ? 'loading_dock' : 'normal';
    setOrderDetail('deliveryType', seletedDeliveryType);
  };

  return (
    <div className="modal-section">
      <div className="checkout-row icon icon-extra-large icon-dock">
        <p>
          Delivery requires access code{' '}
          <span className="whats-this" onClick={() => handleButtonClick('loadingDockHelper')}>
            What's This?
          </span>
        </p>
      </div>
      <label className="toggle-checkbox">
        <input
          className="toggle-checkbox"
          type="checkbox"
          name="deliveryType"
          checked={deliveryType === 'loading_dock'}
          onChange={handleChange}
        />
        <span className="toggle-checkbox__switch" />
      </label>
    </div>
  );
};

export default DeliveryLoadingDock;
