import { useEffect } from 'react';
import { useRouter } from 'next/router';
import moment from 'moment';

import { Layout } from 'components/Common';
import { SupplierFilters, SuppliersDisplay } from 'components/SupplierIndex';
import { fetchInitialSuppliers } from 'actions';
import { formatCategory } from 'utils/format';
import { wrapper } from 'utils/store';
import { supplierIndexSEO } from 'utils/seo';
import useFullScreen from 'hooks/Common/useFullScreen';
import useFilterStore from 'store/useFilterStore';
import useDeliveryDateStore from 'store/useDeliveryDateStore';
import useCategoriesStore from 'store/useCategoriesStore';
import { getCategories } from 'utils/api';

const SupplierIndex = ({ params, categories, queryFilters, user }) => {
  const [showNav, toggleNav] = useFullScreen();
  const toggleFilter = useFilterStore((state) => state.toggleFilter);
  const { setDate, setTime } = useDeliveryDateStore((state) => state);
  const setCategories = useCategoriesStore((state) => state.setCategories);

  const router = useRouter();
  const { for_date: forDate } = router.query;

  // Populate categories store with server-side data
  useEffect(() => {
    if (categories) {
      setCategories(categories);
    }
  }, [categories]);

  useEffect(() => {
    if (forDate && typeof window !== 'undefined') {
      const parsedDate = moment.parseZone(forDate);
      if (parsedDate.isValid()) {
        const deliveryDate = parsedDate.format('DD/MM/YYYY');
        const deliveryTime = parsedDate.format('h:mm a');
        localStorage.setItem('deliveryDate', deliveryDate);
        localStorage.setItem('deliveryTime', deliveryTime);
        setDate(deliveryDate);
        setTime(deliveryTime);
      } else {
        console.error('Invalid date format:', forDate);
      }
    }
  }, [forDate]);

  useEffect(() => {
    if (queryFilters) {
      queryFilters.split(',').forEach((filter) => toggleFilter(filter.humanize()));
    }
    if (forDate) {
      toggleFilter(moment.parseZone(forDate).format('DD/MM/YYYY'));
    }
  }, []);

  const { title, description } = supplierIndexSEO({
    category: formatCategory(params.category),
    suburb: params.suburb.humanize(),
  });

  const forDash = user?.type === 'customer';

  return (
    <Layout
      seo={{ title, description }}
      forDash={forDash}
      customClass={forDash ? 'supplier-index' : ''}
      categories={categories}
      type="suppliers-dash"
    >
      <section className={`search-results ${forDash ? 'dash' : ''}`}>
        <div className={`wrapper ${forDash ? 'dash' : ''}`}>
          <div className={!forDash ? 'supplier-listing-container' : ''}>
            {!forDash && <SupplierFilters categories={categories} showNav={showNav} toggleNav={toggleNav} />}
            <SuppliersDisplay toggleNav={toggleNav} />
          </div>
        </div>
      </section>
    </Layout>
  );
};

export const getServerSideProps = wrapper.getStaticProps(async ({ req, store, params, query }) => {
  const { dispatch } = store;
  const cookies = req?.headers?.cookie;
  const host = req?.headers?.host;
  const { mealUUID } = query;

  const [categories] = await Promise.all([
    getCategories(),
    dispatch(fetchInitialSuppliers({ query: params, cookies, host, mealUUID })),
  ]);

  const queryFilters = query.filters || null;

  if (!['office-catering', 'office-snacks'].includes(params.category)) {
    return {
      notFound: true,
    };
  }
  return { props: { params, categories, queryFilters } };
});

export default SupplierIndex;
