import { createContext } from 'react';

const defaultUserContext = {
  user: null,
  cart: null,
  notifications: [],
};

export const UserContext = createContext(defaultUserContext);

export const UserProviderComponent = ({ children, user, cart, notifications }) => (
  <UserContext.Provider
    value={{
      ...defaultUserContext,
      user,
      cart,
      notifications,
    }}
  >
    {children}
  </UserContext.Provider>
);

export default UserProviderComponent;
