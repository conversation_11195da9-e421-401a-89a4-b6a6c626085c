import { useState, useEffect } from 'react';
import useCheckoutStore from 'store/useCheckoutStore';

import yordar from 'api/yordar';
import { DYNAMIC_UPDATE_ORDER_ENDPOINT } from 'api/endpoints';
import { Spinner } from 'components/Common';

const DeliveryWindows = ({ setModalOpen }) => {
  const {
    order,
    checkout: { delivery_window_dates: deliveryWindowDates },
    setOrderDetail,
    fetchDeliveryWindows,
  } = useCheckoutStore();

  const activeWindowDate = deliveryWindowDates?.find((date) => date.active);
  const [selectedDate, setSelectedDate] = useState(order.deliveryWindowDate || activeWindowDate?.date);
  const [isUpdating, setIsUpdating] = useState(false);
  const [windowErrors, setWindowErrors] = useState([]);

  const [activeDeliveryWindowId, setActiveDeliveryWindowId] = useState(order.deliveryWindowId);
  const activeDeliveryWindowsDate = deliveryWindowDates?.find((windowDate) => windowDate.date === selectedDate);

  useEffect(() => {
    if (!deliveryWindowDates?.length) return;

    const deliveryWindowDate = deliveryWindowDates?.find((date) => date.active);
    setSelectedDate(order.deliveryWindowDate || deliveryWindowDate?.date);
  }, [deliveryWindowDates]);

  useEffect(() => {
    setActiveDeliveryWindowId(order.deliveryWindowId);
  }, [order.deliveryWindowId]);

  const updateDeliveryWindow = async () => {
    if (isUpdating) return;

    const selectedDeliveryWindow = activeDeliveryWindowsDate?.delivery_windows.find(
      (deliveryWindow) => deliveryWindow.id === activeDeliveryWindowId
    );
    if (!selectedDeliveryWindow) return;

    const updateParams = {};
    updateParams.order = {
      delivery_at: selectedDeliveryWindow.delivery_at,
    };
    updateParams.order.associated_woolworths_order_attributes = {
      id: order.woolworthsOrderId,
      delivery_window_id: selectedDeliveryWindow.id,
    };

    try {
      setIsUpdating(true);
      setWindowErrors([]);
      const {
        data: { order: updatedOrder },
      } = await yordar.put(DYNAMIC_UPDATE_ORDER_ENDPOINT(order.id), updateParams);
      // update order date, delivery window date and ID
      setOrderDetail('deliveryAt', updatedOrder.delivery_at);
      setOrderDetail('deliveryWindowDate', updatedOrder.delivery_window_date);
      setOrderDetail('deliveryWindowId', updatedOrder.delivery_window_id);
      setOrderDetail('deliveryWindowText', updatedOrder.delivery_window_text);
      setIsUpdating(false);
      setModalOpen(false);
    } catch (err) {
      setIsUpdating(false);
      const errors = err?.response?.data?.errors;
      setOrderDetail('deliveryWindowId', null);
      if (errors) setWindowErrors(errors);
      else alert('We were unable to select the delivery window. Please try again!');
    }
  };

  if (!deliveryWindowDates?.length) {
    fetchDeliveryWindows(order.id);
    return (
      <div>
        <h3>Delivery Window</h3>
        <div className="between-flex">
          <h4>Fetching Delivery Windows...</h4>
          <Spinner />
        </div>
      </div>
    );
  }

  return (
    <div>
      <h3 className="section-heading">Delivery Window</h3>
      <p>Please select an available delivery window from Woolworths</p>
      <div className="between-flex">
        {deliveryWindowDates.map(({ date, weekday }) => (
          <a
            key={`delivery-window-date-${date}`}
            onClick={() => !isUpdating && setSelectedDate(date)}
            className={`delivery-window-date${selectedDate === date ? ' selected' : ''}`}
          >
            <p className="bold">{weekday.substring(0, 3)}</p>
            <p>{date.substring(0, 5)}</p>
          </a>
        ))}
      </div>
      <hr />
      <div className="delivery-window-container">
        {activeDeliveryWindowsDate?.delivery_windows?.map((deliveryWindow) => (
          <a
            key={`delivery-window-${deliveryWindow.id}`}
            className={`delivery-window${activeDeliveryWindowId === deliveryWindow.id ? ' selected' : ''}${
              activeDeliveryWindowId === deliveryWindow.id && isUpdating ? ' processing' : ''
            }`}
            onClick={() => setActiveDeliveryWindowId(deliveryWindow.id)}
          >
            {deliveryWindow.label}
          </a>
        ))}
      </div>
      {!!windowErrors.length && <p className="delivery-window-errors">{windowErrors.join('.')}</p>}
      <a className="button black" style={{ width: '100%', marginTop: '20px' }} onClick={() => updateDeliveryWindow()}>
        {isUpdating ? 'Submitting...' : 'Submit'}
      </a>
    </div>
  );
};

export default DeliveryWindows;
