import Script from 'next/script';
import Head from 'next/head';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Toaster } from 'components/Common';

const Layout = (props) => {
  const {
    children,
    seo: customSeo,
    hideHeaderAndFooter = false,
    hideFooter,
    forDash,
    customClass = '',
    categories = null,
    type,
    isMealPlan,
  } = props;

  const seo = {
    title: customSeo ? customSeo.title : 'Office Catering Sydney, Melbourne and Brisbane | Corporate Catering | YORDAR',
    description: customSeo
      ? customSeo.description
      : "Australia's best corporate caterers and suppliers ready to deliver office catering and kitchen supplies to your office.",
    image: '/opengraph.png',
  };
  if (typeof window !== 'undefined') {
    document.body.style.backgroundColor = props.bodyBackgroundColor || '#fff';
  }

  return (
    <>
      <Head>
        <title>{seo.title}</title>
        <meta name="description" content={seo.description} />
        <meta name="image" content={seo.image} />
        <meta property="og:title" content={seo.title} />
        <meta property="og:description" content={seo.description} />
        <meta property="og:image" content={seo.image} />
        <meta property="og:image:alt" content={seo.description} />
        <link rel="shortcut icon" type="image/x-icon" href="/favicon.png" />
        <link rel="preconnect" href="https://app.yordar.com.au" />
        <script
          src={`https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_API_KEY}&libraries=places`}
        />
        <script
          id="google-tag-manager"
          dangerouslySetInnerHTML={{
            __html: `
          (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
          new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
          j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
          'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','GTM-57BH9Z5');
        `,
          }}
        />
      </Head>
      {!hideHeaderAndFooter && !forDash && <Header />}
      <Toaster />
      <main className={`main-content ${customClass}`}>
        {forDash && <DashSidebar isMealPlan={isMealPlan} />}
        {forDash && (
          <div className="dash-container">
            <div>
              {forDash && <DashHeader categories={categories} type={type} />}
              {children}
            </div>
          </div>
        )}
        {!forDash && children}
      </main>
      {!hideHeaderAndFooter && !hideFooter && !forDash && <Footer />}
      {!hideHeaderAndFooter && (
        <Script
          type="text/javascript"
          id="hs-script-loader"
          stratgey="lazyOnLoad"
          src="//js.hs-scripts.com/21281170.js"
        />
      )}
    </>
  );
};

export default Layout;
