import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';

import { StripeForm } from 'components/Checkout';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);

const elementsOptions = {
  appearance: { theme: 'stripe' },
};

const PayByNewCard = () => (
  <>
    <p>Use A New Card</p>
    <Elements options={elementsOptions} stripe={stripePromise}>
      <StripeForm />
    </Elements>
  </>
);

export default PayByNewCard;
