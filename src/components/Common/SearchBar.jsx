import { useContext, useEffect, useState } from 'react';
import PlacesAutocomplete, { geocodeByPlaceId } from 'react-places-autocomplete';
import classNames from 'classnames';

import useSearchedAddress from 'hooks/Common/useSearchedAddress';
import { CategoryContext } from 'context/category';
import { HostContext } from 'context/host';

const SearchBar = ({ buttonText = 'Find Suppliers', navigation, disabled, fixedWidth = false }) => {
  const [loading, setLoading] = useState(false);
  const [value, setValue] = useState('');
  const [address, setAddress] = useState('');
  const [isValid, setIsValid] = useState(false);

  const { locality, orderURL } = useContext(HostContext);
  const category = useContext(CategoryContext);

  const headerClass = classNames('searchbar', {
    navigation,
    'fixed-width': fixedWidth,
    'hide-button': !isValid && value && value.length > 0,
  });

  useEffect(() => {
    if (address && value === address) {
      setIsValid(true);
    } else {
      setIsValid(false);
    }
  }, [value, address]);

  useSearchedAddress({ setValue, setAddress });

  const onSearchSubmit = async (val, placeId) => {
    try {
      const [result] = await geocodeByPlaceId(placeId);

      const { address_components: addressComponents } = result;
      // Get values from google places address components array
      const streetNumType = 'street_number';
      const streetNameType = 'route';
      const subLocalityType = 'sublocality';
      const suburbType = 'locality';
      const stateType = 'administrative_area_level_1';
      let suburb = addressComponents
        .find((comp) => comp.types.includes(subLocalityType))
        ?.short_name?.replace(/\s/g, '-');
      suburb ||= addressComponents.find((comp) => comp.types.includes(suburbType)).short_name.replace(/\s/g, '-');
      const state = addressComponents.find((comp) => comp.types.includes(stateType)).short_name;
      const streetNumber = addressComponents.find((comp) => comp.types.includes(streetNumType))?.long_name;
      const streetName = addressComponents.find((comp) => comp.types.includes(streetNameType))?.long_name;

      let streetAddress = null;

      if (streetName) {
        streetAddress = streetNumber ? `${streetNumber} ${streetName}` : streetName;
      }

      // Save values to state
      setLoading(true);
      setValue(val);

      localStorage.setItem('address', val);

      // Push user to app on address selection
      if (streetAddress) {
        window.location.href = `${orderURL}/search/${
          category || 'office-catering'
        }/${state}/${suburb}?street_address=${streetAddress}`;
      } else {
        window.location.href = `${orderURL}/search/${category || 'office-catering'}/${state}/${suburb}`;
      }
    } catch (error) {
      console.log('Error fetching address', error);
    }
  };

  const handleChange = (val) => {
    setValue(val);
  };

  const handleFocus = () => {
    setValue('');
  };

  const handleBlur = () => {
    setValue(address || '');
  };

  const handleClick = (event) => {
    event.preventDefault();
    onSearchSubmit(value);
  };

  const handleSelect = (val, placeId) => {
    setAddress(val);
    onSearchSubmit(val, placeId);
  };

  const searchOptions = {
    componentRestrictions: { country: locality },
    types: ['geocode'],
  };

  const label = 'Enter street address, suburb or city';

  return (
    <>
      <h5 className="icon icon-large icon-right-spacer icon-truck">
        Delivering To {loading && <span className="searchbar-loading" />}
      </h5>
      <div className={headerClass}>
        <PlacesAutocomplete
          searchOptions={searchOptions}
          onChange={handleChange}
          onSelect={handleSelect}
          value={value || ''}
        >
          {({ getInputProps, suggestions, getSuggestionItemProps, loading: loadingSuggestions }) => (
            <>
              <input
                onFocus={handleFocus}
                {...getInputProps({
                  placeholder: label,
                  className: 'location-search-input',
                  onBlur: handleBlur,
                  disabled,
                })}
              />
              {(suggestions.length > 0 || loadingSuggestions) && (
                <div className="autocomplete-dropdown-container">
                  {loadingSuggestions && <div className="autocomplete-dropdown-item">Loading...</div>}
                  {suggestions.map((suggestion) => {
                    const className = `autocomplete-dropdown-item ${suggestion.active ? 'active' : ''} `;
                    return (
                      <div {...getSuggestionItemProps(suggestion, { className })}>
                        <span>{suggestion.description}</span>
                      </div>
                    );
                  })}
                </div>
              )}
            </>
          )}
        </PlacesAutocomplete>
        {!navigation && (
          <button type="button" className="button" disabled={loading || !isValid} onClick={handleClick}>
            {loading ? <span className="searchbar-loading" /> : null}
            {loading ? '....' : buttonText}
          </button>
        )}
      </div>
    </>
  );
};

export default SearchBar;
