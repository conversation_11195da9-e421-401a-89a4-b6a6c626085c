import { ErrorComponent, Layout } from 'components/Common';
import { ShowContainer } from 'components/LoadingDock';
import yordar from 'api/yordar';

import { DYNAMIC_LOADING_DOCK_ENDPOINT } from 'api/endpoints';

const LoadingDockPage = (props) => {
  if (props.errors) {
    return (
      <ErrorComponent
        text={props.errors.join('.')}
        redirect={props.redirect}
        redirectText="Back To Home"
        forDash={props.user?.type === 'customer'}
      />
    );
  }
  return <LoadingDock {...props} />;
};

const LoadingDock = ({ loadingDockData, user }) => {
  const {
    order,
    customer,
    loading_dock: loadingDock
  } = loadingDockData;

  return (
    <Layout
      seo={{ title: `Order Loading Dock - ${order.name}`, description: `Enter Loading Dock for order #${order.id}` }}
      bodyBackgroundColor="#f9f9f9"
      customClass="checkout-dash"
      forDash={user?.type === 'customer'}
      hideFooter
    >
      <div className="checkout">
        <div>
          <div>
            <ShowContainer
              order={order}
              customer={customer}
              loadingDock={loadingDock}
            />
          </div>
        </div>
      </div>
    </Layout>
  );
};

const getCookieHeader = (cookies) => (cookies ? { Cookie: cookies } : {});

const getCheckoutData = async (path, config) => {
  try {
    const { data } = await yordar.get(path, config);
    return data;
  } catch (error) {
    console.error(`Error fetching data from ${path}: `, error);
    throw error;
  }
};

export const getServerSideProps = async ({ req, params }) => {
  const { uuid } = params;
  const cookies = req?.headers?.cookie;
  const apiConfig = {
    withCredentials: true,
    headers: getCookieHeader(cookies),
  };

  try {
    const [loadingDockData] = await Promise.all([
      getCheckoutData(DYNAMIC_LOADING_DOCK_ENDPOINT(uuid), apiConfig),
    ]);
    return { props: { loadingDockData } };
  } catch (error) {
    if (error.response.status === 403) {
      return {
        props: {
          errors: ['You do not have access to this page'],
        },
      };
    }
    return {
      props: {
        ...(error?.response?.data?.errors && { errors: error.response.data.errors }),
        ...(error.response.data?.redirect_url && { redirect: error.response.data?.redirect_url }),
      },
    };
  }
};

export default LoadingDockPage;
