import useQuoteStore from 'store/useQuoteStore';
import { quoteSteps } from './utils/steps';
import QuoteProceedButton from './QuoteProceedButton';

const QuoteDetails = ({ type, uuid }) => {
  const { updateStep, step } = useQuoteStore((state) => ({
    updateStep: state.updateStep,
    step: state.step,
  }));

  function goBack(e) {
    e.preventDefault();
    if (step === 0) {
      let backLink = '/quotes';
      if (uuid) {
        backLink += `/${uuid}`;
      }
      window.location = backLink;
    } else {
      updateStep(false);
    }
  }

  const quoteStep = quoteSteps({ uuid })[type][step];

  return (
    <div className="authorization-module quote-content">
      <div>
        {quoteStep.button && !quoteStep.finished && (
          <a onClick={goBack} className="quote-back-link">
            Back
          </a>
        )}
        <h2 className="quote-title">{quoteStep.title}</h2>
        <div>{quoteStep.content}</div>
      </div>
      {quoteStep.button && (
        <QuoteProceedButton
          text={quoteStep.button}
          validateFields={quoteStep.validationFields}
          type={type}
          uuid={uuid}
        />
      )}
    </div>
  );
};

export default QuoteDetails;
