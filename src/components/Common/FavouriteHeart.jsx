import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

const FavouriteHeart = ({ id, isFavourite, changeFavouriteStatus, setFavouriteIDs }) => {
  const [heart, setHeart] = useState('empty');
  const [beatingHeart, setBeatingHeart] = useState(false);
  const dispatch = useDispatch();

  const heartOptions = isFavourite
    ? { currentStatus: 'full', oppositeStatus: 'empty', method: 'delete' }
    : { currentStatus: 'empty', oppositeStatus: 'full', method: 'put' };

  useEffect(() => {
    setHeart(heartOptions.currentStatus);
  }, [isFavourite]);

  return (
    <span
      className={`icon icon-heart-${heart} favourite-heart ${beatingHeart ? 'beating-heart' : ''}`}
      onMouseEnter={() => setHeart(heartOptions.oppositeStatus)}
      onMouseLeave={() => setHeart(heartOptions.currentStatus)}
      onClick={(e) =>
        changeFavouriteStatus({ e, id, method: heartOptions.method, dispatch, setBeatingHeart, setFavouriteIDs })
      }
      role="presentation"
    />
  );
};

export default FavouriteHeart;
