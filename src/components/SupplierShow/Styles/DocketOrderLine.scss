.supplier-order-lines {
  display: flex;
  padding: 0.5rem 0.25rem;
  margin-bottom: 0.5rem;
  line-height:1.125rem;
  &:first-child {
    margin-top: 0;
  }
  &.loading {
    background: #ededed;
    input {
      background: #ededed;
    }
  }
  &.errored {
    .error-message {
      color: #f82600;
    }
    input {
      border: 2px solid #f82600;
    }
  }
  input {
    width: 42px;
    height: 30px;
    text-align: center;
    padding: 0;
    font-family: $heading-font;
    box-shadow: none;
    &:focus {
      border-color: black;
    }
  }
  span {
    font-size: 14px;
    &:last-of-type {
      margin-left: auto;
    }
  }
}

.orderline-name-and-note {
  margin: 0 10px;
  flex: 1;
  .orderline-note {
    font-size: 12px;
    color: #9b9b9b;
  }
  p { 
    font-size: 14px;
    margin-bottom: 0px;
  }
}

.orderline-price {
  margin-left: auto;
  margin-right: 16px;
}

.orderline-delete {
  cursor: pointer;
}
