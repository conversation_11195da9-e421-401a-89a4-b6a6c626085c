import { Layout, SearchBar } from 'components/Common';
import Image from 'next/image';
import getBaseURL from 'utils/getBaseURL';

const nextJSLogo = require('images/nextjs.svg');
const LogoDark = require('images/logo-dark.svg');

const Home = () => (
  <Layout seo={{ title: 'Yordar Next Application' }}>
    <div className="home-container">
      <div className="logos-and-search">
        <div className="home-logos">
          <div>
            <Image src={nextJSLogo} alt="next-js" />
          </div>
          <div>
            <Image src={LogoDark} width={300} height={86} alt="yordar-logo" />
          </div>
        </div>
        <SearchBar />
      </div>
    </div>
  </Layout>
);
export const getServerSideProps = async (context) => {
  const host = context.req?.headers?.host;
  if (process.env.NEXT_PUBLIC_REDIRECT_HOME_PAGE && process.env.NODE_ENV === 'production') {
    return {
      redirect: {
        destination: getBaseURL({ reqHost: host, type: 'marketing' }),
        permanent: false,
      },
    };
  }
  return { props: { ok: true } };
};

export default Home;
