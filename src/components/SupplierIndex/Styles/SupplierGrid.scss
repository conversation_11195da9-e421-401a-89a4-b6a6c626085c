.listing-grid__supplier-count {
  font-weight: bold;
  margin: 6px 0 14px;
  color: #757575;
}
.listing-grid-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  grid-gap: 18px;
  transition: height 0.3s;
  .alice-carousel__stage-item {
    padding: 0 10px;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.listing-grid-item {
  position: relative;
  animation: fadeIn 0.6s;
  box-shadow: 0px 1px 8px #e8e8e8;
  border-radius: 6px;
  background: white;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  &:hover {
    text-decoration: none;
    .listing-grid-card-image {
      .image-container {
        img {
          transform: scale(1.02);
        }
      }
    }
  }
}

.listing-grid-item__link {
  position: absolute;
  top: 0; 
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 1;
}

.listing-grid-card-image {
  position: relative;
  .image-container {
    max-height: 160px;
    overflow: hidden;
    img {
      transition: all 0.5s;
    }
  }
  .listing-grid-card-label {
    @include heading-font();
    position: absolute;
    top: 10px;
    right: 10px;
    line-height: 1em;
    color: $white;
    font-size: 13px;
    text-transform: uppercase;
    padding: 4px 8px;
    border-radius: 3px;
    &--indigenous {
        background: url(../../../images/icons/indigenous.png);
      }
    &--featured {
      background: $primary;
    }
    &--new {
      background: $highlight;
    }
  }
  .listing-grid-card-custom-price {
    position: absolute;
    top: 0;
    left: 0;
    background: black;
    opacity: 0.75;
    color: white;
    text-transform: uppercase;
    font-size: 11px;
    padding: 4px;
    border-radius: 4px;
    &::before {
      content: '';
      display: inline-block;
      background: url('../../../images/icons/star.svg');
      width: 13px;
      height: 13px;
      margin-right: 4px;
    }
  }
  .supplier-icons {
    position: absolute;
    bottom: 15px;
    display: flex;
  }
  .supplier-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0;
    background: $white;
    width: 42px;
    height: 42px;
    border-radius: 50%;
    margin-right: -4px;
    border-color: #9c9c9c;
    &:before {
      width: 18px;
      height: 18px;
    }
    &.show-count {
      @include heading-font;
      background: $tertiary;
      // color: $white;
      font-size: 16px;
      line-height: 1em;
    }
  }
  .listing-grid-card-min {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba($white, 0.8);
    font-size: 12px;
    text-align: center;
    line-height: 1.4em;
    padding: 4px 15px 10px;
    border-radius: 3px;
    strong {
      @include heading-font();
      display: block;
      font-size: 20px;
    }
  }
}

.listing-grid-item-body {
  padding: 10px 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .listing-grid-item-name-and-heart {
    position: relative;
    display: flex;
    justify-content: space-between;
    .favourite-heart {
      cursor: pointer;
      z-index: 3;
    }
  }
  .listing-grid-item-name {
    @include heading-font;
    font-weight: 700;
  }
  .listing-grid-item-meta {
    font-size: 14px;
    color: rgba($textcolor, 0.6);
    @include ellipsis;
    display: block;
    .listing-grid-item-meta-item {
      &:before {
        content: '•';
        margin: 0 5px 0;
      }
      &:first-of-type {
        &:before {
          display: none;
        }
      }
    }
  }
}

.listing-grid-items {
  &.loading {
    .listing-grid-card {
      height: 220px;
      @include shimmer;
    }
    .listing-grid-item {
      .listing-grid-item-meta, .listing-grid-item-about {
        display: inline-block;
        width: 100%;
        height: 18px;
        @include shimmer;
      }
    }
  }
}

.icons-tooltip {
  position: absolute;
  left: 12px;
  top: 45px;
  color: white;
  background: black;
  width: 150px;
  z-index: 11;
  text-transform: none;
  padding: 6px;
  border-radius: 4px;
  font-size: 12px;
  li {
    line-height: 2;
    list-style-type: disc;
  }
  &.dietary {
    background: #ffffff;
    color: black;
    left: 0px;
    top: 26px;
    width: 100px;
    border: 1px solid black;
    padding: 4px;
    text-align: center;
  }
}