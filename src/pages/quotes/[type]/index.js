import { QuoteImageSection, QuoteDetails } from 'components/Quotes';
import { Layout } from 'components/Common';
import { quotesSEO, cateringQuoteSEO, eventQuoteSEO, snacksQuoteSEO } from 'utils/seo';
import Error404 from 'pages/404';
import yordar from 'api/yordar';
import { DYNAMIC_QUOTE_CUSTOMER_ENDPOINT } from 'api/endpoints';

const quoteSEO = {
  catering: cateringQuoteSEO,
  snacks: snacksQuoteSEO,
  event: eventQuoteSEO,
};

const Quotes = ({ user, type, isValid, uuid, quoteCustomer }) => {
  const forDash = user?.type === 'customer';

  if (!isValid) {
    return <Error404 />;
  }

  if (quoteCustomer) {
    return (
      <Layout seo={quotesSEO} forDash={forDash} customClass={forDash ? 'quotes-dash' : ''} type="quotes-dash">
        <div className="quote-container">
          <div className="auth-container" style={{ padding: '' }}>
            <div className="auth-card">
              <QuoteImageSection type="start" uuid={uuid} quoteCustomer={quoteCustomer} />
              <QuoteDetails type="start" uuid={uuid} />
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  const seo = quoteSEO[type];
  return (
    <Layout seo={seo} forDash={forDash} customClass={forDash ? 'quotes-dash' : ''} type="quotes-dash">
      <div className="quote-container">
        <div className="auth-container" style={{ padding: '' }}>
          <div className="auth-card">
            <QuoteImageSection type={type} />
            <QuoteDetails type={type} />
          </div>
        </div>
      </div>
    </Layout>
  );
};

export const getServerSideProps = async ({ params }) => {
  const { type } = params;
  const validQuoteTypes = ['catering', 'snacks', 'event'];
  const isValidQuoteType = validQuoteTypes.includes(type);
  const isValidUUID = !isValidQuoteType && type.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);
  let quoteCustomer = null;
  if (isValidUUID) {
    const { data } = await yordar.get(DYNAMIC_QUOTE_CUSTOMER_ENDPOINT(type));
    quoteCustomer = data;
  }
  return {
    props: {
      type,
      quoteCustomer,
      isValid: isValidQuoteType || isValidUUID,
      uuid: isValidUUID && type,
    },
  };
};

export default Quotes;
