import useEmployeeSurveyStore from 'store/useEmployeeSurveyStore';

const NameQuestion = () => {
  const { employeeSurveySubmission, updateStep, updateSubmission } = useEmployeeSurveyStore((state) => ({
    employeeSurveySubmission: state.employee_survey_submission,
    updateStep: state.updateStep,
    updateSubmission: state.updateSubmission,
  }));

  const handleKeyUp = (event) => {
    if (event.which === 13 || event.keyCode === 13) updateStep(true);
  };

  return (
    <input
      type="text"
      className="survey-question"
      placeholder="Leave blank to stay Anonymous"
      name="name"
      value={employeeSurveySubmission.name || ''}
      onKeyUp={handleKeyUp}
      onChange={updateSubmission}
    />
  );
};

export default NameQuestion;
