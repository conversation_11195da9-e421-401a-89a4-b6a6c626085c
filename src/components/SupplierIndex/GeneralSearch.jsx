import { useContext, useState } from 'react';
import { useSelector } from 'react-redux';
import DebounceInput from 'react-debounce-input';

import yordar from 'api/yordar';
import { UserContext } from 'context/user';
import { SEARCH_MENU_ITEMS_ENDPOINT } from 'api/endpoints';
import { OutsideAlerter, Spinner } from 'components/Common';
import { SearchResults } from 'components/SupplierIndex';
import useCategoriesStore from 'store/useCategoriesStore';

const GeneralSearch = ({ categoryPage, mobile, forDash }) => {
  const storeCategories = useCategoriesStore((state) => state.categories);
  const initialSuppliers = useSelector((state) => state.suppliers.index);
  const { user } = useContext(UserContext);
  const [searchValue, setSearchValue] = useState('');
  const [searchResults, setSearchResults] = useState({ categories: [], suppliers: [], menuItems: [] });
  const [loading, setLoading] = useState(false);

  function categoriesForSearch(categoriesToFilter) {
    if (!categoriesToFilter) return [];
    const {
      'catering-services': catering = [],
      'kitchen-supplies': kitchen = [],
      dietary = [],
      other = [],
      social = [],
    } = categoriesToFilter;
    switch (categoryPage) {
      case 'office-catering':
        return [...catering, ...dietary, ...other, ...social];
      case 'office-snacks':
        return [...kitchen, ...other, ...social];
      default:
        return [];
    }
  }

  function customerRestrictedSupplierIDs(suppliers) {
    return suppliers
      .filter((supplier) => !supplier.visible_to || supplier?.visible_to?.includes(user?.profile?.id))
      .map((supplier) => supplier.id);
  }

  async function handleSearch(e, userProfileID) {
    setSearchValue(e.target.value);
    setLoading(true);
    if (e.target.value.length < 3) return setSearchResults({ categories: [], suppliers: [], menuItems: [] });
    const { data: matchedMenuItems } = await yordar.get(SEARCH_MENU_ITEMS_ENDPOINT, {
      params: { query: e.target.value, supplier_ids: customerRestrictedSupplierIDs(initialSuppliers, userProfileID) },
      withCredentials: true,
    });
    let matchedSuppliers;

    // Upon request from supplier Hut Dog, special exception for returning them when user searches hotdog
    if (e.target.value.toLowerCase() === 'hot dog' || e.target.value.toLowerCase() === 'hotdog') {
      matchedSuppliers = initialSuppliers.filter((sup) => sup.name.toLowerCase().includes('hut dog'));
    } else {
      matchedSuppliers = initialSuppliers.filter((sup) =>
        sup.name.toLowerCase().includes(e.target.value.toLowerCase())
      );
    }
    const categoriesForSearchResult = categoriesForSearch(storeCategories);
    const matchedCategories = categoriesForSearchResult.filter((category) =>
      category.toLowerCase().includes(e.target.value.toLowerCase())
    );
    setSearchResults({ categories: matchedCategories, suppliers: matchedSuppliers, menuItems: matchedMenuItems });
    setLoading(false);
  }
  return (
    <OutsideAlerter className={`elastic-search-group active ${mobile ? 'mobile' : ''} ${forDash ? 'dash' : ''}`}>
      <DebounceInput
        type="text"
        placeholder="Suppliers, cuisines, snacks"
        value={searchValue}
        className="elastic-search"
        onChange={(e) => handleSearch(e, user?.profile?.id)}
        debounceTimeout={300}
      />
      {searchValue?.length >= 3 && (
        <div className={`elastic-search-results ${loading ? 'loading' : ''}`}>
          {!loading && <SearchResults searchResults={searchResults} searchValue={searchValue} />}
          {loading && <Spinner />}
        </div>
      )}
    </OutsideAlerter>
  );
};

export default GeneralSearch;
