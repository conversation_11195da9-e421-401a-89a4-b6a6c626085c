import { useState } from 'react';
import { shallow } from 'zustand/shallow';
import Modal from 'react-responsive-modal';

import { MealPlanDetailsModal, MealPlanExplanationModal } from 'components/Checkout';
import useCheckoutStore from 'store/useCheckoutStore';

const DETAIL_MAPPINGS = {
  name: 'name',
  number_of_people: 'numberOfPeople',
  cpo_id: 'cpoId',
  gst_free_cpo_id: 'gstFreeCpoId',
  department_identity: 'departmentIdentity',
  credit_card_id: 'creditCardId',
};

const DELIVERY_MAPPINGS = {
  delivery_address_level: 'deliveryAddressLevel',
  delivery_address: 'deliveryAddress',
  delivery_suburb_id: 'deliverySuburbId',
  suburb_label: 'deliverySuburbLabel',
  delivery_instruction: 'deliveryInstruction',
};

const OrderMealPlan = ({ isEditPage }) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState(null);

  const { order, setOrderDetail, mealPlans } = useCheckoutStore(
    (state) => ({
      order: state.order,
      setOrderDetail: state.setOrderDetail,
      mealPlans: state.checkout?.meal_plans,
    }),
    shallow
  );

  const updateOrderDetails = (mealPlan) => {
    Object.entries(DETAIL_MAPPINGS).forEach(([apiField, storeKey]) => {
      setOrderDetail(storeKey, mealPlan[apiField]);
    });

    if (order.deliverySuburbId === mealPlan.delivery_suburb_id) {
      Object.entries(DELIVERY_MAPPINGS).forEach(([apiField, storeKey]) => {
        setOrderDetail(storeKey, mealPlan[apiField]);
      });
    }
  };

  const handleMealPlanSelect = (event) => {
    const mealPlan = mealPlans?.find((plan) => Number(plan.id) === Number(event.target.value));

    setOrderDetail('mealPlanId', mealPlan?.id);

    if (mealPlan?.id) {
      setModalContent(
        <MealPlanDetailsModal
          order={order}
          mealPlan={mealPlan}
          setOrderDetailsChange={(shouldUpdate) => {
            if (shouldUpdate) updateOrderDetails(mealPlan);
            setModalOpen(false);
          }}
          setModalOpen={setModalOpen}
        />
      );
      setModalOpen(true);
    }
  };

  const selectedMealPlanId = order.mealPlanId?.toString() || '';

  return (
    <>
      <div className={`checkout-detail full-width ${isEditPage ? 'order-details--edit' : ''}`}>
        <p className="title">
          Does this order belong in a Meal Plan (optional){' '}
          <span
            className="whats-this"
            onClick={() => {
              setModalOpen(true);
              setModalContent(<MealPlanExplanationModal setModalOpen={setModalOpen} />);
            }}
          >
            What's This?
          </span>
        </p>

        <select
          value={selectedMealPlanId}
          onChange={handleMealPlanSelect}
          className="input-icon meal-plan checkout-input"
        >
          <option value="">Select Meal Plan</option>
          {mealPlans?.map((plan) => (
            <option key={plan.id} value={plan.id.toString()}>
              {plan.name}
            </option>
          ))}
        </select>
      </div>

      <Modal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        center
        styles={{ modal: { width: '500px', borderRadius: '4px' } }}
        showCloseIcon={false}
        closeOnOverlayClick={false}
        animationDuration={100}
      >
        {modalContent}
      </Modal>
    </>
  );
};

export default OrderMealPlan;
