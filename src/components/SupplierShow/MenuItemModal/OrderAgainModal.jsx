import { useContext, useRef, useState } from 'react';
import Image from 'next/image';
import 'react-responsive-modal/styles.css';

import useDocketStore from 'store/useDocketStore';
import useModalStore from 'store/useModalStore';

import { UserContext } from 'context/user';
import { MAXIMUM_MODAL_HEIGHT } from 'utils/constants';

const OrderAgainModal = ({ item }) => {
  const modalRef = useRef();

  const { user } = useContext(UserContext);
  const [selectedLines, setSelectedLines] = useState(
    item.order_lines.reduce((acc, line) => ({ ...acc, [line.id]: !line.not_available }), {})
  );
  const [loading, setLoading] = useState(false);

  const { addOrderLineToCart } = useDocketStore((state) => ({
    addOrderLineToCart: state.addOrderLineToCart,
  }));

  const addBoxShadowFooter = modalRef?.current?.scrollHeight > MAXIMUM_MODAL_HEIGHT;

  const setModalOpen = useModalStore((state) => state.setModalOpen);

  const handleLineToggle = (id) => {
    const orderLine = item.order_lines.find((line) => line.id === id);

    if (orderLine?.not_available) {
      return;
    }

    setSelectedLines((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // Calculate total price of selected order lines
  const calculateTotalPrice = () => {
    if (Object.values(selectedLines).every((v) => !v)) {
      return '0.00';
    }

    const selectedOrderLines = item.order_lines.filter((line) => selectedLines[line.id] && !line.not_available);
    return selectedOrderLines
      .reduce((total, line) => {
        // Parse the line_total as a number, removing the $ if present
        const lineTotal = parseFloat(line.line_total.toString().replace('$', ''));
        return total + lineTotal;
      }, 0)
      .toFixed(2);
  };

  const handleAddToCart = async () => {
    try {
      setLoading(true);

      const selectedOrderLines = item.order_lines.filter((line) => selectedLines[line.id] && !line.not_available);

      const orderLines = selectedOrderLines.map((line) => ({
        item_id: line.menu_item_id,
        quantity: line.quantity,
        note: line.note || '',
        company_id: user?.profile?.company_id,
        ...(line.serving_size_id && { serving_size_id: line.serving_size_id }),
        ...(line.selected_menu_extra_ids?.length && { selected_menu_extra_ids: line.selected_menu_extra_ids }),
      }));

      // Add to cart
      await addOrderLineToCart(orderLines);
      setLoading(false);
      setModalOpen(false);
    } catch (error) {
      console.error('Error adding items to cart:', error);
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="modal-header">
        <h2 className="menu-item-modal-title">{item.name}</h2>
      </div>
      <p style={{ marginLeft: '24px' }}>Last ordered for {item.delivery_date}</p>
      <div className="menu-item-modal-details" ref={modalRef}>
        {item.order_lines.map((orderLine) => (
          <div key={orderLine.id} className={`order-again-modal-line ${orderLine.not_available ? 'disabled' : ''}`}>
            {orderLine.image && (
              <Image src={orderLine.image} alt="menu-item-image" width={40} height={40} quality={2} transform="fill" />
            )}
            {!orderLine.image && <div className="no-orderline-image">{orderLine.name.charAt(0)}</div>}
            <div style={{ flex: 1 }}>
              <p>
                <strong style={{ fontWeight: '900', paddingRight: '4px' }}>{orderLine.quantity}x </strong>
                {orderLine.name.length > 40 ? `${orderLine.name.slice(0, 40)}...` : orderLine.name}
                {orderLine.not_available && (
                  <span style={{ color: '#999', fontStyle: 'italic' }}> (Not Available)</span>
                )}
              </p>
              {orderLine.selected_extras && (
                <p style={{ fontSize: '12px', color: 'grey', maxWidth: '340px', lineHeight: '14px' }}>
                  {orderLine.selected_extras.join(', ')}
                </p>
              )}
              {orderLine.note && (
                <p style={{ fontSize: '12px', color: 'grey', maxWidth: '340px', lineHeight: '14px', marginTop: '4px' }}>
                  {orderLine.note}
                </p>
              )}
            </div>
            <p style={{ marginLeft: 'auto', padding: '0 12px' }}>${orderLine.line_total}</p>
            <label
              htmlFor={`line-toggle-${orderLine.id}`}
              className={`set-orderline ${selectedLines[orderLine.id] ? 'checked' : ''} ${
                orderLine.not_available ? 'disabled' : ''
              }`}
            >
              <input
                id={`line-toggle-${orderLine.id}`}
                checked={selectedLines[orderLine.id]}
                type="checkbox"
                disabled={orderLine.not_available}
                onChange={() => handleLineToggle(orderLine.id)}
              />
            </label>
          </div>
        ))}
      </div>

      <div className={`menu-item-modal-add padded${addBoxShadowFooter ? ' shadow' : ''}`}>
        <button
          className={`button ${Object.values(selectedLines).every((v) => !v) || loading ? 'disabled' : ''}`}
          type="button"
          onClick={handleAddToCart}
          disabled={Object.values(selectedLines).every((v) => !v) || loading}
        >
          <span>
            {Object.values(selectedLines).some((v) => v) ? `Add to Cart - $${calculateTotalPrice()}` : 'Add to Cart'}
          </span>
        </button>
      </div>
    </div>
  );
};

export default OrderAgainModal;
