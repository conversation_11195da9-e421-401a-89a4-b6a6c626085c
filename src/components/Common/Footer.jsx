import { Link, Logo, Newsletter, SocialLinks } from 'components/Common';
import { HostContext } from 'context/host';
import { useContext } from 'react';
import { footer as footerLinks } from 'utils/navigations';

const FooterNavigationLink = ({ className, to, text }) => (
  <li>
    <Link className={className} href={to}>
      {text}
    </Link>
  </li>
);

const Footer = () => {
  const { marketingURL } = useContext(HostContext);
  const footerNav = footerLinks(marketingURL);
  return (
    <footer className="footer">
      <div className="wrapper">
        <section className="footer-links">
          <div className="footer-links-col">
            <nav className="footer-links-nav">
              <h5>
                <Link href={`${marketingURL}/office-catering`}>Office Catering</Link>
              </h5>
              <ul className="footer-links-list">
                {footerNav.catering.map((link) => (
                  <FooterNavigationLink key={link.to + link.icon} {...link} />
                ))}
              </ul>
            </nav>
          </div>
          <div className="footer-links-col">
            <nav className="footer-links-nav">
              <h5>
                <Link href={`${marketingURL}/kitchen-and-pantry`}>Kitchen & Pantry</Link>
              </h5>
              <ul className="footer-links-list">
                {footerNav.pantry.map((link) => (
                  <FooterNavigationLink key={link.to + link.icon} {...link} />
                ))}
              </ul>
            </nav>
          </div>
          <div className="footer-links-col something-else">
            <nav className="footer-links-nav">
              <h5>Something Else?</h5>
              <ul className="footer-links-list">
                {footerNav.somethingElse.map((link) => (
                  <FooterNavigationLink key={link.to + link.icon} {...link} />
                ))}
              </ul>
            </nav>
            <nav className="footer-links-nav about-help">
              <h5>About / Help</h5>
              <ul className="footer-links-list">
                {footerNav.help.map((link) => (
                  <FooterNavigationLink key={link.to + link.icon} {...link} />
                ))}
              </ul>
            </nav>
          </div>
          <div className="footer-links-col footer-locations">
            <nav className="footer-links-nav">
              <h5>Where we Deliver</h5>
              <ul className="footer-links-list">
                {footerNav.locations.map((link) => (
                  <FooterNavigationLink key={link.to + link.icon} {...link} />
                ))}
              </ul>
            </nav>
          </div>
        </section>
        <section className="footer-right">
          <Logo theme="dark" />
          <Newsletter />
          <SocialLinks />
          <div className="footer-sitemap">
            <nav className="footer-sitemap-nav">
              <Link href="/terms">Terms & Privacy</Link>
              <Link href="/sitemap.xml">Sitemap</Link>
            </nav>
            <span className="footer-sitemap-legal">
              © {new Date().getFullYear()} Your Order PTY LTD. All rights reserved.
            </span>
          </div>
        </section>
      </div>
    </footer>
  );
};

export default Footer;
