import useQuoteStore from 'store/useQuoteStore';
import QuoteCheckbox from './QuoteCheckbox';

const DietaryAllergiesForm = ({ potentialAllergies, dietaryPreferences }) => {
  const { dietaries, allergies, notListedAllergyOrDietary, setMultiValueField, setFormField } = useQuoteStore(
    (state) => ({
      dietaries: state.multiValueFields.dietaries,
      allergies: state.multiValueFields.allergies,
      setMultiValueField: state.setMultiValueField,
      notListedAllergyOrDietary: state.formFields.notListedAllergyOrDietary,
      setFormField: state.setFormField,
    })
  );

  return (
    <>
      <p className="quote-label">Potential Allergies</p>
      <div className="checkbox-grid">
        {potentialAllergies.map((allergy) => (
          <QuoteCheckbox
            value={allergy}
            label={allergy}
            key={allergy}
            onClick={() => setMultiValueField('allergies', allergy)}
            checked={allergies.includes(allergy)}
          />
        ))}
      </div>
      <p className="quote-label">Dietary Preferences</p>
      <div className="checkbox-grid">
        {dietaryPreferences.map((dietary) => (
          <QuoteCheckbox
            value={dietary}
            label={dietary}
            key={dietary}
            onClick={() => setMultiValueField('dietaries', dietary || dietary)}
            checked={dietaries.includes(dietary)}
          />
        ))}
      </div>
      <h3 className="quote-sub-heading">Did we miss something?</h3>
      <textarea
        className="quote-textarea"
        placeholder="Please let us know if there's a allergy or dietary concern that we missed"
        value={notListedAllergyOrDietary.value}
        onChange={(e) => setFormField('notListedAllergyOrDietary', e.target.value)}
      />
    </>
  );
};

export default DietaryAllergiesForm;
