/* eslint-disable */ 

// Format Slug to readable format
// ex. 'sydney-nsw-catering' => 'Sydney NSW Catering'
if (!String.prototype.humanize) {
  String.prototype.humanize = function (str) {
    if (!str) str = this;

    return str
      .split('-')
      .map((word) => `${word[0].toUpperCase()}${word.slice(1)}`)
      .join()
      .replace(/,/g, ' ');
  };
}

// Capitalize each letter in a string
if (!String.prototype.titleize) {
  String.prototype.titleize = function (str) {
    if (!str) str = this;

    return str
      .split(' ')
      .map((word) => `${word[0].toUpperCase()}${word.slice(1)}`)
      .join();
  };
}
