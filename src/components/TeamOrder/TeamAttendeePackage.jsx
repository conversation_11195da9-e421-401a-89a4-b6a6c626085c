import { useState } from 'react';
import axios from 'axios';
import Modal from 'react-responsive-modal';
import 'react-responsive-modal/styles.css';

const TeamAttendeePackage = ({ packageOrder }) => {
  const attendeeStatus = packageOrder.team_order_attendee.status;
  const [attendeeUnsubscribed, setAttendeeUnsubscribed] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const orderLines = packageOrder?.team_order_attendee?.order_lines;
  const attendeeOrdered = attendeeStatus === 'ordered';
  const hasDeclined = attendeeStatus === 'declined' || attendeeUnsubscribed;
  const expired = packageOrder.cutoff.has_expired;

  async function unsubscribeAttendee(e) {
    e.preventDefault();
    const { data } = await axios({
      method: 'POST',
      url: packageOrder.team_order_attendee.unsubscribe_url,
    });
    if (data.success) {
      setAttendeeUnsubscribed(true);
    }
  }

  function handleDeclineClick(e) {
    if (!hasDeclined && !expired && e.isTrusted) {
      setShowConfirmModal(true);
    }
  }

  function handleConfirmDecline() {
    setShowConfirmModal(false);
    const syntheticEvent = { preventDefault: () => {} };
    unsubscribeAttendee(syntheticEvent);
  }

  function getAttendeeCardOptions() {
    if (expired && !attendeeOrdered) {
      return { class: 'expired', declineButton: 'Order Passed' };
    }
    if (hasDeclined) {
      return { class: 'declined', declineButton: 'Not Attending' };
    }
    if (!expired && !attendeeOrdered) {
      return { class: 'invited', declineButton: "I'm not attending" };
    }
    if (attendeeOrdered && !expired) {
      return { class: 'ordered', declineButton: "I'm not attending" };
    }
    if (attendeeOrdered && expired) {
      return { class: 'past-order', declineButton: 'Order Passed' };
    }
  }

  const options = getAttendeeCardOptions();

  return (
    <div className="team-attendee-day">
      <p className={`expiry-text ${options.class}`}>{packageOrder.delivery_at}</p>
      <div className="team-attendee-card">
        <div className="team-attendee-supplier-image">
          <img src={packageOrder.suppliers[0].image} alt={packageOrder.suppliers[0].name} />
        </div>
        <div className="team-attendee-supplier-card__details">
          <p style={{ fontWeight: 'bold', marginBottom: '4px' }}>{packageOrder.suppliers[0].name}</p>
          {!hasDeclined && (
            <p className="team-orderline-info">{orderLines.map((ol) => `${ol.quantity}x ${ol.name}`).join(', ')}</p>
          )}
          <div className="expiry-container">
            {!attendeeOrdered && !hasDeclined && <p>{packageOrder.expiry_options.text}</p>}
            {hasDeclined && <p className="declined">You Declined Your Order</p>}
          </div>
        </div>
        <div className="team-attendee-button-container">
          <button
            type="button"
            className={`button ${options.class}`}
            onClick={handleDeclineClick}
            style={{ height: '100%' }}
          >
            {options.declineButton}
          </button>
          {!expired && (
            <a className={`button ${expired ? 'gray-btn' : ''}`} href={packageOrder.team_order_attendee.order_url}>
              {attendeeOrdered ? 'Edit Order' : 'Order'}
            </a>
          )}
        </div>
      </div>
      <Modal
        open={showConfirmModal}
        center
        styles={{
          modal: { maxWidth: '500px', padding: '20px', borderRadius: '10px' },
          closeButton: { cursor: 'pointer', marginLeft: '6px' },
        }}
        showCloseIcon={false}
        onClose={() => {}}
      >
        <div className="decline-confirmation">
          <h3 style={{ marginBottom: '16px' }}>Decline Order Confirmation</h3>
          <p style={{ marginBottom: '20px', lineHeight: '1.5' }}>
            Are you sure you want to decline your order from <strong>{packageOrder.suppliers[0].name}</strong> for{' '}
            <strong>{packageOrder.delivery_at}</strong>?
          </p>
          <div
            className="decline-confirmation-buttons"
            style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}
          >
            <button
              type="button"
              className="button black"
              onClick={() => setShowConfirmModal(false)}
              style={{ minWidth: '80px' }}
            >
              Cancel
            </button>
            <button
              type="button"
              className="button"
              onClick={handleConfirmDecline}
              style={{ backgroundColor: '#dc2454', color: 'white', minWidth: '120px', border: 'none' }}
            >
              Yes, Decline Order
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default TeamAttendeePackage;
