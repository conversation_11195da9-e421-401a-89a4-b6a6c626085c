import useCheckoutStore from 'store/useCheckoutStore';

const DeliveryDate = ({ handleButtonClick, isWoolworthsOrder }) => {
  const {
    order: { deliveryAtDisplayTime, deliveryAtDisplayDate, deliveryAt, deliveryWindowText },
    validations: { deliveryAt: deliveryAtValidation },
  } = useCheckoutStore();
  if (!isWoolworthsOrder) {
    return (
      <div className={`modal-section${deliveryAtValidation === false ? ' invalid' : ''}`}>
        <div className="checkout-row icon icon-extra-large icon-alarm">
          {deliveryAtDisplayTime && deliveryAtDisplayDate ? (
            <div>
              <span className="bold">{deliveryAtDisplayTime}</span>
              <p style={{ lineHeight: '16px' }}>{deliveryAtDisplayDate}</p>
            </div>
          ) : (
            <p className="bold">Please set a delivery time</p>
          )}
        </div>
        <button
          type="button"
          className={`checkout-button button ${deliveryAt ? 'white' : 'black'} rounded ${
            deliveryAtValidation === false ? 'invalid--button' : ''
          }`}
          onClick={() => handleButtonClick('date')}
        >
          {deliveryAt ? 'Change' : 'Add'}
        </button>
      </div>
    );
  }
  return (
    <div className="modal-section">
      <div className="checkout-row icon icon-extra-large icon-alarm">
        <div>
          <p className="bold">Woolworths Delivery Window:</p>
          <p>{deliveryWindowText}</p>
        </div>
      </div>
      <button
        type="button"
        className={`checkout-button button ${deliveryAt ? 'white' : 'black'} rounded`}
        onClick={() => handleButtonClick('deliveryWindow')}
      >
        Change
      </button>
    </div>
  );
};

export default DeliveryDate;
