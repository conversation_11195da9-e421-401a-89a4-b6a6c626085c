const SupplierIndexDefault = () => null;

function getCookieValue(cookieString, name) {
  if (!cookieString || !name) return null;

  const match = cookieString.match(`(^|;)\\s*${name}\\s*=\\s*([^;]+)`);
  return match ? match[2] : null;
}

export const getServerSideProps = async ({ req, params }) => {
  const { category } = params;
  const cookies = req?.headers?.cookie;
  const state = getCookieValue(cookies, 'yordar_state');
  const suburb = getCookieValue(cookies, 'yordar_suburb');
  if (state && suburb) {
    return {
      redirect: {
        destination: `/search/${category}/${state}/${suburb}`,
        permanent: false,
      },
    };
  }
  return { notFound: true };
};

export default SupplierIndexDefault;
