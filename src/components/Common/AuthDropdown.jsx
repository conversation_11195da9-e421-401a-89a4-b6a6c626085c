import { useContext, useEffect, useRef } from 'react';
import { HostContext } from 'context/host';

const AuthDropdown = ({ user, setIsDropdownOpen }) => {
  const { appURL } = useContext(HostContext);

  const logout = (e) => {
    e.preventDefault();
    document.getElementById('logout-form-dropdown').submit();
  };

  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="auth-dropdown-tooltip" ref={dropdownRef}>
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        {user.signed_in_as_admin && (
          <a className="auth-dropdown" href={`${appURL}/admin`}>
            Admin Dash
          </a>
        )}
        <a href={`${appURL}/c_profile/account-and-billing`} className="auth-dropdown">
          Account Details
        </a>
        <form id="logout-form-dropdown" className="auth-dropdown" action={`${appURL}/logout`} method="post">
          <input name="_method" type="hidden" value="delete" />
          <a href="logout" onClick={logout}>
            Logout
          </a>
        </form>
      </div>
    </div>
  );
};

export default AuthDropdown;
