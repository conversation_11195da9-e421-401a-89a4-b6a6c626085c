import useCheckoutStore from 'store/useCheckoutStore';

const OrderTotals = () => {
  const { order, orders, activeOrderID } = useCheckoutStore();

  const activeTotals = orders?.[activeOrderID]?.totals;
  const noDiscount = !activeTotals?.discount || activeTotals?.discount === '$0.00';
  const noTopup = !activeTotals?.topup || activeTotals?.topup === '$0.00';

  const discountLabel = order?.promotion ? `${order.promotion.name} (${order.promotion.discount_note})` : 'Discount';

  if (!activeTotals) {
    return <p>No Totals</p>;
  }
  return (
    <div className="checkout-docket-section">
      <PriceUnit className="grey" label="Subtotal" value={activeTotals.subtotal} />
      <PriceUnit className="grey" label="Delivery" value={activeTotals.delivery} />
      <PriceUnit className="primary" label={discountLabel} value={`-${activeTotals.discount}`} hideLabel={noDiscount} />
      <PriceUnit className="grey" label="GST" value={activeTotals.gst} />
      <PriceUnit className="primary" label="Topup" value={activeTotals.topup} hideLabel={noTopup} />
      <PriceUnit className="bold total" label="Total" value={activeTotals.total} />
    </div>
  );
};

const PriceUnit = ({ className, label, value, hideLabel }) => {
  if (hideLabel || !value) return null;
  return (
    <div className={`totals-value ${className}`}>
      <p>{label}</p>
      <p>{value}</p>
    </div>
  );
};

export default OrderTotals;
