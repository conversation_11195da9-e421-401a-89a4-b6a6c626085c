import useCheckoutStore from 'store/useCheckoutStore';

const ApproveOrderModal = ({ setModalOpen, awaitUserChoice }) => {
  const { order, orderSuppliers } = useCheckoutStore((state) => ({
    order: state.orders[state.order.id],
    orderSuppliers: state.orderSuppliers,
  }));

  const supplierNames = orderSuppliers ? Object.values(orderSuppliers).map((supplier) => supplier.name) : [];

  return (
    <div>
      <h3 className="section-heading">Approve Quote Order</h3>
      <p>
        Are you sure you want to Approve this order?
        <br />
        <strong>{supplierNames.length > 1 ? 'Suppliers' : 'Supplier'}</strong>: {supplierNames.join(', ')}
        <br />
        <strong>Order total:</strong> {order.totals.total}
      </p>

      <div className="button-row-full-width">
        <button
          type="button"
          className="button primary"
          onClick={() => {
            awaitUserChoice(true);
            setModalOpen(false);
          }}
        >
          Approve Order
        </button>
        <button type="button" className="button grey" onClick={() => {
            awaitUserChoice(false);
            setModalOpen(false);
          }}>
          Check order details
        </button>
      </div>
    </div>
  );
};

export default ApproveOrderModal;