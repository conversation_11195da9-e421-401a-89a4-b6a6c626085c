.dropdown-wrapper {
  position: relative;
  outline: none;
  padding: 8px 12px;
  &.category-group-dropdown {
    display: inline-block;
    margin-right: 8px;
    .dropdown {
      border: 1px solid;
      .dropdown-item > a {
        font-size: 21px;
        height: initial;
        padding: 5px 9px;
      }
    }
    > a {
      font-size: 26px;
      color: $primary;
      border:none;
      border-bottom: 1px solid $primary;
      border-radius: 0;
      padding: 10px;
    }
  }
  .label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    font-size: 16px;
    padding: 10px 15px;
    margin: 0;
    border-radius: 3px;
    background: $white;
    border: darken($off-white, 10%) 1px solid;
    cursor: pointer;
    @include media-down(mobile) {
      line-height: 24px;
      border-radius: 2px;
    }

    @include media-down(small-mobile) {
      font-size: 14px;
    }

    .icon {
      pointer-events: none;
      display: flex;
      align-items: center;
      margin-left: 12px;
      &:before {
        width: 12px;
        height: 12px;
        transition: all 0.3s;
        vertical-align: middle;
      }
    }

    .badge {
      margin-left: 8px;
      background: $secondary;
      @include media-down(small-mobile) {
        margin-left: 4px;
        height: 18px;
        width: 18px;
        line-height: 18px;
        font-size: 12px;
      }
    }

    &.active {
      .icon {
        &:before {
          transform: rotate(-180deg);
        }
      }
      &+.dropdown {
        top: 49px;
        height: auto;
        opacity: 1;
        visibility: visible;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
      }
    }

    &:hover {
      text-decoration: none;
    }
  }

  .dropdown {
    position: absolute;
    top: 40px;
    left: 0;
    z-index: 20;
    background: $white;
    box-shadow: 0 14px 36px 2px rgba($black, 0.15);
    border-radius: 5px;
    padding: 10px 5px;
    min-width: 100%;
    height: 0;
    opacity: 0;
    visibility: hidden;
    overflow: hidden;
    transition: top 0.4s, opacity 0.2s;
    left: auto;
    right: 0;

    .dropdown-item {
      a {
        @include ellipsis();
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 25px;
        width: 100%;
        text-decoration: none;
        cursor: pointer;
        &:hover {
          text-decoration: underline;
        }
      }
      @include media-down(large-mobile) {
        font-size: 14px;
      }
    }
  }
}

.supplier-menu-banner {
  .dropdown-wrapper {
    .label {
      display: inline;
      border: none;
      white-space: nowrap;
      padding: 8px 12px;
      font-size: 15px;
      .icon {
        display: inline-block;
      }
      &.active {
        &+.dropdown {
          top: 37px;
          overflow-y: scroll;
          max-height: 500px;
        }
      }
    }
    .dropdown-item {
      white-space: nowrap;
      &.active {
        font-weight: bold;
      }
      a {
        display: inline-block;
        height: 30px;
        &:hover {
          font-weight: bold;
          text-decoration: none;
        }
        // Add psuedo element to account for extra width of bold font
        // https://stackoverflow.com/a/********/********
        &::before {
          display: block;
          content: attr(data-content);
          font-weight: bold;
          height: 1px;
          color: transparent;
          overflow: hidden;
          visibility: hidden;
          white-space: nowrap;
        }
      }
    }
  }
}
