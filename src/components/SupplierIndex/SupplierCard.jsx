import { useState } from 'react';

import { Image } from 'components/Common';

const SupplierCard = ({ listing }) => (
  <div className="listing-grid-card-image">
    <Image url={listing.image_id} width={800} height={340} alt={`${listing.name}-image`} />
    {listing.business_codes.includes('Indigenous Owned') && (
      <span className="listing-grid-card-label listing-grid-card-label--indigenous" />
    )}
    {listing.is_featured && !listing.business_codes.includes('Indigenous Owned') && (
      <span className="listing-grid-card-label listing-grid-card-label--featured">Featured</span>
    )}
    {listing.is_new && !listing.is_featured && !listing.business_codes.includes('Indigenous Owned') && (
      <span className="listing-grid-card-label listing-grid-card-label--new">New</span>
    )}
    {listing.has_custom_menu && <span className="listing-grid-card-custom-price">Custom Menu</span>}
    {listing.has_custom_pricing && !listing.has_custom_menu && (
      <span className="listing-grid-card-custom-price">Custom Pricing</span>
    )}
    <SupplierIcons items={listing.tooltip} />
    {listing.min_order && listing.min_order !== 'Unknown' && (
      <div className="listing-grid-card-min">
        Min Order <strong>{listing.min_order} </strong>
      </div>
    )}
  </div>
);

const SupplierIcons = ({ items }) => {
  const [tooltipActive, setTooltipActive] = useState(false);
  if (!items || items.length === 0) return null;
  const iconCount = items.length;

  return (
    <div
      className="supplier-icons"
      onMouseEnter={() => setTooltipActive(true)}
      onMouseLeave={() => setTooltipActive(false)}
    >
      <div className={`supplier-icon icon icon-${items[0].icon} listing-grid-item-about-tooltip`}>{items[0].label}</div>
      {iconCount > 1 && <div className="supplier-icon show-count">+{iconCount - 1}</div>}
      {tooltipActive && (
        <p className="icons-tooltip">
          {items.map((item) => (
            <li key={item.label}>{item.label}</li>
          ))}
        </p>
      )}
    </div>
  );
};

export default SupplierCard;
