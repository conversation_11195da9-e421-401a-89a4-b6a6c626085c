import { WOOLWORTHS_SUPPLIER_ID } from './constants';
import { getSupplierIndexParams } from './format';

export function formatOrderLinesForApi({ selectedMenuItems, extras, companyID }) {
  return selectedMenuItems.map((menuItem) => ({
    item_id: menuItem.itemId,
    note: menuItem.note,
    quantity: menuItem.quantity,
    ...(menuItem?.serving_sizes || menuItem.itemId === menuItem.serving.id || { serving_size_id: menuItem.serving.id }),
    ...(extras.length && { selected_menu_extra_ids: extras.map(({ id }) => id) }),
    company_id: companyID,
  }));
}

export function calculateMenuItemTotal({ menuItems, extras }) {
  const menuItemQuantities = menuItems.length && menuItems.reduce((sum, { quantity }) => sum + quantity, 0);
  const menuItemPrices = menuItems.map(
    (menuItem) => Number(menuItem.serving.discount || menuItem.serving.price) * Number(menuItem.quantity)
  );
  const menuExtrasPrices = extras.map((each) => Number(each.price).toFixed(2) * menuItemQuantities);
  const totalPrices = [...menuItemPrices, ...menuExtrasPrices];
  const totalPrice = totalPrices.length && totalPrices.reduce((acc, curr) => acc + curr).toFixed(2);
  return totalPrice;
}

export const sanitizedDeliveryAddress = (order) => {
  if (!order?.delivery_suburb) return null;

  const orderSuburb = order.delivery_suburb;
  return {
    street: order.delivery_address,
    suburb: orderSuburb.name,
    state: orderSuburb.state,
    postcode: orderSuburb.postcode,
  };
};

export const orderHasOtherSuppliers = (initialOrders) => {
  if (!Object.keys(initialOrders).length) return false;
  if (Object.keys(initialOrders).length > 1) return true;
  const singleOrder = Object.values(initialOrders)[0];
  if (!singleOrder.locations) return false;
  const hasNonWoolworthsSupplier = Object.values(singleOrder?.locations).some((location) =>
    Object.values(location.suppliers).some((supplier) => supplier.id !== WOOLWORTHS_SUPPLIER_ID)
  );
  return hasNonWoolworthsSupplier;
};

export const getUniqueSupplierIds = (orders, passedID) => {
  if (!orders) return [];
  const supplierIds = new Set();

  Object.values(orders).forEach((order) => {
    Object.values(order.locations).forEach((location) => {
      if (!location.suppliers) return;

      Object.values(location.suppliers).forEach((supplier) => {
        supplierIds.add(supplier.id);
      });
    });
  });

  return Array.from([...supplierIds, passedID]);
};

export const getAddAnotherSupplierLink = ({ category = 'office-catering', geoLocation, mealUUID, dateOptions }) => {
  const addAnotherSupplierLink = `/search/${category}/${geoLocation?.state}/${geoLocation?.suburb}`;
  const supplierIndexParams = getSupplierIndexParams(mealUUID, dateOptions?.deliveryDate, dateOptions?.deliveryTime);
  return addAnotherSupplierLink + supplierIndexParams;
};
