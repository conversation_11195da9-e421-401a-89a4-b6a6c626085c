import { useContext } from 'react';
import { UserContext } from 'context/user';

const PayOnAccountRequest = () => {
  const { user } = useContext(UserContext);

  const openHubspotChat = () => {
    window.HubSpotConversations.widget.open();
  };

  return (
    <div>
      <h3 style={{ marginTop: '26px', marginBottom: '20px' }}>Paying on Account</h3>
      <p>
        Hi <strong>{user.first_name}, </strong>
        <p>
          Unfortunately, your yordar account has not been verified for payment on account. Please feel free to reach out
          to our orders team to get verified and set up for payment on account.
        </p>
        <a style={{ color: '#1f9e86', fontWeight: 'bold', cursor: 'pointer' }} onClick={openHubspotChat}>
          Contact our team now
        </a>
      </p>
    </div>
  );
};

export default PayOnAccountRequest;
