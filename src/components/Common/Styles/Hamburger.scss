// Mixins & Animations
@mixin menu-toggle-mid-line($color: $primary) {
  background: linear-gradient(
    to bottom,
    transparent 0%,
    transparent calc(50% - 1px),
    $color calc(50% - 1px),
    $color calc(50% + 1px),
    transparent calc(50% + 1px),
    transparent 100%
  );
}

@keyframes toggle_top_line {
  25% {
    top: 10px;
  }
  100% {
    top: 10px;
    transform: rotate(45deg);
  }
}

@keyframes toggle_bottom_line {
  25% {
    top: 10px;
  }
  100% {
    top: 10px;
    transform: rotate(-45deg);
  }
}

.hamburger {
  @include menu-toggle-mid-line($off-black);
  display: none;
  position: relative;
  z-index: 999;
  appearance: none;
  border: none;
  width: 30px;
  height: 20px;
  font-size: 0;
  padding: 0;
  outline: none;
  cursor: pointer;
  &.dash {
    width: 20px;
    height: 16px;
    &:before, &:after {
      width: 20px;
    }
  }

  &:before, &:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background: $off-black;
  }

  &.show {
    display: block;
    margin-right: 8px;
  }

  &:after {
    top: auto;
    bottom: 0;
  }

  @include media-down(large-tablet) {
    display: block;
  }

  // Cross animation
  &.active {
    // Hide middle line
    @include menu-toggle-mid-line($color: transparent);
    animation: menu-toggle-animation .4s ease;
    &:before {
      animation: toggle_top_line .4s ease;
      animation-fill-mode: forwards;
    }
    &:after {
      animation: toggle_bottom_line .4s ease;
      animation-fill-mode: forwards;
    }
  }
}
