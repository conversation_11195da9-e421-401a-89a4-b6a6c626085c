import useEmployeeSurveyStore from 'store/useEmployeeSurveyStore';
import { cateringImage, pantryImage } from './utils/images';

const SurveyBanner = () => {
  const { survey, step, isSubmitted } = useEmployeeSurveyStore((state) => ({
    survey: state.survey,
    step: state.step,
    isSubmitted: state.isSubmitted,
  }));

  if (!survey) return null;

  const stepsCount = survey.questions.length;
  const stepsCompleted = step === stepsCount || isSubmitted;

  return (
    <div className="auth-card__illustration quote">
      <div className="quote-image">
        <img
          src={survey.category_group_name === 'Catering' ? cateringImage : pantryImage}
          alt={`${survey.category_group_name} survey`}
        />
      </div>

      <div className="progress-bar-container">
        <p className="progress-tag">{stepsCompleted ? 'Completed' : 'Enter Details'}</p>
        <progress value={isSubmitted ? survey.questions.length : step} max={stepsCount} />
      </div>

      <div className="quote-panel-info" style={{ paddingTop: 0 }}>
        <h4 className="auth-card-title" style={{ fontWeight: 'bold', fontSize: '20px', marginBottom: '6px' }}>
          {survey.category_group_name} Survey
        </h4>
        <p>Some information about the survey</p>
      </div>
    </div>
  );
};

export default SurveyBanner;
