import useQuoteStore from 'store/useQuoteStore';
import RadioButtonGroup from './RadioButtonGroup';

const CateringRequirementsForm = () => {
  const { setBoolField, setFormField, boolFields } = useQuoteStore((state) => ({
    setBoolField: state.setBoolField,
    setFormField: state.setFormField,
    formFields: state.formFields,
    boolFields: state.boolFields,
  }));

  return (
    <>
      <p className="quote-option-text">Do you require items to be individually packaged?</p>
      <div className="checkbox-flex">
        <RadioButtonGroup
          name="individuallyPacked"
          options={[
            { id: 'true', label: 'Yes' },
            { id: 'false', label: 'No' },
          ]}
          value={boolFields.individuallyPacked.toString()}
          onChange={(selectedOption) => setBoolField('individuallyPacked', selectedOption === 'true')}
        />
      </div>
      <p className="quote-option-text">Do you require cutlery?</p>
      <div className="checkbox-flex">
        <RadioButtonGroup
          name="cutlery"
          options={[
            { id: 'true', label: 'Yes' },
            { id: 'false', label: 'No' },
          ]}
          value={boolFields.cutlery.toString()}
          onChange={(selectedOption) => setBoolField('cutlery', selectedOption === 'true')}
        />
      </div>
      <h3 className="quote-sub-heading">Any other requirements we should know about?</h3>

      <textarea
        className="quote-textarea"
        placeholder="Please let us know if there's any special requirements for your catering order"
        onBlur={(e) => setFormField('specialRequirements', e.target.value)}
      />
    </>
  );
};

export default CateringRequirementsForm;
