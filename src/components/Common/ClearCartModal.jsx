import Modal from 'react-responsive-modal';
import useDocketStore from 'store/useDocketStore';

const ClearCartModal = ({ message, backNavigationOptions = { path: '/', value: 'Back' } }) => {
  const { clearOrder } = useDocketStore((state) => ({
    clearOrder: state.clearOrder,
  }));
  return (
    <Modal
      open
      center
      styles={{
        modal: { maxWidth: '500px', padding: 0, borderRadius: '10px' },
        closeButton: { cursor: 'pointer', marginLeft: '6px' },
      }}
      showCloseIcon={false}
      onClose={() => {}}
    >
      <div className="menu-modal">
        <h3 className="menu-modal__heading menu-modal__heading-woolworths">Cart Error</h3>
        <p>{message}</p>
        <p>Do you wish to clear the cart so you can proceed?</p>
        <a className="modal-button" href={backNavigationOptions.path}>
          {backNavigationOptions.value}
        </a>
        <a className="modal-button primary" onClick={() => clearOrder()}>
          Clear My Cart
        </a>
      </div>
    </Modal>
  );
};

export default ClearCartModal;
