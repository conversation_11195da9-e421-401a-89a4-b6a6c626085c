// SearchBar
.searchbar {
  position: relative;
  display: flex;
  margin-top: 2em;
  input {
    width: 100%;
    margin: 0;
    padding: 18px 20px;
    border-radius: 5px 0 0 5px;
    box-shadow: none!important;
    @include media-down(tablet) {
      box-shadow: none;
    }
  }
  &.fixed-width {
    input {
      width: 400px;
    }
  }
  .button {
    position: relative;
    display: flex;
    flex-shrink: 0;
    align-items: center;
    opacity: 1;
    border-radius: 0 5px 5px 0;
    font-size: 15px;
    transition: width 0.3s;
    @include media-down(tablet) {
      font-size: 0;
      padding: 0 20px;
    }
    @include media-down(mobile) {
      padding: 0 15px;
    }
    &:before {
      @include media-down(tablet) {
        content: '';
        display: block;
        width: 25px;
        height: 25px;
        background: url('../../../images/icons/search-white.svg') no-repeat center;
        background-size: contain;
        @include media-down(mobile) {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
  .autocomplete-dropdown-container {
    position: absolute;
    top: 52px;
    left: 0;
    z-index: 9;
    background: #ffffff;
    width: 100%;
    text-align: left;
    border: 1px solid rgba($black, 0.2);
    border-radius: 0 0 5px 5px;
    box-shadow: rgba($black, 0.1) 0 2px 3px;
  }
  .autocomplete-dropdown-item {
    display: grid;
    grid-template-columns: 25px auto;
    padding: 10px 15px;
    border-bottom: rgba($black, 0.2) 1px solid;
    text-transform: uppercase;
    color: rgba($textcolor, 0.8);
    font-size: 13px;
    font-weight: bold;
    line-height: 1.6em;
    cursor: pointer;
    &:hover {
      background: lighten($off-white, 3%);
    }
    &:last-of-type {
      border-bottom: none;
    }
    &:before {
      content: '';
      display: block;
      width: 16px;
      height: 16px;
      background: url('../../../images/icons/marker.svg') no-repeat center;
      background-size: contain;
      opacity: 1;
      margin: 4px 0 0;
    }
  }
  &.hide-button {
    input {
      border-radius: 5px;
    }
    .button {
      opacity: 0;
      width: 0;
      font-size: 0;
      padding: 0;
      border-width: 0!important;
    }
  }
  &.navigation {
    margin-top: 0;
    input {
      padding: 10px;
      @include media-down(small-tablet) {
        width: 100%;
        margin: 20px 0;
      }
    }
  }
}

.searchbar-loading {
  display: inline-block;
  margin-left: 4px;
  z-index: 1;
  width: 30px;
  height: 10px;
  background: $white url('../../../images/icons/three-dots.svg') center no-repeat;
  background-size: 24px;
}

.searchbar.dash-location-bar {
  min-width: 300px;
  margin: 0;
  @include media-down(large-tablet) {
    min-width: initial;
  }
  input {
    background-image: url('../../../images/icons/marker.svg');
    background-repeat: no-repeat;
    background-position: 3% center;
    background-size: 16px;
    padding: 8px 0;
    padding-left: 36px;
    background-color: rgb(243, 243, 243);
    font-size: 14px;
  }
}