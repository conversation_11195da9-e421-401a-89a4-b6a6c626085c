import useDocketStore from 'store/useDocketStore';

const LocationHeading = ({ id, name, setChangingLocationID, onlyOneLocation }) => {
  const { activeOrderID, setLocation } = useDocketStore((state) => ({
    activeOrderID: state.activeOrderID,
    setLocation: state.setLocation,
  }));

  return (
    <>
      <span>{name}</span>
      <div className="location-options">
        <span
          className="icon icon-pencil"
          onClick={(e) => {
            e.stopPropagation();
            setChangingLocationID(id);
          }}
          role="presentation"
        />
        <span
          className="icon icon-bin"
          onClick={(e) => {
            e.stopPropagation();
            setLocation({ id, name, method: 'delete', orderID: activeOrderID, onlyOneLocation });
          }}
          role="presentation"
        />
      </div>
    </>
  );
};

export default LocationHeading;
