import { useRouter } from 'next/router';

const SupplierHeader = ({ formatCategory, hasFilters, suppliersCount }) => {
  const router = useRouter();
  const { category, suburb, mealUUID } = router.query;

  const supplierCountMsg = hasFilters
    ? `Showing ${suppliersCount.filtered} of ${suppliersCount.total} suppliers`
    : `Showing ${suppliersCount.total} suppliers`;

  return (
    <header className="search-results-header">
      <h1 className="search-results-title">
        {mealUUID ? 'Meal Plans' : formatCategory(category)} in <span>{suburb.humanize()}</span>
      </h1>
      <p className="listing-grid__supplier-count">{supplierCountMsg}</p>
    </header>
  );
};

export default SupplierHeader;
