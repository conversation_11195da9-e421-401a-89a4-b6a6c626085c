import { useEffect } from 'react';
import { Spinner } from 'components/Common';

const ReloadingSurvey = () => {
  const reloadTime = 5000;

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      window.location.reload();
    }, reloadTime);

    return () => clearTimeout(timeoutId);
  }, []);
  return (
    <>
      <p style={{ fontWeight: 'bold' }}>Reloading Survey</p>
      <Spinner size="large" />
    </>
  );
};

export default ReloadingSurvey;
