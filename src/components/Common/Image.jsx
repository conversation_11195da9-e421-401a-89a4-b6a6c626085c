import { useRef, useState } from 'react';
import NextImage from 'next/image';
import getImagePath from 'utils/getImagePath';

const Image = ({ url, alt, className = '', width, height, noContainer, quality = 1, transform = 'fill' }) => {
  const [loading, setLoading] = useState(true);
  const imgRef = useRef(null);

  function handleImageLoad() {
    setLoading(false);
  }

  if (!url) {
    return <div className={`image-container static-placeholder ${className}`} />;
  }

  const ImageComponent = (
    <NextImage
      ref={imgRef}
      src={getImagePath({
        id: url,
        width: width * quality,
        height: height * quality,
        transform,
      })}
      width={width}
      height={height}
      onLoad={handleImageLoad}
      alt={alt}
      className={className}
      unoptimized
    />
  );

  if (noContainer) return ImageComponent;

  return <div className={`image-container ${className} ${loading ? 'placeholder' : ''}`}>{ImageComponent}</div>;
};

export default Image;
