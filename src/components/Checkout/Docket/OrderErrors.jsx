import useCheckoutStore from 'store/useCheckoutStore';

const OrderErrors = ({ checkoutPanel, moveToOrderSummary }) => {
  const {
    order: { checkoutErrors },
  } = useCheckoutStore();

  const errors = checkoutPanel === 'details' ? Object.values(checkoutErrors).flat() : checkoutErrors[checkoutPanel];
  if (!errors?.length) return null;

  return (
    <div className="checkout-docket-section invalid">
      <ul style={{ listStyle: 'none' }}>
        {errors.map((error) => (
          <li>{error}</li>
        ))}
      </ul>
      {checkoutPanel === 'details' && !!checkoutErrors.order.length && (
        <a className="invalid" onClick={moveToOrderSummary}>
          Click here to check order item errors!
        </a>
      )}
    </div>
  );
};

export default OrderErrors;
