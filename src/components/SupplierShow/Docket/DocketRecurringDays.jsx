import useDocketStore from 'store/useDocketStore';

const DocketRecurringDays = ({ recurrentOrderDays }) => {
  const { activeOrderID, setActiveOrderID } = useDocketStore((state) => ({
    activeOrderID: state.activeOrderID,
    setActiveOrderID: state.setActiveOrderID,
  }));

  if (!recurrentOrderDays) return null;

  return (
    <ul className="recurring-days-list">
      {recurrentOrderDays.map((day) => (
        <li
          key={day.id}
          className={Number(activeOrderID) === day.id ? 'active' : ''}
          onClick={() => setActiveOrderID(day.id)}
          role="presentation"
        >
          {day.name.titleize()}
        </li>
      ))}
    </ul>
  );
};

export default DocketRecurringDays;
