import { useContext, useRef, useState } from 'react';
import { useRouter } from 'next/router';
import { Modal } from 'react-responsive-modal';

import { ClearCartModal, Image } from 'components/Common';
import {
  FavouriteHeart,
  MenuItemOption,
  MenuItemExtras,
  MenuItemModalFooter,
  OrderAgainModal,
  TeamMenuItemModalFooter,
  RecurringOrderNotification,
} from 'components/SupplierShow';
import { WOOLWORTHS_SUPPLIER_ID } from 'utils/constants';
import { formatOrderLinesForApi } from 'utils/order';
import { UserContext } from 'context/user';
import 'react-responsive-modal/styles.css';
import useMenuItemStore from 'store/useMenuItemStore';
import useModalStore from 'store/useModalStore';
import useDocketStore from 'store/useDocketStore';

const MenuItemModal = ({ addingDisabled, supplierID, isTeamOrder = false }) => {
  const { user } = useContext(UserContext);
  const modalRef = useRef();
  const router = useRouter();
  const { mealUUID } = router.query;
  const { isWoolworthsOrder } = useDocketStore((state) => ({
    isWoolworthsOrder: state.order?.isWoolworthsOrder,
  }));
  const { modalOpen, setModalOpen } = useModalStore((state) => ({
    modalOpen: state.open,
    setModalOpen: state.setModalOpen,
  }));

  const { item, orderAgain, setOrderAgain, setMenuItem } = useMenuItemStore((state) => ({
    item: state.menuItem,
    orderAgain: state.orderAgain,
    setOrderAgain: state.setOrderAgain,
    setMenuItem: state.setMenuItem,
  }));
  const [selectedMenuItems, setSelectedMenuItems] = useState([]);
  const [extras, setExtras] = useState([]);
  const [stickyTitle, setStickyTitle] = useState(false);

  const closeModal = () => {
    setSelectedMenuItems([]);
    setExtras([]);
    setModalOpen(false);
    setOrderAgain(null);
    setMenuItem(null);
  };

  if (orderAgain) {
    return (
      <Modal
        open={modalOpen}
        onClose={() => closeModal()}
        center
        className="menu-item-modal"
        styles={{
          modal: { minWidth: '400px', padding: 0, borderRadius: '10px' },
          closeButton: { cursor: 'pointer', marginLeft: '6px' },
        }}
      >
        <OrderAgainModal item={orderAgain} />
      </Modal>
    );
  }

  if (!item) return null;
  if (isWoolworthsOrder && supplierID !== WOOLWORTHS_SUPPLIER_ID) {
    return (
      <ClearCartModal
        message="Your cart is currently configured for a Woolworths order."
        backNavigationOptions={{ path: '/show/woolworths', value: 'Back To Woolworths' }}
      />
    );
  }
  const hasServings = item?.serving_sizes?.length;
  const orderLines = formatOrderLinesForApi({
    selectedMenuItems,
    extras,
    companyID: user?.profile?.company_id,
    supplierID,
    ...(mealUUID && { mealUUID }),
  });

  return (
    <Modal
      open={modalOpen}
      onClose={() => closeModal()}
      center
      className="menu-item-modal"
      styles={{
        modal: { maxWidth: '500px', padding: 0, borderRadius: '10px' },
        closeButton: { cursor: 'pointer', marginLeft: '6px' },
      }}
    >
      {addingDisabled && <RecurringOrderNotification />}
      {!addingDisabled && (
        <>
          <div className={`modal-header${stickyTitle ? ' sticky' : ''}`}>
            <h2 className="menu-item-modal-title">{item.name}</h2>
            {user && <FavouriteHeart item={item} />}
          </div>
          <div
            className="menu-item-modal-details"
            onScroll={(e) => (e.target.scrollTop > 40 ? setStickyTitle(true) : setStickyTitle(false))}
            ref={modalRef}
          >
            <p className="menu-item-modal-description">{item.description}</p>
            {item?.image_id && (
              <Image
                url={item.image_id}
                className="menu-item-modal-image"
                alt="menu-item-image"
                width={600}
                height={300}
                quality={2}
                transform="fill"
              />
            )}
            {hasServings ? (
              item.serving_sizes.map((serving) => (
                <MenuItemOption
                  extras={extras}
                  itemId={item.id}
                  key={serving.id}
                  defaultQuantity={0}
                  minQuantity={item.minimum_quantity}
                  serving={serving}
                  setExtras={setExtras}
                  selectedMenuItems={selectedMenuItems}
                  setSelectedMenuItems={setSelectedMenuItems}
                />
              ))
            ) : (
              <MenuItemOption
                extras={extras}
                itemId={item.id}
                key={item.id}
                defaultQuantity={1}
                minQuantity={item.minimum_quantity}
                maxQuantity={isWoolworthsOrder ? 36 : 0}
                serving={item}
                setExtras={setExtras}
                selectedMenuItems={selectedMenuItems}
                setSelectedMenuItems={setSelectedMenuItems}
              />
            )}
            {item.menu_extra_sections && (
              <div className="menu-item-modal-extras">
                {item.menu_extra_sections.map((section) => (
                  <MenuItemExtras section={section} key={section.id} extras={extras} setExtras={setExtras} />
                ))}
              </div>
            )}
          </div>
          {!isTeamOrder && (
            <MenuItemModalFooter
              orderLines={orderLines}
              selectedMenuItems={selectedMenuItems}
              setSelectedMenuItems={setSelectedMenuItems}
              extras={extras}
              setExtras={setExtras}
              modalRef={modalRef}
              supplierID={supplierID}
              item={item}
              closeModal={closeModal}
            />
          )}
          {isTeamOrder && (
            <TeamMenuItemModalFooter
              orderLines={orderLines}
              selectedMenuItems={selectedMenuItems}
              setSelectedMenuItems={setSelectedMenuItems}
              extras={extras}
              setExtras={setExtras}
              modalRef={modalRef}
              supplierID={supplierID}
              item={item}
              closeModal={closeModal}
            />
          )}
        </>
      )}
    </Modal>
  );
};

export default MenuItemModal;
