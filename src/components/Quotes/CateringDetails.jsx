import useQuoteStore from 'store/useQuoteStore';
import InputField from './InputField';

const CateringDetails = () => {
  const { formFields, setFormField } = useQuoteStore((state) => ({
    formFields: state.formFields,
    setFormField: state.setFormField,
  }));

  const inputFields = [
    {
      type: 'text',
      label: 'Location',
      field: 'location',
      className: 'icon icon-marker icon-large icon-right-spacer',
      span: '1/3',
    },
    {
      type: 'date',
      label: 'Date of your event',
      field: 'date',
      className: 'icon icon-date icon-large icon-right-spacer',
    },
    {
      type: 'time',
      label: 'Time of your event',
      field: 'time',
      className: 'icon icon-clock icon large icon-right-spacer',
    },
    {
      type: 'text',
      label: 'Estimated No. of Attendees',
      field: 'estimatedAttendees',
      className: 'icon icon-team icon-large icon-right-spacer',
    },
    {
      type: 'text',
      label: "What's the Occasion?",
      field: 'occasion',
      className: 'icon icon-occasion icon-large icon-right-spacer',
    },
    { type: 'budget', label: 'Budget', field: 'budget', className: 'icon icon-copy icon-large icon-right-spacer' },
  ];

  return (
    <div>
      <div className="quote-details-form">
        {inputFields.map(({ type, label, field, className, span }) => (
          <InputField
            key={field}
            type={type}
            label={label}
            className={className}
            isError={formFields[field].isError}
            onChange={(value) => setFormField(field, value)}
            value={formFields[field].value}
            span={span}
          />
        ))}
      </div>
    </div>
  );
};

export default CateringDetails;
