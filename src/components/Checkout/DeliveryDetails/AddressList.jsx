import { useContext, useState } from 'react';
import PlacesAutocomplete, { geocodeByPlaceId } from 'react-places-autocomplete';
import { HostContext } from 'context/host';

import { SavedAddressList } from 'components/Checkout';

const AddressList = ({ setActiveAddress }) => {
  const [value, setValue] = useState('');
  const { locality } = useContext(HostContext);

  const onSearchSubmit = async (val, placeId) => {
    try {
      const [result] = await geocodeByPlaceId(placeId);

      const { address_components: addressComponents } = result;
      // Get values from google places address components array
      const streetNumType = 'street_number';
      const streetNameType = 'route';
      const suburbType = 'locality';
      const stateType = 'administrative_area_level_1';
      const postcodeType = 'postal_code';
      const suburb = addressComponents.find((comp) => comp.types.includes(suburbType)).short_name;
      const state = addressComponents.find((comp) => comp.types.includes(stateType)).short_name;
      const postcode = addressComponents.find((comp) => comp.types.includes(postcodeType)).short_name;
      const streetNumber = addressComponents.find((comp) => comp.types.includes(streetNumType))?.long_name;
      const streetName = addressComponents.find((comp) => comp.types.includes(streetNameType))?.long_name;

      let streetAddress = null;

      if (streetName) {
        streetAddress = streetNumber ? `${streetNumber} ${streetName}` : streetName;
      }

      const suburbLabel = `${suburb} ${state} ${postcode}`;

      setActiveAddress({
        level: '',
        instructions: '',
        label: `${streetAddress}, ${suburb} ${state} ${postcode}`,
        suburb,
        suburbLabel,
        state,
        street_address: streetAddress,
        postcode,
      });

      setValue(val);
    } catch (error) {
      console.log('Error fetching address', error);
    }
  };

  const handleChange = (val) => {
    setValue(val);
  };

  const handleSelect = (val, placeId) => {
    onSearchSubmit(val, placeId);
  };

  const searchOptions = {
    componentRestrictions: { country: locality },
    types: ['address'],
    suppressDefaultStyles: true,
  };

  return (
    <PlacesAutocomplete searchOptions={searchOptions} onChange={handleChange} onSelect={handleSelect} value={value}>
      {({ getInputProps, suggestions, getSuggestionItemProps, loading: loadingSuggestions }) => (
        <>
          <input
            // onFocus={handleFocus}
            {...getInputProps({
              placeholder: 'Enter Delivery Address',
              className: 'location-search-input icon',
            })}
            spellCheck={false}
            required
          />
          <SavedAddressList
            suggestions={suggestions}
            setActiveAddress={setActiveAddress}
            getSuggestionItemProps={getSuggestionItemProps}
          />
        </>
      )}
    </PlacesAutocomplete>
  );
};

export default AddressList;
