import { useState, useEffect, useRef, useContext } from 'react';
import { UserContext } from 'context/user';
import useCheckoutStore from 'store/useCheckoutStore';
import { toast } from 'react-toastify';
import { defaultToastOptions, toastTypeOptions } from 'utils/toasterConfig';

const QuoteButtons = ({ handleSubmit }) => {
  const {
    order,
    quoteDetails,
    setQuoteDetails,
    validations,
    setValidationField,
    inProgress,
    setInProgress,
  } = useCheckoutStore((state) => ({
    order: state.order,
    quoteDetails: state.quoteDetails,
    setQuoteDetails: state.setQuoteDetails,
    validations: state.validations,
    setValidationField: state.setValidationField,
    inProgress: state.inProgress,
    setInProgress: state.setInProgress,
  }));

  const [isSendingQuote, setIsSendingQuote] = useState(false);
  const [addMessage, setAddMessage] = useState(false);
  const {
    user: { email: customerEmail },
  } = useContext(UserContext);

  const emailsRef = useRef(null);
  useEffect(() => {
    if (emailsRef && emailsRef.current) {
      emailsRef.current.style.height = 'auto';
      emailsRef.current.style.height = `${emailsRef.current.scrollHeight + 20}px`;
    }
  }, [quoteDetails.emails]);

  const handleQuoteEmails = (event) => {
    setValidationField('quoteEmails', null, true);
    setQuoteDetails({
      emails: event.target.value,
    });
  };

  const handleQuoteMessage = (event) => {
    setQuoteDetails({
      message: event.target.value,
    });
  };

  const handleSpaceInEmails = (event) => {
    if (event.which === 13 || event.keyCode === 13 || event.keyCode === 32) {
      setValidationField('quoteEmails', null, true);
      let emailsValue = event.target.value;
      emailsValue = emailsValue.replace(/\n|\r|\s/g, ';');
      setQuoteDetails({
        emails: emailsValue,
      });
    }
  };

  const handleSendQuote = () => {
    setValidationField('quoteEmails', null, true);
    if (quoteDetails.emails) {
      handleSubmit({ andSubmit: true, mode: 'quote' });
    } else {
      setValidationField('quoteEmails', false);
      toast.dismiss();
      toast.error('Need at least a single email to send quote', { ...defaultToastOptions, ...toastTypeOptions.info });
    }
  };

  const quoteCustomer = () => {
    const quoteEmails = quoteDetails.emails;
    if (!quoteEmails.includes(customerEmail)) {
      setQuoteDetails({
        emails: `${customerEmail};${quoteEmails}`,
      });
    }
  };

  if (isSendingQuote) {
    return (
      <div className="checkout-docket" style={{ marginTop: '1rem' }}>
        <div className="checkout-docket-info">
          <small className="hint" style={{ float: 'right', fontSize: '14px' }}>
            Separate multiple emails with semicolon ';'
          </small>
          <textarea
            ref={emailsRef}
            className={validations.quoteEmails === false ? 'invalid--input' : ''}
            placeholder="Email address(es) to send the quote to"
            value={quoteDetails.emails}
            onChange={handleQuoteEmails}
            onKeyUp={handleSpaceInEmails}
          />
          <a className="text-primary" onClick={quoteCustomer}>
            Click to add customer email <em>`{customerEmail}`</em>
          </a>

          {!addMessage && (
            <a
              className="button checkout-button confirm tertiary tiny"
              style={{ padding: '0 0.3rem', width: 'auto', margin: 0 }}
              onClick={() => setAddMessage(true)}
            >
              Add Quote Message
            </a>
          )}
          {addMessage && (
            <textarea
              rows="2"
              placeholder="Message to be sent with the quote"
              value={quoteDetails.message}
              onChange={handleQuoteMessage}
            />
          )}
          <button className="button checkout-button confirm primary" onClick={handleSendQuote}>
            {inProgress ? 'Sending Quote....' : 'Send Quote'}
          </button>
          <button
            className="button checkout-button"
            style={{ backgroundColor: 'grey', marginTop: '0.5rem' }}
            onClick={() => {
              setValidationField('creditCardName', null);
              setValidationField('creditCardNum', null);
              setValidationField('creditCardId', !!order.creditCardId);
              setValidationField('quoteEmails', null, true);
              setIsSendingQuote(false);
            }}
          >
            Cancel Quote
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <button
        className="button checkout-button confirm secondary"
        onClick={() => {
          setValidationField('creditCardName', null, true);
          setValidationField('creditCardNum', null, true);
          setValidationField('creditCardId', null, true);
          setValidationField('cpoId', null, true);
          setValidationField('departmentIdentity', null, true);
          setInProgress('save-quote');
          handleSubmit({ andSubmit: true, mode: 'save-quote' });
        }}
      >
        {inProgress === 'save-quote' ? 'Saving as Quote...' : 'Save as Quote'}
      </button>
      <button
        className="button checkout-button confirm tertiary"
        onClick={() => {
          setValidationField('creditCardName', null, true);
          setValidationField('creditCardNum', null, true);
          setValidationField('creditCardId', null, true);
          setValidationField('cpoId', null, true);
          setValidationField('departmentIdentity', null, true);
          setIsSendingQuote(true);
        }}
      >
        Send Quote
      </button>
    </>
  );
};

export default QuoteButtons;
