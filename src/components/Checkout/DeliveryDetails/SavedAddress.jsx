const SavedAddress = ({ address = {}, setActiveAddress }) => {
  const {
    level,
    street_address: streetAddress,
    suburb_label: suburbLabel
  } = address;

  return (
    <div
      className="saved-address"
      onClick={() =>
        setActiveAddress({ ...address, label: `${streetAddress}, ${suburbLabel}`, suburbLabel: suburbLabel })
      }
    >
      <div>
        <p className="bold">
          {level ? `${level}/` : ''}
          {streetAddress}
        </p>
        <p className="grey">{suburbLabel}</p>
      </div>
      <div className="edit-saved-address" />
    </div>
  );
};

export default SavedAddress;
