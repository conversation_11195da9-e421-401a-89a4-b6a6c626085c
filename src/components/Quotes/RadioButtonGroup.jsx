const RadioButtonGroup = ({ name, options, value, onChange, className }) => (
  <div className="budget-radios">
    {options.map((option) => {
      const id = `${name}-${option.id}`;
      return (
        <div key={id} className={className}>
          <input
            type="radio"
            value={option.id}
            checked={value === option.id}
            onChange={() => onChange(option.id)}
            id={id}
            name={name}
          />
          <label htmlFor={id}>{option.label}</label>
        </div>
      );
    })}
  </div>
);

export default RadioButtonGroup;
