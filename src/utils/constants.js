export const CATEGORY_GROUP_OPTIONS = (state, suburb) => [
  { label: 'Office Catering', href: `/search/office-catering/${state}/${suburb}`, category: 'office-catering' },
  { label: 'Office Snacks', href: `/search/office-snacks/${state}/${suburb}`, category: 'office-snacks' },
];
export const DAYS = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
export const MAXIMUM_MODAL_HEIGHT = 500;
export const WOOLWORTHS_SUPPLIER_ID = 10233;
export const COST_CENTER_ID_FORMATS = {
  'free-text': {
    type: 'text',
    label: '',
    limit: '',
  },
  digits: {
    type: 'number',
    label: 'Numerical',
    limit: '',
  },
  '4-digits': {
    type: 'number',
    label: '4 digit',
    limit: 4,
  },
};
export const mealPlanFilters = ['Buffets', 'Individually Boxed Meals', 'Share Meals'];
