import { useEffect, useState } from 'react';

import useVisibleMenuSection from 'store/useVisibleMenuSection';

const useActiveMenuSection = (menuSectionRefs) => {
  const setVisibleMenuSection = useVisibleMenuSection((state) => state.setVisibleMenuSection);
  const [activeMenuSection, setActiveMenuSection] = useState(null);
  const [scrollPosition, setScrollPosition] = useState(0);
  const NAV_HEIGHT = 100;

  const handleScroll = () => {
    const position = window.pageYOffset;
    setScrollPosition(position);
  };

  useEffect(() => {
    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const visibleMenuSection = () => {
    // HERE
    const offsets = menuSectionRefs.current.map((section) => section?.offsetTop - NAV_HEIGHT);
    const offsetsInRange = offsets.filter((offset) => offset <= scrollPosition);
    const offset = offsetsInRange[offsetsInRange.length - 1];
    menuSectionRefs.current.forEach((menuSection) => {
      if (!menuSection?.offsetTop) return;
      if (offset === menuSection.offsetTop - NAV_HEIGHT) {
        if (activeMenuSection?.id !== menuSection.id) {
          return setActiveMenuSection({ id: menuSection.id, name: menuSection.dataset.title });
        }
      }
    });
  };

  useEffect(() => {
    visibleMenuSection();
  }, [scrollPosition]);
  useEffect(() => {
    setVisibleMenuSection(activeMenuSection);
  }, [activeMenuSection]);
};

export default useActiveMenuSection;
