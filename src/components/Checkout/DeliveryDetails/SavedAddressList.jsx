import useCheckoutStore from 'store/useCheckoutStore';
import { SavedAddress } from 'components/Checkout';

const SavedAddressList = ({ setActiveAddress, suggestions, getSuggestionItemProps }) => {
  const { checkout } = useCheckoutStore();
  const savedAddresses = checkout?.saved_addresses || [];
  const recentAddresses = checkout?.recent_addresses || [];

  const googleAddresses = suggestions.filter(
    (suggestion) =>
      suggestion.types.includes('street_address') ||
      suggestion.types.includes('premise') ||
      suggestion.types.includes('subpremise')
  );

  if (suggestions.length) {
    return (
      <div className="saved-address-list">
        {googleAddresses.map((suggestion) => {
          const className = `autocomplete-dropdown-item ${suggestion.active ? 'active' : ''} `;
          return (
            <>
              <div {...getSuggestionItemProps(suggestion, { className })}>
                <SavedAddress
                  address={{
                    level: '',
                    street_address: suggestion.formattedSuggestion.mainText,
                    suburb_label: suggestion.formattedSuggestion.secondaryText,
                  }}
                  setActiveAddress={setActiveAddress}
                />
              </div>
            </>
          );
        })}
      </div>
    );
  }

  return (
    <>
      {savedAddresses.length > 0 && (
        <div className="saved-address-list">
          <p style={{ marginTop: '12px', marginBottom: 0, fontWeight: 'bold' }}>Saved Addresses</p>
          {savedAddresses.map((savedAddress) => (
            <SavedAddress address={savedAddress} setActiveAddress={setActiveAddress} />
          ))}
        </div>
      )}
      {recentAddresses.length > 0 && (
        <div className="saved-address-list">
          <p style={{ marginTop: '12px', marginBottom: 0, fontWeight: 'bold' }}>Recent Addresses</p>
          {recentAddresses.map((recentAddress) => (
            <SavedAddress address={recentAddress} setActiveAddress={setActiveAddress} />
          ))}
        </div>
      )}
    </>
  );
};

export default SavedAddressList;
