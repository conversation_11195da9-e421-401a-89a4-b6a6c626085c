.accordion__section {
  display: flex;
  flex-direction: column;
}

.accordion {
  background-color: $black;
  color: $white;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  border: none;
  outline: none;
  transition: background-color 0.6s ease;
  &.active {
    background-color: lighten($black, 20%);
  }
}


.accordion__title {
  font-family: $body-font;
  font-weight: 600;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  font-size: 18px;
  font-weight: bold;
  background: #4e4e4e;
  color: white;
  padding: 12px 10px 12px 40px;
  cursor: pointer;
  &::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 50%;
    margin-top: -5px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 5px 0 5px 6px;
    border-color: transparent transparent transparent #fff;
  }
  &.active {
    background: black;
    &::before {
      transform: rotate(90deg);
    }
  }
  &.add {
    background: white;
    box-shadow: 0px 0px 4px 1px #e9e9e9;
    color: black;
    padding: 12px 17px;
    justify-content: flex-start;
    &::before {
      display: inline-block;
      position: static;
      background: url('../../../images/icons/plus.svg');
      width: 16px;
      height: 16px;
      margin-right: 10px;
      border: none;
    }
    &.active::before {
      background: url('../../../images/icons/minus-hollow.svg');
      transform: none;
    }
  }
}

.accordion__content {
  background-color: white;
  overflow: hidden;
  transition: max-height 0.6s ease;
  border-bottom: 1px solid #eee;
}