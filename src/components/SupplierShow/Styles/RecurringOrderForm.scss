.docket-time {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  font-weight: bold;
  border: 1px solid #b2b2b2;
  padding: 10px;
  background: #f1f1f1;
  @include media-down(small-tablet) {
    .button {
      width: initial;
      margin-top: 0;
    }
  }
  p {
    margin: 0;
    display: flex;
    align-items: center;
    &::before {
      content: '';
      margin-right: 4px;
      display: inline-block;
      background-repeat: no-repeat;
      background-position: center;
      width: 20px;
      height: 20px;
      background-image: url('../../../images/icons/clock.svg');
    }
  }
  .button {
    padding: 4px 8px;
    font-size: 12px;
  }
}
.docket-recurring-order {
  padding: 0 10px;
  .set-recurring, .copy-all {
    display: inline-block;
    cursor: pointer;
    padding-left: 35px;
    margin-top: 12px;
    margin-bottom: 14px;
    background: url(../../../images/icons/icon-checkbox-empty.svg) 0% 50% no-repeat;
    &.checked {
      background: url(../../../images/icons/icon-checkbox-checked.svg) 0% 50% no-repeat;
    }
  }
  .recurring-frequency {
    padding: 10px;
    margin-top: 10px;
    margin-bottom: 10px;
    color: black;
    font-weight: bold;
  }
  input[type="checkbox"] {
    position: absolute;
    margin: 0;
    clip: rect(0, 0, 0, 0);
  }
  .recurring-days {
    display: flex;
    margin-bottom: 10px;
  }
  .recurring-day {
    padding: 4px;
    border: 1px solid $black;
    cursor: pointer;
    width: 100%;
    text-align: center;
    &.checked {
      background: black;
      color: white;
    }
  }
  .recurring-item-description {
    font-weight: bold;
    margin-bottom: 10px;
  }
  .recurring-public-holidays {
    appearance: radio;
    margin-right: 10px;
  }
  .submit-recurring {
    margin-top: 10px;
    margin-bottom: 10px;
    padding: 8px;
    background: black;
    color: white;
    cursor: pointer;
    &.disabled {
      pointer-events: none;
      background: #e2e5e5;
    }
    &__loading {
      width: 50px;
      margin: 8px auto;
    }
  }
}

.recurring-days-list {
  display: flex;
  justify-content: space-between;
  text-align: center;
  li {
    width: 100%;
    padding: 4px 8px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    cursor: pointer;
  }
  .active {
    background: black;
    color: white;
  }
}