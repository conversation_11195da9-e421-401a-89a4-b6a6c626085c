import useDocketStore from 'store/useDocketStore';

const TeamOrderLevels = () => {
  const { order, setTeamAttendeeLevel, teamAttendee } = useDocketStore((state) => ({
    order: state.order,
    setTeamAttendeeLevel: state.setTeamAttendeeLevel,
    teamAttendee: state.teamAttendee,
  }));

  return (
    <div className="team-order-levels">
      <h5 className="team-level">Select Your Level</h5>
      <select name="attendee-levels" id="attendee-levels" onChange={(e) => setTeamAttendeeLevel(e.target.value)}>
        {order?.attendee_levels.map((level) => (
          <option key={level.id} value={level.id} selected={level.id === teamAttendee?.team_order_level_id}>
            {level.name}
          </option>
        ))}
      </select>
    </div>
  );
};

export default TeamOrderLevels;
