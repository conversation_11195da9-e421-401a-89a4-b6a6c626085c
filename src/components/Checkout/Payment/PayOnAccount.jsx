import useCheckoutStore from 'store/useCheckoutStore';

const PayOnAccount = () => {
  const {
    order,
    setOrderDetail,
    checkout: { billing_preference: billingPreference },
  } = useCheckoutStore();

  const handleChange = (event) => {
    setOrderDetail(event.target.name, event.target.value);
  };

  if (billingPreference === 'instantly') {
    return (
      <>
        <p className="billing-title">Choose Billing Method</p>
        <p>
          You've currently set your account to be <strong>billed per order</strong>. This order will be{' '}
          <strong>invoiced individually on delivery</strong>.
        </p>
      </>
    );
  }

  return (
    <>
      <p className="billing-title">Choose Billing Method</p>
      <p>
        You've currently set your account to be <strong>billed {billingPreference}</strong>. You can choose to either
        add this to your {billingPreference} billing or invoice it separately as an individual order.
      </p>
      <select
        name="invoiceIndividually"
        className="checkout-input account"
        value={order.invoiceIndividually}
        onChange={handleChange}
      >
        <option key="invoice-individually-false" value={false}>
          Add to my {billingPreference} billing
        </option>
        <option key="invoice-individually-true" value>
          Invoice separately from {billingPreference} billing
        </option>
      </select>
    </>
  );
};

export default PayOnAccount;
