import yordar from 'api/yordar';
import { useState } from 'react';
import { DYNAMIC_UPDATE_ORDER_ENDPOINT } from 'api/endpoints';
import useCheckoutStore from 'store/useCheckoutStore';

const OrderCoupon = () => {
  const [couponCode, setCouponCode] = useState('');
  const [couponErrors, setCouponErrors] = useState([]);
  const { orders, activeOrderID, attachCoupon } = useCheckoutStore();

  if (Object.keys(orders).length !== 1) return null;

  const order = orders[activeOrderID];
  const { id: orderId, coupon_code: orderCouponCode } = order;

  const submitCoupon = async () => {
    setCouponErrors([]);
    if (couponCode) {
      try {
        const {
          data: { totals },
        } = await yordar.put(DYNAMIC_UPDATE_ORDER_ENDPOINT(orderId), {
          order: {
            coupon_code: couponCode,
            id: orderId,
          },
        });
        attachCoupon({
          orderId,
          couponCode,
          totals,
        });
      } catch (err) {
        const { errors } = JSON.parse(err.request.response);
        setCouponErrors(errors);
      }
    } else {
      setCouponErrors(['Need a valid Coupon Code!']);
    }
  };

  return (
    <div className="checkout-section checkout-docket-section">
      {!orderCouponCode && (
        <div className="checkout-coupon">
          <input
            type="text"
            placeholder="Add Your Yordar Coupon Here"
            className="coupon"
            value={couponCode}
            onChange={(e) => setCouponCode(e.target.value)}
          />
          <button type="button" className="button small" onClick={submitCoupon}>
            Apply Coupon
          </button>
        </div>
      )}
      {orderCouponCode && (
        <p style={{ marginBottom: 0 }}>
          Applied Coupon:<strong>{orderCouponCode}</strong>
        </p>
      )}
      {couponErrors.length > 0 && couponErrors.map((error) => <p>{error}</p>)}
    </div>
  );
};

export default OrderCoupon;
