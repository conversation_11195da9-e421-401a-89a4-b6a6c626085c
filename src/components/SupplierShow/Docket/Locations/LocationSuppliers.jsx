import React from 'react';
import { useRouter } from 'next/router';

import { LocationOrderLine } from 'components/SupplierShow';
import { DYNAMIC_SUPPLIER_ENDPOINT } from 'api/endpoints';
import Link from 'next/link';

const LocationSuppliers = ({ suppliers, isTeamOrder }) => {
  const router = useRouter();
  const { supplier: currentSupplierSlug, mealUUID } = router.query;

  const supplierMenuURL = (supplier) => {
    if (isTeamOrder) return '';
    if (currentSupplierSlug === supplier.slug) return '';

    return `${DYNAMIC_SUPPLIER_ENDPOINT({
      slug: supplier.slug,
      mealUUID,
      json: false,
    })}`;
  };

  return (
    <div className="docket-panel">
      {suppliers.map((supplier) => {
        const orderLines = Object.values(supplier.order_lines);
        const supplierURL = supplierMenuURL(supplier);

        return (
          <React.Fragment key={supplier.name}>
            <div className="docket-supplier-header">
              <h4>{supplier.name}</h4>
              {!!supplierURL && <Link href={supplierURL}>View Menu</Link>}
            </div>
            {orderLines.map((orderline) => (
              <LocationOrderLine key={orderline.id} orderline={orderline} />
            ))}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default LocationSuppliers;
