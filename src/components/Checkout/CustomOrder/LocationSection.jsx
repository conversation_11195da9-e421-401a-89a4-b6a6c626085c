import { useState } from 'react';
import AsyncSelect from 'react-select/async';
import debounce from 'debounce-promise';
import yordar from 'api/yordar';

import useCheckoutStore from 'store/useCheckoutStore';
import { CustomSupplierSelect, SupplierSection } from 'components/Checkout';

import { CUSTOM_ORDER_SUPPLIERS_ENDPOINT } from 'api/endpoints';

const LocationSection = ({ location }) => {
  const [showSupplierSelect, setShowSupplierSelect] = useState(false);

  const locationSuppliers = location?.suppliers || {};

  const { activeOrderID, setLocation, addSupplier } = useCheckoutStore((state) => ({
    activeOrderID: state.activeOrderID,
    setLocation: state.setLocation,
    addSupplier: state.addSupplier,
  }));

  const handleAddSupplierToLocation = (selectedSupplier) => {
    if (selectedSupplier) {
      const { value: id, label: name, commission, slug, image, category_id } = selectedSupplier;
      addSupplier({
        orderID: activeOrderID,
        locationID: location.id,
        supplier: {
          id,
          name,
          commission,
          slug,
          image,
          category_id,
        },
      });
      setShowSupplierSelect(false);
    }
  };

  const promiseSupplierOptions = debounce(async (query) => {
    if (!query || query.length < 3) return [];

    try {
      const { data: responseSuppliers } = await yordar.get(CUSTOM_ORDER_SUPPLIERS_ENDPOINT, {
        params: { query },
        withCredentials: true,
      });

      return responseSuppliers.map((supplier) => ({
        value: supplier.id,
        label: supplier.name,
        commission: supplier.commission,
        slug: supplier.slug,
        image: supplier.image,
        category_id: supplier.category_id,
      }));
    } catch (err) {
      return [];
    }
  }, 1000);

  return (
    <div className="location-section">
      <div className="heading-block">
        <h5>{location.name}</h5>
        <div>
          <button type="button" onClick={() => setShowSupplierSelect(!showSupplierSelect)} className="button">
            + Add Supplier
          </button>
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              setLocation({ id: location.id, method: 'delete', orderID: activeOrderID });
            }}
            className="button white"
          >
            🗑️
          </button>
        </div>
      </div>

      {showSupplierSelect && (
        <div
          className="custom-supplier-select"
          style={{ marginBottom: '16px', display: 'flex', gap: '8px', alignItems: 'center' }}
        >
          <AsyncSelect
            cacheOptions
            defaultOptions
            isClearable
            placeholder="Search for Supplier"
            loadOptions={promiseSupplierOptions}
            onChange={(selected) => handleAddSupplierToLocation(selected)}
            value={null}
            styles={{
              control: (baseStyles, state) => ({
                ...baseStyles,
                borderRadius: '4px',
                border: '1px solid #ddd',
                boxShadow: state.isFocused ? 'none' : 'none',
              }),
              container: (baseStyles) => ({
                ...baseStyles,
                flex: 1,
              }),
            }}
            components={{ Option: CustomSupplierSelect }}
          />

          <button type="button" onClick={() => setShowSupplierSelect(false)} className="button grey">
            Cancel
          </button>
        </div>
      )}

      {/* Display suppliers */}
      {Object.keys(locationSuppliers).map((supplierID) => (
        <SupplierSection key={supplierID} location={location} supplier={locationSuppliers[supplierID]} />
      ))}

      {!Object.keys(locationSuppliers).length && (
        <div style={{ textAlign: 'center', color: '#666', fontStyle: 'italic', padding: '20px' }}>
          No suppliers added yet. Click "Add Supplier" to get started.
        </div>
      )}
    </div>
  );
};

export default LocationSection;
