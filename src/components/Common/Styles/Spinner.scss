.spinner {
  display: inline-block;
  position: relative;
  width: 60px;
  height: 60px;
  &.small {
    width: 20px;
    height: 20px;
    div {
      width: 13px;
      height: 13px;
    }
  }
  &.large {
    width: 100px;
    height: 100px;
    div {
      width: 100px;
      height: 100px;
    }
  }
  &.primary div{
    border-color: $primary transparent transparent transparent;
  }
}
.spinner div {
  box-sizing: border-box;
  display: block;
  position: absolute;
  width: 44px;
  height: 44px;
  margin: 8px;
  border: 2px solid black;
  border-radius: 50%;
  animation: spinner 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  border-color: black transparent transparent transparent;
}
.spinner div:nth-child(1) {
  animation-delay: -0.45s;
}
.spinner div:nth-child(2) {
  animation-delay: -0.3s;
}
.spinner div:nth-child(3) {
  animation-delay: -0.15s;
}
@keyframes spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}