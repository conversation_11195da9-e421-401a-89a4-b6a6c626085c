import { useEffect, useContext } from 'react';
import { ToastContainer, toast } from 'react-toastify';
import { toastTypes, defaultToastOptions, toastTypeOptions } from 'utils/toasterConfig';

import { UserContext } from 'context/user';

function isHTML(str) {
  const doc = new DOMParser().parseFromString(str, 'text/html');
  return Array.from(doc.body.childNodes).some((node) => node.nodeType === 1);
}

const ToastHtml = ({ message }) => (
  <div className="toastify-injected-html" dangerouslySetInnerHTML={{ __html: message }} />
);

const Toaster = () => {
  const { notifications } = useContext(UserContext);

  useEffect(() => {
    toast.dismiss();

    notifications.forEach(({ type, message }) => {
      const sanitizedMessage = isHTML(message) ? <ToastHtml message={message} /> : message;
      const toastType = toastTypes[type] || 'info';
      const toastOptions = toastTypeOptions[toastType] || {};
      toast[toastType](sanitizedMessage, { ...defaultToastOptions, ...toastOptions });
    });
  }, [notifications]);

  return <ToastContainer />;
};

export default Toaster;
