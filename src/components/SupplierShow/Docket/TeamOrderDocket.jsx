import { DocketLocations, DocketPricing, TeamOrderCheckoutButton, TeamOrderLevels } from 'components/SupplierShow';
import useFullScreen from 'hooks/Common/useFullScreen';
import useDocketStore from 'store/useDocketStore';

const TeamOrderDocket = () => {
  const [showNav, toggleNav] = useFullScreen();

  const { activeOrder, order, teamAttendee } = useDocketStore((state) => ({
    activeOrder: state.orders[state.activeOrderID],
    order: state.order,
    orders: state.orders,
    teamAttendee: state.teamAttendee,
  }));

  const hasOrder = !!Number(activeOrder?.totals?.total_number);
  const remainingSpend = activeOrder?.totals?.remaining_spend;

  return (
    <>
      <div className="docket-container">
        <div className={`docket ${showNav ? 'open' : 'closed'}`}>
          <div className="mobile-filters-heading">
            <span>Cart</span>
            <button
              type="button"
              onClick={toggleNav}
              className="icon icon-close docket-close-btn"
              aria-label="Close Filter"
            />
          </div>
          <div className="docket-wrapper">
            <div className="docket-internal">
              {remainingSpend && (
                <p className="remaining-spend">
                  Total Spend: {activeOrder?.totals?.total_spend} - Remaining Spend: {remainingSpend}
                </p>
              )}
              <div className="team-order-details">
                <h4 className="team-order-name">{order?.name}</h4>
                <div className="docket-delivery-at">{order.delivery_at}</div>
                <div className="team-budget">
                  {teamAttendee?.is_team_admin ? 'Attendee Budget' : 'Your Budget'}: ${order.team_order_budget}
                </div>
              </div>

              {order?.attendee_levels && <TeamOrderLevels />}
              <DocketLocations isTeamOrder />
              <DocketPricing hasOrder={hasOrder} orderTotals={activeOrder?.totals} isTeamOrder />
            </div>
            <div className="docket-checkout">
              <TeamOrderCheckoutButton isActive={hasOrder} total={activeOrder?.totals?.total_number} />
            </div>
          </div>
          {!!teamAttendee?.package_url && (
            <div className="back-to-package">
              <a href={teamAttendee.package_url}>
                {teamAttendee.is_team_admin ? 'Show Attendee Package Page' : 'Back to Your Team Orders'}
              </a>
            </div>
          )}
        </div>
      </div>
      {hasOrder && (
        <button type="button" onClick={toggleNav} className="docket-open-btn" aria-label="Open Cart">
          View Order
        </button>
      )}
    </>
  );
};

export default TeamOrderDocket;
