.survey-screen {
  background: #f4f0eb;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20px 20px;
  flex: 1 0 auto;
  &.justify {
    justify-content: space-between;
  }
  &.with-padding-top {
    padding-top: 100px;
  }
  .logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }  
  .survey-welcome__heading {
    font-family: $body-font;
    font-weight: 900;
    margin-bottom: 20px;
    font-size: 22px;
  }
  .survey-welcome__name {
    font-family: $heading-font;
    font-weight: 700;
    text-transform: uppercase;
    text-align: center;
    font-size: 16px;
    color: rgb(156, 156, 156);
  }
  .start-survey {
    width: 100%;
  }
}

.question-container {
  margin-top: 12vh;
  width: 100%;
}

.question {
  max-width: 600px;
  margin: auto;
}

.question-title {
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 900;
  line-height: 24px;
}

.ratings-container {
  display: flex;
  justify-content: space-between;
}

.overall-ratings-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  canvas {
    max-width: 200px;
    margin: 20px;
    @include media-up(tablet) {
      max-width: 300px;
    }
  }
  
}

.rating-selector {
  flex: 1;
  aspect-ratio: 1;
  border: 1px solid black;
  display: inline-block;
  padding: 0.5rem;
  text-align: center;
  margin-top: 10px;
  line-height: 2;
  &.toggle-selector {
    width: auto;
    margin-right: 1rem;
  }
  &:hover {
    background-color: grey;
    color: white; 
  }
  &.checked {
    background-color: black !important;
    color: white; 
  }
  & + .rating-selector {
    border-left: none;
  }
}

textarea.survey-question {
  padding: 10px 16px;
  transition: border 0.3s ease, height 0.3s ease;
  border: 1px solid rgb(210, 210, 210);
  &:focus {
    border-color: black;
  }
}

.single-select-question {
  display: flex;
  flex-direction: column;
  margin-bottom: 12px;
  .single-select-answer {
    background: white;
    border: 1px solid #333;
    color: black;
    text-transform: none;
    font-family: $body-font;
    padding: 14px 0;
    font-weight: 700;
    transition: all .3s ease-in;
    &.selected {
      background: black;
      color: white;
      border-color: black;
    }
    & + .single-select-answer {
      margin-top: 12px;
    }
  }
}

.survey-button-container {
  margin-top: auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.survey-button {
  background: black;
  border: none;
  width: 100% !important;
  padding: 16px 0;
  font-weight: bold;
  text-transform: uppercase !important;
  color: white;
  margin-top: auto;
  font-family: $body-font;
  max-width: 600px;
  margin-top: 6px;
  &.back {
    padding: 15px 0;
    background: #ffffff;
    color: black;
    border: 1px solid #808080;
  }
}

.slider-color {
  // -webkit-appearance: none;
  width: 100%;
  // height: 2px;
  border-radius: 5px;
  margin: 10px 0;
  // background: #000000;
  outline: none;
  opacity: 0.7;
  -webkit-transition: opacity .15s ease-in-out;
  transition: opacity .15s ease-in-out;
}

.survey-submit-heading {
  font-family: $body-font;
  font-weight: 900;
}