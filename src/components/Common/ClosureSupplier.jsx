const ClosureSupplier = ({ supplier }) => {
  if (supplier?.outside_operating_hours) {
    return (
      <p className="delivery-date-error">
        {supplier.name} operates from {supplier.operating_hours}.
      </p>
    );
  }
  if (supplier.close_from === supplier.close_to) {
    return (
      <p className="delivery-date-error">
        {supplier.name} will be closed on {supplier.close_from}.
      </p>
    );
  }

  return (
    <p className="delivery-date-error">
      {supplier.name} will be closed from {supplier.close_from} to {supplier.close_to}.
    </p>
  );
};

export default ClosureSupplier;
