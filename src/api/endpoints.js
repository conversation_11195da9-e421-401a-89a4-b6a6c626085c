import { WOOLWORTHS_SUPPLIER_ID } from 'utils/constants';
import {
  supplierIndexParams,
  supplierShowParams,
  sessionOrderParams,
  checkoutOrderParams,
  getQueryParams,
} from './params';
// AUTH
export const DYNAMIC_LOGIN_ENDPOINT = (param) => `/api/gatsby/status/login.json?${param}`;
export const CART_ENDPOINT = '/api/gatsby/status/cart.json';

// SUPPLIERS
export const DYNAMIC_SUPPLIERS_ENDPOINT = (query = {}) => {
  const queryParams = getQueryParams({ approvedParams: supplierIndexParams, query });
  return `/suppliers.json${queryParams}`;
};
export const DYNAMIC_SUPPLIER_ENDPOINT = ({ slug, json = true, query = {} }) => {
  const queryParams = getQueryParams({ approvedParams: supplierShowParams, query });
  return `/show/${slug}${json ? '.json' : ''}${queryParams}`;
};

export const CUSTOMER_SUPPLIERS_ENDPOINT = '/api/suppliers/customer-suppliers';
export const DYNAMIC_FAVOURITE_SUPPLIER_ENDPOINT = (id) => `/api/favourite_suppliers/${id}.json`;
export const CATEGORIES_ENDPOINT = '/api/categories.json';
export const BUILD_SUPPLIERS_ENDPOINT = '/api/suppliers/cache-list';
export const SEARCH_MENU_ITEMS_ENDPOINT = 'api/menu_items/search';
export const DYNAMIC_ORDER_ENDPOINT = (orderID) => (orderID ? `api/orders/${orderID}.json` : 'api/orders.json');

// MENU
export const DYNAMIC_MENU_SECTION_ENDPOINT = (id) =>
  `/api/menu_sections/${id}.json?supplier_profile_id=${WOOLWORTHS_SUPPLIER_ID}`;
export const DYNAMIC_FAVOURITE_ITEM_ENDPOINT = (id) => `/api/favourite_menu_items/${id}.json`;
export const DYNAMIC_DELIVERY_DETAILS_ENDPOINT = (supplierID) => `/api/suppliers/${supplierID}/delivery-details`;

// CART
export const DYNAMIC_SESSION_ORDER_ENDPOINT = (query = {}) => {
  const queryParams = getQueryParams({ approvedParams: sessionOrderParams, query });
  return `/api/orders/from-session.json${queryParams}${queryParams ? '&' : '?'}timestamp=${new Date().getTime()}`; // Timestamp is to prevent caching
};
export const DYNAMIC_LOCATION_ENDPOINT = (locationID) => `/api/locations${locationID ? `/${locationID}` : ''}`;
export const ORDER_LINES_ENDPOINT = '/api/order_lines';
export const DYNAMIC_UPDATE_ORDER_LINE_ENDPOINT = (orderlineID) => `/api/order_lines/${orderlineID}`;
export const DYNAMIC_UPDATE_ORDER_COMMISSION_ENDPOINT = (orderID) => `/api/orders/${orderID}/update_commission.json`;
export const RECURRING_ORDER_ENDPOINT = 'api/orders/recurrent.json';
export const CLEAR_CART_ENDPOINT = 'cart/clear.json';

// NEWSLETTER
export const NEWSLETTER_ENDPOINT = `${process.env.NEXT_PUBLIC_YORDAR_MARKETING_URL}/api/active-campaign`;

// CHECKOUT
export const CHECKOUT_DETAILS_ENDPOINT = '/api/customers/checkout_details.json';
export const DYNAMIC_CHECKOUT_ORDER_ENDPOINT = (query = {}) => {
  const queryParams = getQueryParams({ approvedParams: checkoutOrderParams, query });
  return `api/orders/checkout.json${queryParams}`;
};
export const DYNAMIC_UPDATE_ORDER_ENDPOINT = (orderID) => `api/orders/${orderID}.json`;
export const DYNAMIC_CHECKOUT_DATE_VALIDATION = (orderID) => `/api/orders/${orderID}/validate_checkout_date.json`;
export const CHECKOUT_DATE_VALIDATION = '/api/orders/validate_checkout_date.json';
export const DYNAMIC_ORDER_MINIMUM_CHECK_ENDPOINT = (orderID) => `/api/orders/${orderID}/check_minimum_spend.json`;
export const DYNAMIC_WOOLWORTHS_ORDER_VALIDATION_ENDPOINT = (orderID) =>
  `/api/orders/${orderID}/validate_woolworths_order.json`;
export const DYNAMIC_ORDER_SUPPLIER_AVAILABILITY_CHECK_ENDPOINT = (orderID) =>
  `/api/orders/${orderID}/check_supplier_suburb_availability.json`;
export const SUBURBS_ENDPOINT = '/api/suburbs.json';
export const NEW_STRIPE_CARD_ENDPOINT = '/api/stripe_credit_cards.json';
export const DYNAMIC_CHECKOUT_SUBMIT_ENDPOINT = (orderID) => `api/orders/${orderID}/submit.json`;
export const DYNAMIC_FETCH_DELIVERY_WINDOWS_ENDPOINT = (orderID) => `api/orders/${orderID}/fetch_delivery_windows.json`;
export const CHECKOUT_PAGE = '/checkout';

// Custom Order endpoints
export const CUSTOM_ORDER_SUPPLIERS_ENDPOINT = '/api/custom_orders/fetch_suppliers.json';
export const DYNAMIC_CUSTOM_ORDER_SUPPLIER_MENU_ENDPOINT = ({ slug }) => {
  return `/api/custom_orders/supplier_menu/${slug}.json`;
};

// ORDER EDIT
export const DYNAMIC_EDIT_DETAILS_ENDPOINT = (orderID) => `/api/customers/checkout_details.json?order_id=${orderID}`;
export const DYNAMIC_ORDER_EDIT_ENDPOINT = (orderID, finaliseQuote) =>
  `api/orders/${orderID}/edit.json${finaliseQuote ? '?finaliseQuote=true' : ''}`;
export const DYNAMIC_CHECKOUT_EDIT_ENDPOINT = (orderID) => `api/orders/${orderID}/amend.json`;
export const DYNAMIC_SUPPLIER_ADD_MORE_ENDPOINT = (orderID, supplierID) =>
  `/c_profile/orders/add-more/${orderID}?supplier_id=${supplierID}`;
export const DYNAMIC_ORDER_EDIT_PAGE = (orderID) => `/checkout/edit/${orderID}`;

// ORDER LOADING DOCKS
export const DYNAMIC_LOADING_DOCKS_REQUEST_ENDPOINT = (orderUUID, requestUUID) =>
  `api/orders/${orderUUID}/loading_docks/new.json?requestUUID=${requestUUID}`;
export const DYNAMIC_LOADING_DOCK_ENDPOINT = (orderUUID) => `api/orders/${orderUUID}/loading_docks.json`;

// EMPLOYEE SURVEY
export const DYNAMIC_EMPLOYEE_SURVEY_ENDPOINT = (uuid) => `/api/employee_surveys/${uuid}.json`;
export const DYNAMIC_EMPLOYEE_SURVEY_SUBMISSION_ENDPOINT = (uuid) => `api/employee_surveys/${uuid}/submit.json`;

// TEAM ORDER
export const DYNAMIC_ATTENDEE_PACKAGE_ENDPOINT = (uuid) => `/api/team_order_attendees/package/${uuid}.json`;
export const DYNAMIC_ATTENDEE_CHECKOUT_ENDPOINT = (uuid) => `/api/team_order_attendees/${uuid}/checkout`;
export const DYNAMIC_TEAM_ORDER_ENDPOINT = (uuid) => `team-orders/attendee-order/${uuid}.json`;

// QUOTES
export const DYNAMIC_CUSTOMER_QUOTES_ENDPOINT = ({ uuid }) => `/api/quotes.json${uuid ? `?uuid=${uuid}` : ''}`;
export const DYNAMIC_QUOTE_CUSTOMER_ENDPOINT = (uuid) => `/api/quotes/customer/${uuid}`;

// IMAGE PATH
export const DYNAMIC_CLOUDINARY_PATH = ({ width, height }) =>
  `https://res.cloudinary.com/yordar-p/image/upload/w_${width},h_${height},c_fill,q_auto,fl_lossy,f_auto`;
