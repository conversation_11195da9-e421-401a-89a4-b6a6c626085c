import { MenuScrollingCategories, MajorMenuCategories } from 'components/SupplierShow';

const MenuNav = ({ menuSections, dualNav = false, aisleAndSection, setAisleAndSection }) => {
  const { activeSection, activeAisle } = aisleAndSection;

  return (
    <div className="supplier-menu-banner-wrapper">
      {dualNav ? (
        <>
          <div className="supplier-menu-banner aisle">
            <MajorMenuCategories
              menuSections={menuSections}
              setAisleAndSection={setAisleAndSection}
              activeGroup={activeAisle || 0}
              isAisle
            />
          </div>
          <div className="supplier-menu-banner">
            <MajorMenuCategories
              menuSections={menuSections[activeAisle]?.menu_sections}
              setAisleAndSection={setAisleAndSection}
              activeGroup={activeSection || 0}
            />
          </div>
        </>
      ) : (
        <div className="supplier-menu-banner">
          <MenuScrollingCategories menuSections={menuSections} />
        </div>
      )}
    </div>
  );
};

export default MenuNav;
