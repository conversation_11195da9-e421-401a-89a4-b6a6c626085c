import Select from 'react-select';

import useCheckoutStore from 'store/useCheckoutStore';

import VisaIcon from 'images/icons/visa.svg';
import MasterCardIcon from 'images/icons/mastercard.svg';
import AmexIcon from 'images/icons/amex.svg';
import Image from 'next/image';

const PayBySavedCard = ({ savedCreditCards }) => {
  const iconsMap = {
    visa: VisaIcon,
    mastercard: MasterCardIcon,
    amex: AmexIcon,
  };
  const {
    setOrderDetail,
    order: { creditCardId },
  } = useCheckoutStore();
  const cardOptions = savedCreditCards.map((card) => ({
    value: card.id,
    label: `${card.name} ending in ${card.last4} (expires ${card.expiry_month}/${card.expiry_year})`,
    brand: card.brand,
    icon: iconsMap[card.brand],
  }));
  const selectedCard = savedCreditCards?.find((savedCard) => savedCard.id === creditCardId);
  return (
    <div className="credit-card-container">
      <div className="saved-cards-select">
        {!!creditCardId && (
          <p>
            This order will be <strong>individually invoiced</strong>.
          </p>
        )}

        <Select
          options={cardOptions}
          value={cardOptions.find((option) => option.value === creditCardId)}
          onChange={(selectedOption) => setOrderDetail('creditCardId', selectedOption.value)}
          isSearchable={false}
          classNamePrefix="react-select"
          getOptionLabel={(e) => (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Image src={e.icon.src} height={30} width={30} />
              <span style={{ marginLeft: 5 }}>{e.label}</span>
            </div>
          )}
          placeholder="Select Saved Card"
        />
        {!!selectedCard && (
          <div className="notice">
            {selectedCard.brand_label} credit card payments will incur a {selectedCard.surcharge_percent}%{' '}
            {!!selectedCard.surcharge_fee && `+ ${selectedCard.surcharge_fee * 100}c surcharge`}.
          </div>
        )}
      </div>
    </div>
  );
};

export default PayBySavedCard;
