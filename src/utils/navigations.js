export const header = ({ appURL, isAdmin = false }) => ({
  loggedInLinks: [
    ...(isAdmin ? [{ className: 'icon icon-login', to: `${appURL}/admin`, text: 'Admin' }] : []),
    {
      className: 'icon icon-truck',
      to: `${appURL}/c_profile`,
      text: 'My Orders',
    },
    {
      className: 'icon icon-team',
      to: `${appURL}/c_profile/new_team_order`,
      text: 'Team Orders',
    },
    {
      className: 'icon icon-bill',
      to: `${appURL}/c_profile/invoices`,
      text: 'Invoices',
    },
    {
      className: 'icon icon-card',
      to: `${appURL}/c_profile/payment_options`,
      text: 'Credit Cards',
    },
    {
      className: 'icon icon-shopcart-dark',
      to: '/checkout',
      text: 'Cart',
    },
  ],
  loggedOutLinks: [
    {
      className: 'icon icon-login',
      to: `${appURL}/login`,
      text: 'Login',
    },
    {
      className: 'icon icon-register',
      to: `${appURL}/register`,
      text: 'Register',
    },
  ],
});

export const footer = (marketingURL) => ({
  catering: [
    {
      className: 'icon icon-toaster',
      to: `${marketingURL}/breaky`,
      text: 'Breaky',
    },
    {
      className: 'icon icon-teapot',
      to: `${marketingURL}/morning-tea`,
      text: 'Morning Tea',
    },
    {
      className: 'icon icon-restaurant',
      to: `${marketingURL}/staff-lunch`,
      text: 'Staff Lunch',
    },
    {
      className: 'icon icon-teacup',
      to: `${marketingURL}/afternoon-tea`,
      text: 'Afternoon Tea',
    },
    {
      className: 'icon icon-hand-right',
      to: `${marketingURL}/finger-food`,
      text: 'Finger Food',
    },
    {
      className: 'icon icon-restaurant',
      to: `${marketingURL}/grazing-table-catering`,
      text: 'Grazing Tables',
    },
    {
      className: 'icon icon-indigenous',
      to: `${marketingURL}/indigenous-catering`,
      text: 'Indigenous Catering',
    },
    {
      className: 'icon icon-plus',
      to: `${marketingURL}/office-catering`,
      text: 'View all',
    },
  ],
  pantry: [
    {
      className: 'icon icon-bread',
      to: `${marketingURL}/office-bread`,
      text: 'Office Bread',
    },
    {
      className: 'icon icon-solo-cup',
      to: `${marketingURL}/office-drinks`,
      text: 'Office Drinks',
    },
    {
      className: 'icon icon-coffee',
      to: `${marketingURL}/office-coffee-supplies`,
      text: 'Office Coffee Supplies',
    },
    {
      className: 'icon icon-apple',
      to: `${marketingURL}/office-fruit`,
      text: 'Office Fruit',
    },
    {
      className: 'icon icon-wine-glass',
      to: `${marketingURL}/office-alcohol`,
      text: 'Office Alcohol',
    },
    {
      className: 'icon icon-milk',
      to: `${marketingURL}/office-milk`,
      text: 'Office Milk',
    },
    {
      className: 'icon icon-chocolate-bar',
      to: `${marketingURL}/office-snacks-pantry`,
      text: 'Office Snacks',
    },
    {
      className: 'icon icon-shopping-basket',
      to: `${marketingURL}/pantry-management`,
      text: 'Pantry Management',
    },
    {
      className: 'icon icon-plus',
      to: `${marketingURL}/kitchen-and-pantry`,
      text: 'View all',
    },
  ],
  somethingElse: [
    {
      className: 'icon icon-team',
      to: `${marketingURL}/team-ordering`,
      text: 'Team Ordering',
    },
    {
      className: 'icon icon-event',
      to: `${marketingURL}/events`,
      text: 'Event Catering',
    },
    {
      className: 'icon icon-user',
      to: `/quotes`,
      text: 'Get A Quote',
    },
    {
      className: 'icon icon-eco-friendly',
      to: `${marketingURL}/yordar-sustainability`,
      text: 'Yordar Sustainability',
    },
    {
      className: 'icon icon-diverse',
      to: `${marketingURL}/diversity-and-inclusion`,
      text: 'Diversity & Inclusion',
    },
  ],
  help: [
    {
      className: 'icon icon-about-us',
      to: `${marketingURL}/about-us`,
      text: 'About Us',
    },
    {
      className: 'icon icon-why-yordar',
      to: `${marketingURL}/why-yordar`,
      text: 'Why Yordar',
    },
    {
      className: 'icon icon-contact-us',
      to: `${marketingURL}/contact-us`,
      text: 'Contact Us',
    },
    {
      className: 'icon icon-truck',
      to: `${marketingURL}/supplier-partnership`,
      text: 'For Suppliers',
    },
    {
      className: 'icon icon-hand-right',
      to: `${marketingURL}/blog`,
      text: 'Blog',
    },
  ],
  locations: [
    {
      className: 'icon icon-marker',
      to: `${marketingURL}/office-catering/sydney`,
      text: 'Sydney',
    },
    {
      className: 'icon icon-marker',
      to: `${marketingURL}/office-catering/melbourne`,
      text: 'Melbourne',
    },
    {
      className: 'icon icon-marker',
      to: `${marketingURL}/office-catering/brisbane`,
      text: 'Brisbane',
    },
    {
      className: 'icon icon-marker',
      to: `${marketingURL}/office-catering/perth`,
      text: 'Perth',
    },
    {
      className: 'icon icon-marker',
      to: `${marketingURL}/office-catering/adelaide`,
      text: 'Adelaide',
    },
    {
      className: 'icon icon-marker',
      to: `${marketingURL}/office-catering/canberra`,
      text: 'Canberra',
    },
  ],
});
