import useEmployeeSurveyStore from 'store/useEmployeeSurveyStore';

const TextQuestion = ({ question, answer, setAnswer }) => {
  const { updateStep } = useEmployeeSurveyStore((state) => ({
    updateStep: state.updateStep,
  }));

  const handleKeyUp = (event) => {
    if (event.which === 13 || event.keyCode === 13) updateStep(true);
  };

  const handleChange = (event) => {
    setAnswer((state) => ({ ...state, value: event.target.value }));
    adjustTextareaHeight(event.target);
  };

  const adjustTextareaHeight = (textarea) => {
    const computedStyle = window.getComputedStyle(textarea, null);
    const borderHeight =
      parseInt(computedStyle.getPropertyValue('border-top-width'), 10) +
      parseInt(computedStyle.getPropertyValue('border-bottom-width'), 10);

    textarea.style.height = 0; // Temporarily collapse to measure only the content
    textarea.style.height = `${textarea.scrollHeight + borderHeight}px`;
  };

  return (
    <textarea
      rows="1"
      className="survey-question"
      value={answer?.value}
      onKeyUp={handleKeyUp}
      onChange={handleChange}
      style={{ overflowY: 'hidden', resize: 'none' }}
    />
  );
};

export default TextQuestion;
