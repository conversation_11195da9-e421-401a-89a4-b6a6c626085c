import keyGenerator from 'utils/keyGenerator';

const MenuSectionSkeleton = () => (
  <div className="menu-sections loading">
    <div className="menu-section">
      <div className="menu-section-title" />
      <ul className="menu-section-items">
        {new Array(10).fill().map(() => (
          <li className="menu-section-item" key={keyGenerator('skeleton-card')} />
        ))}
      </ul>
    </div>
  </div>
);

export default MenuSectionSkeleton;
