import create from 'zustand';

const initialDeliveryDateErrors = {
  leadTime: null,
  isOutsideOperatingHours: false,
  isClosed: false,
  closureSuppliers: [],
};

const initialState = {
  delivery_date: null,
  delivery_time: null,
  delivery_at: null,
  dateValidated: false,
  dateErrors: initialDeliveryDateErrors,
  showDateModal: false,
  modalIsClosable: false,
};

const useDeliveryDatesStore = create((set, get) => ({
  ...initialState,
  setDate: (deliveryDate) => {
    set({ delivery_date: deliveryDate });
  },
  setTime: (deliveryTime) => {
    set({ delivery_time: deliveryTime });
  },
  setDeliveryAt: (deliveryAt) => {
    set({ delivery_at: deliveryAt });
  },
  setOpenDeliveryModal: (open, errors) => {
    const { dateErrors } = get();
    set({ showDateModal: open, dateErrors: { ...dateErrors, ...errors } });
  },
  setDateValidated: (dateValidated) => {
    set({ dateValidated });
  },
  setModalIsClosable: (modalIsClosable) => {
    set({ modalIsClosable });
  },
  clearDate: () => {
    localStorage.removeItem('deliveryDate');
    localStorage.removeItem('deliveryTime');
    set({ ...initialState });
  },
}));

export default useDeliveryDatesStore;
