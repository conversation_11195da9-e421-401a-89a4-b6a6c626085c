import { useState } from 'react';
import { Calendar } from '@hassanmojab/react-modern-calendar-datepicker';
import '@hassanmojab/react-modern-calendar-datepicker/lib/DatePicker.css';

import { TimeSlotList } from 'components/Checkout';
import { ClosureSupplier } from 'components/Common';
import useDeliveryDateStore from 'store/useDeliveryDateStore';
import { dateAndTimeFromLocalStorage } from 'utils/dateTime';

const initialErrorState = {
  leadTime: null,
  isOutsideOperatingHours: false,
  isClosed: false,
  closureSuppliers: [],
};

const DeliveryDateSupplierModal = ({ setModalOpen }) => {
  const { date, time } = dateAndTimeFromLocalStorage();
  const [selectedDay, setSelectedDay] = useState(date);
  const [selectedTime, setSelectedTime] = useState(time);
  const [deliveryError, setDeliveryError] = useState(initialErrorState);
  const { setDate, setTime } = useDeliveryDateStore((state) => ({
    setDate: state.setDate,
    setTime: state.setTime,
    setDeliveryAt: state.setDeliveryAt,
  }));

  function minimumDate() {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    // Note: getMonth() returns 0-11, but react-modern-calendar-datepicker expects 1-12 for months
    const currentMonth = currentDate.getMonth() + 1;
    const currentDay = currentDate.getDate();

    return {
      year: currentYear,
      month: currentMonth,
      day: currentDay,
    };
  }

  function clearErrors() {
    setDeliveryError(initialErrorState);
  }

  const submitDeliveryDate = async () => {
    if (!selectedDay || !selectedTime) return;

    if (selectedDay) {
      const dateString = `${selectedDay.day}/${selectedDay.month}/${selectedDay.year}`;
      setDate(dateString);
      localStorage.setItem('deliveryDate', dateString);
    }
    if (selectedTime) {
      setTime(selectedTime.display);
      localStorage.setItem('deliveryTime', selectedTime.display);
    }

    setModalOpen(false);
  };

  return (
    <div>
      <h3>Select Delivery Date & Time</h3>
      <p className="notice">
        Please note: Hot food may arrive up to 15 minutes prior to the delivery time, cold food may arrive up to 60
        minutes prior to the delivery time.
      </p>
      <div className="date-time-container">
        <Calendar
          value={selectedDay}
          onChange={(e) => {
            clearErrors();
            setSelectedDay(e);
          }}
          colorPrimary="#000"
          minimumDate={minimumDate()}
        />
        <TimeSlotList onChange={setSelectedTime} value={selectedTime.display} clearErrors={clearErrors} />
      </div>
      {!!deliveryError.leadTime && !deliveryError.closureSuppliers.length && (
        <p className="delivery-date-error">
          Your order requires a minimum lead time of {deliveryError.leadTime}. If you proceed with the order it may be
          rejected by the supplier(s)
        </p>
      )}
      {!!deliveryError.closureSuppliers.length && (
        <>
          {deliveryError.closureSuppliers.map((supplier) => (
            <div key={`supplier-closure-${supplier.id}`}>
              <ClosureSupplier supplier={supplier} />
            </div>
          ))}
          {deliveryError.isClosed && (
            <p className="delivery-date-error">Please place your order before or after these dates.</p>
          )}
          {!deliveryError.isClosed && deliveryError.isOutsideOperatingHours && (
            <p className="delivery-date-error">
              If you proceed the {deliveryError.closureSuppliers.length > 1 ? 'suppliers' : 'supplier'} may reject the
              order.
            </p>
          )}
        </>
      )}
      <button
        onClick={deliveryError.isClosed ? null : submitDeliveryDate}
        className={`button black ${deliveryError.isClosed ? 'disable-date' : ''}`}
        style={{ width: '100%' }}
      >
        {(deliveryError.leadTime || deliveryError.isOutsideOperatingHours) && !deliveryError.isClosed
          ? 'Proceed Anyway'
          : 'Submit'}
      </button>
    </div>
  );
};

export default DeliveryDateSupplierModal;
