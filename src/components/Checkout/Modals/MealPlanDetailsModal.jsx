const MealPlanDetailsModal = ({ order, mealPlan, setOrderDetailsChange }) => (
  <div>
    <h3 className="section-heading">Do you also want to update the order details based on selected meal plan?</h3>

    <p>The following detail will be updated:</p>

    <p>
      Name for Order: <strong>{mealPlan.name}</strong>
    </p>
    <p>
      Number of People: <strong>{mealPlan.number_of_people}</strong>
    </p>

    {order.deliverySuburbId === mealPlan.delivery_suburb_id && (
      <>
        <p>
          Address:{' '}
          <strong>
            {mealPlan.delivery_address_level ? `${mealPlan.delivery_address_level}/` : ''}
            {mealPlan.delivery_address} {mealPlan.suburb_label}
          </strong>
        </p>
        <p>
          Delivery Instructions: <strong>{mealPlan.delivery_instruction}</strong>
        </p>
      </>
    )}
    {mealPlan.cpo_id && (
      <p>
        Purchase Order: <strong>`{mealPlan.po_number}`</strong>
      </p>
    )}
    {mealPlan.gst_free_cpo_id && (
      <p>
        GST FREE Purchase Order: <strong>`{mealPlan.gst_free_po_number}`</strong>
      </p>
    )}
    {mealPlan.department_identity && (
      <p>
        Cost Centre ID: <strong>`{mealPlan.department_identity}`</strong>
      </p>
    )}

    <p>
      Payment Option: <strong>{mealPlan.payment_option}</strong>
    </p>

    <div className="button-row-full-width">
      <button type="button" className="button primary" onClick={() => setOrderDetailsChange(true)}>
        YES
      </button>
      <button type="button" className="button grey" onClick={() => setOrderDetailsChange(false)}>
        NO
      </button>
    </div>
  </div>
);

export default MealPlanDetailsModal;
