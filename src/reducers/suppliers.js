import {
  FETCH_SUPPLIERS,
  FETCH_SINGLE_SUPPLIER,
  FETCH_SUPPLIER_MENU_SECTION,
  SET_FAVOURITE_ITEM,
  SET_FAVOURITE_SUPPLIER,
  LOADING_SUPPLIER_MENU,
} from 'actions/types';

const initialState = {
  index: [],
  listing: {},
};

function handleFavouriteSupplier(suppliers, payload) {
  const { id, isFavourite } = payload;
  return suppliers.map((supplier) => {
    if (supplier.id === id) {
      return {
        ...supplier,
        is_favourite: isFavourite,
      };
    }
    return supplier;
  });
}

function handleFavouriteItems(menuSections, payload) {
  const { id, isFavourite } = payload;
  let newState = [...menuSections];
  let favourites = newState.find((section) => section.name === 'Favourites');
  const menuItemInFavourites = favourites?.menu_items.find((item) => item.id === id);

  const sectionWithItem = newState.find((section) => section.menu_items.find((item) => item.id === id));
  const menuItemToChange = sectionWithItem.menu_items.find((item) => item.id === id);

  // If favourite section doesn't exist, create it..
  if (!favourites) {
    newState = [{ name: 'Favourites', id: 'favourites', menu_items: [] }, ...newState];
  }
  // If favourite section exists, but it only has one item that matches payload id, remove the whole section.
  if (favourites && favourites.menu_items.length === 1 && favourites.menu_items[0].id === id) {
    newState = newState.filter((section) => section.name !== 'Favourites');
  }
  // If item is in favourites section, remove it.
  if (menuItemInFavourites) {
    newState = newState.map((section) =>
      section.name === 'Favourites'
        ? { ...section, menu_items: section.menu_items.filter((item) => item.id !== id) }
        : section
    );
  }
  // If item is not in favourites section, add it.
  else {
    favourites = newState.map((section) =>
      section.name === 'Favourites' ? { ...section, menu_items: section.menu_items.unshift(menuItemToChange) } : section
    );
  }
  return newState.map((section) => ({
    ...section,
    menu_items: section.menu_items.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          is_favourite: isFavourite,
        };
      }
      return item;
    }),
  }));
}

export const suppliers = (state = initialState, action) => {
  switch (action.type) {
    case FETCH_SUPPLIERS: {
      return {
        ...state,
        index: action.payload,
      };
    }
    case FETCH_SINGLE_SUPPLIER: {
      return {
        ...state,
        listing: action.payload,
      };
    }
    case FETCH_SUPPLIER_MENU_SECTION: {
      return {
        ...state,
        listing: {
          ...state.listing,
          activeSection: action.payload.section_grouped_menu_items[0],
        },
      };
    }
    case SET_FAVOURITE_SUPPLIER: {
      return {
        ...state,
        index: handleFavouriteSupplier(state.index, action.payload),
      };
    }
    case SET_FAVOURITE_ITEM: {
      return {
        ...state,
        listing: {
          ...state.listing,
          section_grouped_menu_items: handleFavouriteItems(state.listing.section_grouped_menu_items, action.payload),
        },
      };
    }
    case LOADING_SUPPLIER_MENU:
      return {
        ...state,
        listing: {
          ...state.listing,
          loadingMenu: action.payload,
        },
      };
    default:
      return state;
  }
};
