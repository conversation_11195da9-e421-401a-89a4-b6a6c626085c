import { useState } from 'react';

import { AddressList, CheckoutAddressForm } from 'components/Checkout';

const DeliveryAddressModal = ({ setModalOpen }) => {
  const [activeAddress, setActiveAddress] = useState(null);

  return (
    <div className="checkout-address">
      <div style={{ marginBottom: '20px', display: 'flex' }}>
        {activeAddress && (
          <i
            className="icon icon-chevron-left"
            style={{ marginRight: '5px' }}
            onClick={() => {
              setActiveAddress(null);
            }}
          />
        )}
      </div>
      {!activeAddress && <AddressList setActiveAddress={setActiveAddress} />}
      {activeAddress && (
        <CheckoutAddressForm
          activeAddress={activeAddress}
          setActiveAddress={setActiveAddress}
          setModalOpen={setModalOpen}
        />
      )}
    </div>
  );
};

export default DeliveryAddressModal;
