.show-package {
  width: 80vw;
  padding: 20px 40px;
  margin: auto;
  max-width: 1100px;
  &.team-attendee {
    margin: initial;
    justify-self: center;
    @include media-down(hamburger) {
      padding: 40px 40px;
    }
  }
  @include media-down(small-tablet) {
    width: 100%;
    padding: 20px 10px;
  }
  &-banner {
    margin: 4rem 0 3rem;
    text-align: center;
    p {
      margin-top: 2rem;
    }
  }
}

.team-order-package-page {
  height: 100%;
  background-color: #fafbfb;
  display: grid;
  p {
    margin-bottom: 0;
  }
}

.team-attendee-week {
  display: grid;
  margin-bottom: 12px;
  grid-template-columns: repeat(auto-fit, minmax(300px, max-content));
  grid-gap: 16px;
  justify-content: center;
  @include media-down(mobile) {
    grid-template-columns: 1fr;
    grid-gap: 30px;
    .team-attendee-day {
      max-width: initial;
    }
  }
}

.team-attendee-date-change {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
  @include media-down(hamburger) {
    justify-content: space-between;
  }
  .calendar-date-change {
    display: flex;
    align-items: center;
    border-radius: 50%;
    background-color: white;
    aspect-ratio: 1 / 1;
    margin: 0;
    filter: invert(1);
    font-size: 7px;
    border: none;
    max-width: 40px;
    &::after {
      content: '';
      display: inline-block;
      width: 12px;
      height: 20px;
      background-image: url('../../../images/icons/chevron-left.svg');
      background-size: cover;
      background-repeat: no-repeat;
      background-position: 65%;
    }
    &.forward {
      transform: rotate(180deg);
    }
    &:hover {
      background-color: #e5798d;
    }
  }
}

.admin-order-calendar-container {
  text-align: center;
  .DatePicker__input {
    font-size: 16px;
    background: white;
    color: black;
    border-radius: 20px;
    padding: 10px 50px;
    margin: 0 20px;
    @include media-down(large-mobile) {
      padding: 10px 16px;
      margin: 0;
    }
  }
}

.team-attendee-card {
  border: 1px solid #e9e9e9;
  border-radius: 4px;
}

.team-orderline-info {
  color: grey;
  font-size: 14px;
}

.team-attendee-supplier-card__details {
  padding: 6px;
  padding-bottom: 8px;
  text-align: center;
  background: white;
}

.team-attendee-day {
  max-width: 360px;
  width: 100%;
  .expiry-text {
    display: flex;
    align-items: center;
    margin: 8px 0;
    font-weight: bold;
    font-family: $heading-font;
    &::before {
      content: '';
      display: inline-block;
      width: 20px;
      height: 20px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      margin-right: 20px;
    }
    &.ordered, &.past-order {
      font-weight: bold;
      color: $primary;
      &::before {
        background-image: url('../../../images/icons/check-circle.svg');
      }
    }
    &.invited {
      &::before {
        border: 1px solid gray;
        border-radius: 50%;
      }
    }
    &.declined {
      &::before {
        background-image: url('../../../images/icons/cancel-red.svg');
      }
    }
    &.expired {
      &::before {
        background: #d6d6d6;
        border-radius: 50%;
      }
    }
  }
}

.team-attendee-button-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  .button:first-of-type {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
  }
  .button:last-of-type {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .gray-btn {
    background: #e4e5e9;
    color: gray;
  }
  p {
    display: flex;
    align-items: center;
    margin-bottom: 0;
    font-size: 14px;
    &::before {
      content: '';
      display: inline-block;
      width: 20px;
      height: 20px;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      margin-right: 8px;
      margin-left: 4px;
    }
  }
  .button {
    flex: 1;
    text-transform: none;
    font-family: $body-font;
    border: none;
  }
  .invited, .ordered {
    color: black;
    background: white;
    transition: background 0.2s ease;
    border-top: 1px solid #e7e7e7;
    &:hover {
      background: black;
      color: white;
    }
  }
  .past-order {
    grid-column: span 2;
    background: white;
    color: black;
    cursor: default;
    &:hover {
      color: black;
      background: white;
    }
  }
  .declined {
    background: white;
    cursor: default;
    background: #e4e5e9;
    color: gray;
    border-color: #e4e5e9;
    border-right: 1px solid #c5c5c5 !important
  }
  .expired {
    color: black;
    background: white;
    grid-column: span 2;
  }
}

.expiry-container {
  flex: 1;
  text-align: center;
  background: white;
  font-size: 14px;
  font-family: $heading-font;
  .ordered {
    color: $primary;
    &::before {
      background-image: url('../../../images/icons/truck-black.svg');
    }
  }
  .declined {
    color: red;
  }
}




