import { useState } from 'react';

const dietariesMap = {
  GF: 'Gluten Free',
  V: 'Vegetarian',
  VE: 'Vegan',
  IP: 'Individually Packed',
  DF: 'Dairy Free',
  NF: 'Nut Free',
  EF: 'Egg Free',
  H: 'Halal',
  K: 'Kosher',
};

const Dietary = ({ dietary }) => {
  const [tooltipActive, setTooltipActive] = useState(false);
  return (
    <div style={{ position: 'relative' }}>
      <span
        className={`letter-icon ${dietary}`}
        key={dietary}
        onMouseEnter={() => setTooltipActive(true)}
        onMouseLeave={() => setTooltipActive(false)}
      >
        {dietary}
      </span>
      {tooltipActive && (
        <p className="icons-tooltip dietary">
          <li>{dietariesMap[dietary]}</li>
        </p>
      )}
    </div>
  );
};

export default Dietary;
