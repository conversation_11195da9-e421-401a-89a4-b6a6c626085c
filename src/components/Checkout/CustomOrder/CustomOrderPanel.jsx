import { useState } from 'react';
import useCheckoutStore from 'store/useCheckoutStore';

import { BlankCustomOrder, CustomOrderLineModal, LocationSection, LocationInput } from 'components/Checkout';

const CustomOrderPanel = () => {
  const { orders, activeOrderID } = useCheckoutStore((state) => ({
    orders: state.orders,
    activeOrderID: state.activeOrderID,
  }));

  const activeOrder = orders?.[activeOrderID];
  const orderLocations = activeOrder?.locations || {};
  const hasOrderLocations = !!Object.keys(orderLocations).length;

  const [showLocationInput, setShowLocationInput] = useState(!Object.keys(orderLocations).length);

  return (
    <>
      <div className="checkout-order">
        {!hasOrderLocations && <BlankCustomOrder />}

        {Object.keys(orderLocations).map((locationID) => (
          <LocationSection key={locationID} location={orderLocations[locationID]} />
        ))}

        <CustomOrderLineModal />
      </div>
      <div>
        {hasOrderLocations && (
          <div>
            <button
              type="button"
              onClick={() => setShowLocationInput(true)}
              className="button fw black"
              style={{ marginTop: '1rem', padding: '8px' }}
            >
              + Add Another Section
            </button>
          </div>
        )}
        {showLocationInput && <LocationInput setShowLocationInput={setShowLocationInput} />}
      </div>
    </>
  );
};

export default CustomOrderPanel;
