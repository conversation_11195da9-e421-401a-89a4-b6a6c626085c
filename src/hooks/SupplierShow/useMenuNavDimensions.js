import { useEffect, useState } from 'react';

// Code taken from https://stackoverflow.com/a/60978633/11616583
// Same except for changing dependency array to listen for which menu section is currently active
// Reason for this is for overflowing categories, as the spawning of the 'More' dropdown changes available width

const useMenuNavDimensions = (myRef, visibleMenuSection) => {
  const getDimensions = () => ({
    width: myRef.current.offsetWidth,
    height: myRef.current.offsetHeight,
  });

  const [dimensions, setDimensions] = useState({ width: null, height: null });

  useEffect(() => {
    const handleResize = () => {
      setDimensions(getDimensions());
    };

    if (myRef.current) {
      setDimensions(getDimensions());
    }

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [visibleMenuSection]);

  return dimensions;
};

export default useMenuNavDimensions;
