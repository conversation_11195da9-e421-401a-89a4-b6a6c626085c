import { useEffect, useRef, useState } from 'react';

import { NumberButton } from 'components/SupplierShow';

const MenuItemOption = ({
  itemId,
  defaultQuantity,
  maxQuantity,
  minQuantity,
  serving,
  selectedMenuItems,
  setSelectedMenuItems,
}) => {
  const [menuItemValue, setMenuItemValue] = useState(defaultQuantity);
  const [openNote, setOpenNote] = useState(false);
  const [note, setNote] = useState('');
  const [savedNote, setSavedNote] = useState(null);
  const [showQuantityExceeded, setShowQuantityExceeded] = useState(null);
  const [showQuantitySubceeded, setShowQuantitySubceeded] = useState(null);
  const quantityInput = useRef(null);

  useEffect(() => {
    const filteredItems = selectedMenuItems.filter((selectedItem) => selectedItem.serving.name !== serving.name);
    if (menuItemValue > 0) {
      setSelectedMenuItems([...filteredItems, { serving, itemId, quantity: menuItemValue, note: savedNote }]);
    } else {
      setSelectedMenuItems(filteredItems);
    }
  }, [menuItemValue, savedNote]);

  useEffect(() => {
    if (defaultQuantity) {
      quantityInput.current.focus();
    }
  }, [quantityInput.current]);

  const handleSaveNote = () => {
    setSavedNote(note);
    setOpenNote(false);
  };
  const handleCancelNote = () => {
    setSavedNote(null);
    setOpenNote(false);
    setNote('');
  };

  const handleMenuItemValue = (inputValue) => {
    if (!inputValue || inputValue < 0) return setMenuItemValue('');
    setMenuItemValue(inputValue ? Number(inputValue) : '');
  };

  const handleMinQuantity = () => {
    if (!minQuantity || minQuantity === 1) return;
    if (menuItemValue < minQuantity && menuItemValue !== 0) {
      return setShowQuantitySubceeded(true);
    }
    return setShowQuantitySubceeded(false);
  };

  const handleMaxQuantity = () => {
    if (!maxQuantity) return null;
    if (menuItemValue <= maxQuantity) {
      return setShowQuantityExceeded(false);
    }
    setMenuItemValue(36);
    return setShowQuantityExceeded(true);
  };

  return (
    <>
      <div className="menu-item-modal-option-container">
        <div className="menu-item-modal-option">
          <span className="menu-item-modal-option-name">
            {serving.name}
            {serving.over_budget && <span className="menu-item-serving-over-budget"> (Over budget)</span>}
          </span>
          <span
            className={`menu-item-modal-option-price${serving.discount ? ' menu-item-modal-option-price--custom' : ''}${serving.over_budget ? ' over-budget' : ''
              }`}
          >
            {serving.discount ? `$${serving.discount}` : `$${serving.price}`}
          </span>
          <span className="menu-item-modal-add-note" onClick={() => setOpenNote((open) => !open)} role="presentation">
            {openNote ? '' : 'Add Note'}
          </span>
          <div className="menu-item-modal-input">
            <NumberButton
              menuItemValue={menuItemValue > 0 ? menuItemValue - 1 : 0}
              setMenuItemValue={setMenuItemValue}
              operation="-"
              setShowQuantitySubceeded={setShowQuantitySubceeded}
              minQuantity={minQuantity}
              handleMinQuantity={handleMinQuantity}
            />
            <input
              type="number"
              value={menuItemValue}
              ref={quantityInput}
              onFocus={(e) => e.currentTarget.select()}
              onChange={(e) => handleMenuItemValue(e.target.value)}
              onBlur={() => {
                handleMinQuantity();
                handleMaxQuantity();
              }}
            />
            <NumberButton
              menuItemValue={menuItemValue + 1}
              setMenuItemValue={setMenuItemValue}
              operation="+"
              setShowQuantitySubceeded={setShowQuantitySubceeded}
              minQuantity={minQuantity}
              handleMinQuantity={handleMinQuantity}
            />
          </div>
        </div>
        {openNote && (
          <input
            type="text"
            placeholder="Extra sauce, no onions, etc"
            className="menu-item-modal-note"
            value={note}
            onChange={(e) => setNote(e.target.value)}
          />
        )}
        {openNote && (
          <div className="note-buttons">
            <button className="button tiny" type="button" onClick={handleSaveNote}>
              Save
            </button>
            <button className="button tiny black" type="button" onClick={handleCancelNote}>
              Cancel
            </button>
          </div>
        )}
        {showQuantityExceeded && (
          <div className="quantity-warning">
            <p>You can only order a maximum of {maxQuantity} items</p>
          </div>
        )}
        {showQuantitySubceeded && (
          <div className="quantity-warning">
            <p>Warning: The supplier has requested a minimum of {minQuantity} items</p>
          </div>
        )}
      </div>
    </>
  );
};

export default MenuItemOption;
