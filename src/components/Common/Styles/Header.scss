.header {
  position: relative;
  z-index: 8;
  background: white;
  box-shadow: 0 0 15px 5px rgba(0,0,0,0.03);
  .wrapper {
    display: grid;
    grid-template-columns: 1fr 2fr auto;
    align-items: center;
    padding-top: 10px;
    padding-bottom: 10px;
    @include media-down(large-tablet) {
      grid-template-columns: 1fr 26px;
      padding: 12px 15px;
    }
  }
}

.sticky-header {
  position: sticky;
  top: 0px;
  animation: slide-down 0.4s ease-in-out;
}

@keyframes slide-down {
  0% {
    transform: translateY(-40px);
  }
  100% {
    transform: translateY(0);
  }
}