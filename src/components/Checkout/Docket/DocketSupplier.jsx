import { useState, useContext, useEffect } from 'react';

import useCheckoutStore from 'store/useCheckoutStore';
import { DYNAMIC_SUPPLIER_ADD_MORE_ENDPOINT } from 'api/endpoints';
import { Image, Link } from 'components/Common';
import { SupplierDeliveryOverride } from 'components/Checkout';
import { HostContext } from 'context/host';

const DocketSupplier = ({ supplier, index, checkoutOrder, activeOrder, checkoutPanel }) => {
  const { orderSuppliers } = useCheckoutStore((state) => ({
    orderSuppliers: state.orderSuppliers,
  }));
  const [showOverrideForm, setShowOverrideForm] = useState(false);
  const { appURL } = useContext(HostContext);
  const orderSupplier = orderSuppliers[supplier.id];

  const mealUUID = checkoutOrder?.mealPlan?.uuid;
  const supplierMenuLink = mealUUID ? `${orderSupplier.menu_link}?mealUUID=${mealUUID}` : orderSupplier.menu_link;
  const activeOrderSupplier = activeOrder?.orderSuppliers ? activeOrder.orderSuppliers[supplier.id] : null;
  const showLinks = checkoutPanel !== 'success';
  const showOverride = showLinks && activeOrderSupplier; // orders.orderSuppliers are only passed for Yordar Admin in order checkout JSON

  // show delivery fee override if present (only for admins)
  useEffect(() => {
    if (!activeOrderSupplier?.delivery_fee_override) return;

    setShowOverrideForm(true);
  }, [activeOrderSupplier?.delivery_fee_override]);

  if (!supplier.orderLineCount || !orderSupplier) return null;

  return (
    <>
      <div className="align" style={index !== 0 ? { marginTop: '12px' } : {}}>
        <div>
          <div className="align">
            <Link href={supplierMenuLink || ''} className="supplier-menu-link">
              <Image
                url={orderSupplier.image_id}
                width={44}
                height={44}
                quality={4}
                className="docket-icon"
                alt={`${orderSupplier.name}`}
              />
            </Link>
            <div>
              <Link href={supplierMenuLink || ''} className="supplier-menu-link">
                <p className="cart-supplier-name">
                  {orderSupplier.name}{' '}
                  <span style={{ paddingLeft: '4px', fontWeight: 'normal' }}>({supplier.orderLineCount} Items)</span>
                </p>
              </Link>
              {showOverride && (
                <a className="delivery-override" onClick={() => setShowOverrideForm(!showOverrideForm)}>
                  {showOverrideForm ? 'Hide Override' : 'Custom Delivery Fee'}
                </a>
              )}
            </div>
          </div>
        </div>

        {showLinks && !!supplierMenuLink && checkoutOrder.status === 'draft' && (
          <a style={{ marginLeft: 'auto', color: '#1f9e86', fontWeight: 'bold' }} href={supplierMenuLink}>
            Back to Menu
          </a>
        )}
        {showLinks && checkoutOrder.status !== 'draft' && (
          <a
            style={{ marginLeft: 'auto', color: '#1f9e86', fontWeight: 'bold' }}
            href={`${appURL}${DYNAMIC_SUPPLIER_ADD_MORE_ENDPOINT(activeOrder.id, supplier.id)}`}
          >
            Add more
          </a>
        )}
      </div>
      {showOverride && showOverrideForm && (
        <SupplierDeliveryOverride
          supplier={supplier}
          activeOrder={activeOrder}
          activeOrderSupplier={activeOrderSupplier}
        />
      )}
    </>
  );
};

export default DocketSupplier;
