import useQuoteStore from 'store/useQuoteStore';

const QuoteCategorySelect = ({ type, quoteCategories }) => {
  const { setMultiValueField, categories, noCategoryError, setFormField } = useQuoteStore((state) => ({
    setMultiValueField: state.setMultiValueField,
    setFormField: state.setFormField,
    noCategoryError: state.noCategoryError,
    categories: state.multiValueFields.categories,
  }));

  return (
    <div>
      <div className="category-select">
        {quoteCategories[type].map((category) => (
          <div key={category.name}>
            <label className="quote-category" htmlFor={`checkbox-${category.name}`}>
              <p className={`icon ${category.class}`}>{category.name}</p>
              <input
                type="checkbox"
                id={`checkbox-${category.name}`}
                onChange={() => setMultiValueField('categories', category.name)}
                checked={categories.includes(category.name)}
              />
              <span className="custom-checkbox" />
            </label>
          </div>
        ))}
        {noCategoryError && <p style={{ color: 'red' }}>Please Select a Category</p>}
      </div>
      <h3 className="quote-sub-heading">Something Else?</h3>
      <textarea
        className="quote-textarea"
        placeholder="Please specify what you're looking for"
        onBlur={(e) => setFormField('notListedCategory', e.target.value)}
      />
    </div>
  );
};

export default QuoteCategorySelect;
