import { useState, useContext } from 'react';

import { HostContext } from 'context/host';
import useCheckoutStore from 'store/useCheckoutStore';
import yordar from 'api/yordar';
import { SUBURBS_ENDPOINT, DYNAMIC_ORDER_SUPPLIER_AVAILABILITY_CHECK_ENDPOINT } from 'api/endpoints';

const CheckoutAddressForm = ({ activeAddress = {}, setActiveAddress, setModalOpen }) => {
  const { locality: countryCode } = useContext(HostContext);
  const {
    order: { id: orderID },
    setAddress,
  } = useCheckoutStore();
  const [unavailableSuburb, setUnavailableSuburb] = useState(false);

  function formatSuppliers({ suppliers, suburbLabel }) {
    if (suppliers.length === 1) {
      return `Sorry, ${suppliers[0]} can't deliver to ${suburbLabel}`;
    }

    const supplierNames = suppliers.slice(0, -1).join(', ');
    const lastSupplierName = suppliers[suppliers.length - 1];

    return `Sorry, ${supplierNames} and ${lastSupplierName} can't deliver to ${suburbLabel}`;
  }

  async function checkSupplierSuburbAvailablilty() {
    let availabilityResponse = null;
    if (activeAddress.suburb_id) {
      // only check order supplier availability for selected suburb
      const { data: supplierCheckResponse } = await yordar(
        DYNAMIC_ORDER_SUPPLIER_AVAILABILITY_CHECK_ENDPOINT(orderID),
        {
          params: { suburb_id: activeAddress.suburb_id },
        }
      );
      availabilityResponse = supplierCheckResponse;
    } else {
      // fetch best suburb based on passed in suburb info and check order supplier availability
      const suburbParams = {
        country_code: countryCode,
        postcode: activeAddress?.postcode,
        name: activeAddress?.suburb?.humanize(),
        best_matched_to: {
          postcode: activeAddress?.postcode,
          name: activeAddress?.suburb?.humanize(),
        },
        order_id: orderID, // passed for the availability check
      };

      const { data: suburbResponse } = await yordar(SUBURBS_ENDPOINT, {
        params: suburbParams,
      });
      availabilityResponse = suburbResponse;
    }

    setUnavailableSuburb(false);
    if (!availabilityResponse.is_available) {
      const unavailableSuppliers = availabilityResponse.suppliers.filter((supplier) => !supplier.has_delivery_zones);
      return setUnavailableSuburb({
        suppliers: unavailableSuppliers.map((supplier) => supplier.name),
        suburb: availabilityResponse.suburb.label,
      });
    }

    setAddress({
      deliveryAddress: activeAddress.street_address,
      deliveryAddressLevel: activeAddress.level,
      deliveryInstruction: activeAddress.instructions,
      deliverySuburbId: availabilityResponse.suburb.id,
      deliverySuburbLabel: availabilityResponse.suburb.label,
    });
    setModalOpen(false);
  }

  return (
    <div className="checkout-address-form">
      <input
        type="text"
        className="checkout-address-input"
        placeholder="Street Address"
        value={activeAddress.label || ''}
        readOnly
      />
      <input
        type="text"
        className="checkout-address-input"
        placeholder="Level, Floor or Suite"
        value={activeAddress.level || ''}
        onChange={(e) => setActiveAddress((state) => ({ ...state, level: e.target.value }))}
      />
      <textarea
        className="checkout-address-input"
        placeholder="Delivery Instructions"
        value={activeAddress.instructions || ''}
        onChange={(e) => setActiveAddress((state) => ({ ...state, instructions: e.target.value }))}
      />
      {unavailableSuburb && (
        <>
          <p className="error">
            {formatSuppliers({ suppliers: unavailableSuburb.suppliers, suburbLabel: unavailableSuburb.suburb })}
          </p>
          <button type="button" className="button primary" onClick={() => setActiveAddress(null)}>
            Back
          </button>
        </>
      )}
      {!unavailableSuburb && (
        <button type="button" className="button black" onClick={checkSupplierSuburbAvailablilty}>
          Submit
        </button>
      )}
    </div>
  );
};

export default CheckoutAddressForm;
