.DatePicker {
  margin-left:auto;
  width: 25vw;
  z-index: 1;
  cursor: pointer;
  @include media-down(large-tablet) {
    display: none;
  }
  &__input {
    padding: 13px;
    font-size: 16px;
    text-align: left;
    background: url('../../../images/icons/calendar.svg');
    background-position: 95%;
    background-repeat: no-repeat;
    background-size: 20px;
    cursor: pointer;
  }
  &__calendarContainer {
    box-shadow: 0 14px 36px 2px #bdbdbd8c;
  }
}

.sticky-wrapper {
  position: sticky;
  top: 60px;
  max-height: 100vh;
  overflow: hidden;
  @include media-down(large-tablet) {
    max-height: none;
  }
}

@include media-down(large-tablet) {
  .sticky-wrapper.mobile-open {
    position: fixed;
    max-height: 100vh;
    overflow-y: scroll;
    background: white;
    width: 100%;
    z-index: 10000;
    padding: 0 20px;
    top: 0;
    left: 0;
  }
  .sticky-wrapper.mobile-open .filters {
    display: block;
  }
  .sticky-wrapper.mobile-closed .filters {
    display: none;
    @include media-up(large-tablet) {
      display: block;
    }
  }
}

.mobile-search-and-filters {
  display: flex;
  margin-bottom: 20px;
  @include media-up(large-tablet) {
    display: none;
  }
  .elastic-search-group {
    flex: 1;
    margin-right: 10px;
    .elastic-search {
      width: 100%;
    }
  }
}

.filters {
  position: sticky;
  top: 0;
  &.open {
    display: block;
  }
}

.filters-body {
  max-height: 60vh;
  overflow-y: scroll;
  border-bottom: 1px solid #ededed;
  &::-webkit-scrollbar, &::-webkit-scrollbar-thumb {
    border-radius: 4px;
  }
  &::-webkit-scrollbar-thumb {
    background: #d1d7d7;
  }
  &::-webkit-scrollbar {
    width: 0.4em;
  }
  @include media-down(large-tablet) {
    max-height: none;
  }
}

.filters-section {
  border-bottom: 1px solid #ededed;
  padding: 12px 0;
}
.order-date-change {
  padding: 8px 0;
  cursor: pointer;
  color: $primary;
  font-weight: bold;
  &.set {
    color: black;
    font-weight: 900;
  }
}
h5.category-navigation {
  text-transform: uppercase;
  margin: 12px 0;
  > a {
    padding-bottom: 2px;
  }
  > a:hover {
    text-decoration: none;
    border-bottom: 2px solid black;
  }
  &::before {
    filter: contrast(0);
  }
  &.selected {
    > a {
      color: $primary;
      border-bottom: 2px solid $primary;
    }
    &::before {
      filter: none;
    }
  }
}

.mobile-filters-container {
  display: none;
  @include media-down(large-tablet) {
    display: block;
  }
}
.filters {
  &-heading {
    position: sticky;
    top: 0;
    background: white;
    display:flex;
    align-items: center;
    padding: 10px 0;
    margin-bottom: 20px;
    border-bottom: 1px solid #e6e6e6;
    z-index: 99999999999;
    @include media-up(large-tablet) {
      display: none;
    }    
    span {
      display: inline-block;
      font-size: 20px;
      margin: auto;
    }
  }
  &.closed {
    @include visibility();
    &:before {
      opacity: 0;
    }
  }
  &-open-btn {
    display: inline-block;
    border-radius: 4px;
    border: none;
    margin-left: 10px;
    padding: 16px;
    @include media-up(large-tablet) {
      display: none;
    }
  }
  &-close-btn {
    position: absolute;
    display: inline-block;
    background: transparent;
    border: none;
    padding: 0;
    &::before {
      width: 30px;
      height: 30px;
    }
    
  }

  &.open {
    @include media-down(large-tablet) {
      @include visibility(true);
      position: fixed;
      top: 0;
      left: 0;
      z-index: 9;
      background: white;
      width: 100%;
      height: 100vh;
      max-height: 100vh;
      overflow-y: scroll;
      -webkit-overflow-scrolling: touch;
      padding: 0px 20px;
      z-index: 99999999999; // intercom is set to a high value
      &:before {
        top: 0;
        opacity: 1;
      }
    }
    @include media-down(large-mobile) {
      padding: 0px 20px;
    }
    .header-navigation-parent-link {
      @include media-down(large-tablet) {
        opacity: 1;
        top: 0;
      }
    }
  }
  &__buttons {
    position: sticky;
    bottom: 0;
    display: flex;
    justify-content: center;
    background: white;
    padding: 20px 0;
    @include media-up(large-tablet) {
      display: none;
    }
    button {
      width: 50%;
      height: 50px;
    }
    button + button {
      margin-left: 10px;
    }
  }
  h5 {
    margin: 14px 0;
  }
  .filter {
    display: block;
    margin-bottom: 12px;
    font-size: 14px;
  }
  input[type="checkbox"] {
    position: absolute;
    margin: 0;
    clip: rect(0, 0, 0, 0);

  }
  label {
    display: inline-block;
    cursor: pointer;
    padding-left: 28px;
    background: url(../../../images/icons/icon-checkbox-empty.svg) 0% 50% no-repeat;
    background-size: 20px
  }
  label.checked {
    background: url(../../../images/icons/icon-checkbox-checked.svg) 0% 50% no-repeat;
  }
}

.dropdown {
  &.extra-padding {
  padding: 15px;
  }
  &-title {
    margin: 8px 0;
    column-span: all;
  }
}

.filter-checkbox {
  display: block;
  margin-bottom: 7px;
  font-size: 14px;
  cursor: pointer;
  padding-left: 28px;
  background: url(../../../images/icons/icon-checkbox-empty.svg) 0% 50% no-repeat;
  background-size: 18px;
  &:hover {
    text-decoration: underline;
  }
  &.checked {
    background: url(../../../images/icons/icon-checkbox-checked.svg) 0% 50% no-repeat; 
    background-size: 18px;
  }
  input[type="checkbox"] {
    position: absolute;
    margin: 0;
    clip: rect(0, 0, 0, 0);
  }
  &:last-of-type {
    margin-bottom: 0;
  }
}