import { useEffect, useState, useRef } from 'react';

import useDocketStore from 'store/useDocketStore';

const Accordion = ({ title, id, active, addLocation, children }) => {
  const [height, setHeight] = useState('0px');

  const { setActiveLocationID } = useDocketStore((state) => ({
    setActiveLocationID: state.setActiveLocationID,
  }));

  const contentRef = useRef(null);

  function toggleAccordion() {
    if (active) {
      setActiveLocationID(null);
      setHeight('0px');
    } else {
      setActiveLocationID(id);
      setHeight(`${contentRef.current.scrollHeight}px`);
    }
  }

  useEffect(() => {
    if (active) {
      setHeight(`${contentRef.current.scrollHeight}px`);
    }
  }, [contentRef.current?.scrollHeight]);

  useEffect(() => {
    if (!active) {
      setHeight('0px');
    }
  }, [active]);

  return (
    <div className="accordion__section">
      <div
        className={`accordion__title${active ? ' active' : ''}${addLocation ? ' add' : ''}`}
        onClick={toggleAccordion}
        role="presentation"
      >
        {title}
      </div>
      <div ref={contentRef} style={{ maxHeight: `${height}` }} className="accordion__content">
        {children}
      </div>
    </div>
  );
};

export default Accordion;
