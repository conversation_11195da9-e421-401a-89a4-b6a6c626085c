import yordar from 'api/yordar';
import { DY<PERSON>MIC_MENU_SECTION_ENDPOINT, DYNAMIC_SUPPLIER_ENDPOINT } from 'api/endpoints';
import getBaseURL from 'utils/getBaseURL';
import {
  FETCH_SUPPLIERS,
  FETCH_SINGLE_SUPPLIER,
  FETCH_SUPPLIER_MENU_SECTION,
  SET_FAVOURITE_ITEM,
  SET_FAVOURITE_SUPPLIER,
  LOADING_SUPPLIER_MENU,
} from 'actions/types';
import { fetchSuppliers } from 'utils/api';

export const fetchInitialSuppliers = ({ query, cookies, host, mealUUID }) => async (dispatch) => {
  const payload = await fetchSuppliers({ query, cookies, host, mealUUID });
  dispatch({
    type: FETCH_SUPPLIERS,
    payload,
  });
};

export const fetchSupplier = ({ slug, cookies, host, query }) => async (dispatch) => {
  const { data: payload } = await yordar.get(DYNAMIC_SUPPLIER_ENDPOINT({ slug, query }), {
    withCredentials: true,
    headers: cookies ? { Cookie: cookies } : {},
    baseURL: getBaseURL({ reqHost: host, type: 'app' }),
  });

  if (query.mealUUID) {
    const targetCategories = ['buffets', 'individually-boxed-meals', 'share-meals'];

    payload.section_grouped_menu_items = payload.section_grouped_menu_items.sort((a, b) => {
      const aHasTarget = a.categories?.some((cat) => targetCategories.includes(cat));
      const bHasTarget = b.categories?.some((cat) => targetCategories.includes(cat));
      return (bHasTarget ? 1 : 0) - (aHasTarget ? 1 : 0);
    });
  }

  dispatch({
    type: FETCH_SINGLE_SUPPLIER,
    key: slug,
    payload,
  });
};

export const fetchTeamSupplier = (payload, uuid) => async (dispatch) => {
  dispatch({
    type: FETCH_SINGLE_SUPPLIER,
    key: uuid,
    payload,
  });
};

export const fetchSupplierMenuSection = ({ id }) => async (dispatch) => {
  dispatch({ type: LOADING_SUPPLIER_MENU, payload: true });
  const { data: payload } = await yordar.get(DYNAMIC_MENU_SECTION_ENDPOINT(id), { withCredentials: true });
  dispatch({
    type: FETCH_SUPPLIER_MENU_SECTION,
    payload,
  });
  dispatch({ type: LOADING_SUPPLIER_MENU, payload: false });
};

export const setFavouriteItem = ({ id, isFavourite }) => async (dispatch) => {
  dispatch({
    type: SET_FAVOURITE_ITEM,
    payload: { id, isFavourite },
  });
};

export const setFavouriteSupplier = ({ id, isFavourite }) => async (dispatch) => {
  dispatch({
    type: SET_FAVOURITE_SUPPLIER,
    payload: { id, isFavourite },
  });
};
