import { useContext } from 'react';
import yordar from 'api/yordar';
import { UserContext } from 'context/user';
import useQuoteStore from 'store/useQuoteStore';
import { HostContext } from 'context/host';
import { DYNAMIC_CUSTOMER_QUOTES_ENDPOINT } from 'api/endpoints';

const QuoteProceedButton = ({ text, validateFields, type, uuid }) => {
  const {
    updateStep,
    formFields,
    setFormField,
    setNoCategoryError,
    setNoEventRequirementsError,
    multiValueFields,
    boolFields,
    captchaResponse,
  } = useQuoteStore((state) => ({
    updateStep: state.updateStep,
    formFields: state.formFields,
    setFormField: state.setFormField,
    setNoCategoryError: state.setNoCategoryError,
    setNoEventRequirementsError: state.setNoEventRequirementsError,
    multiValueFields: state.multiValueFields,
    boolFields: state.boolFields,
    captchaResponse: state.captchaResponse,
  }));

  const { appURL, marketingURL } = useContext(HostContext);

  const { user } = useContext(UserContext);

  const validateCategoryFields = () => {
    const isValid = multiValueFields.categories.length > 0;
    setNoCategoryError(!isValid);
    return isValid;
  };

  const validateEventFields = () => {
    const isValid =
      multiValueFields.eventRequirements.length > 0 &&
      multiValueFields.eventStyles.length > 0 &&
      multiValueFields.serviceStyles.length > 0;
    setNoEventRequirementsError(!isValid);
    return isValid;
  };

  const validateFormInputs = () => {
    let allFieldsValidated = true;
    validateFields.forEach((field) => {
      const { value } = formFields[field];
      const errorPresent = !value;
      if (errorPresent) {
        allFieldsValidated = false;
        setFormField(field, value);
      }
    });
    return allFieldsValidated;
  };

  const handleClick = async () => {
    if (text === 'Home') {
      // eslint-disable-next-line no-return-assign
      if (user) {
        window.location = `${appURL}/c_profile`;
      } else {
        window.location = marketingURL;
      }
      return; // Return here to prevent further execution.
    }

    let isValidationPassed = true;
    if (validateFields) {
      switch (validateFields) {
        case 'categories':
          isValidationPassed = validateCategoryFields();
          break;
        case 'event-requirements':
          isValidationPassed = validateEventFields();
          break;
        default:
          isValidationPassed = validateFormInputs();
          break;
      }
    }

    // If validation failed, stop the submission.
    if (!isValidationPassed) {
      return;
    }

    if (text === 'Submit') {
      if (!captchaResponse && !user) {
        alert('Please verify that you are human');
        return;
      }

      await yordar({
        method: 'POST',
        url: DYNAMIC_CUSTOMER_QUOTES_ENDPOINT({ uuid }),
        data: { form_data: { type, ...sanitizeParams() } },
        withCredentials: true,
      });
    }

    // Call updateStep() only if validation passed and form has been submitted.
    updateStep();
  };

  const sanitizeParams = () => {
    const sanitizedParams = {};

    // Handle formFields
    Object.keys(formFields).forEach((key) => {
      if (formFields[key].value) {
        if (type === 'snacks' && key === 'budgetType') {
          return;
        }
        sanitizedParams[key] = formFields[key].value;
        if (key === 'time') {
          const formattedTime = formFields[key].value.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          });
          sanitizedParams[key] = formattedTime;
        }
      }
    });

    // Handle multiValueFields
    Object.keys(multiValueFields).forEach((key) => {
      if (multiValueFields[key].length > 0) {
        sanitizedParams[key] = multiValueFields[key];
      }
    });

    if (type === 'snacks') {
      sanitizedParams.currentProductList = boolFields.currentProductList ? 'Yes' : 'No';
      sanitizedParams.needsMerchandisingEquipment = boolFields.needsMerchandisingEquipment ? 'Yes' : 'No';
    }
    if (type === 'catering') {
      sanitizedParams.individuallyPacked = boolFields.individuallyPacked ? 'Yes' : 'No';
      sanitizedParams.cutlery = boolFields.cutlery ? 'Yes' : 'No';
    }

    return sanitizedParams;
  };

  return (
    <button type="button" className="button" onClick={handleClick}>
      {text}
    </button>
  );
};

export default QuoteProceedButton;
