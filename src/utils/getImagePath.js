// Menu items in db have images saved as just the id, some 'image/upload/:id', or some the full img url
// Ideally the DB entries are standardised at some point

const getImagePath = ({ id, width, height, transform = 'fill' }) => {
  if (!id) throw new Error('You did not pass an id to getImagePath');

  // Woolworths Image
  if (id.includes('cdn0.woolworths.media')) {
    return id;
  }

  // Cloudinary Image
  let filteredID = id.replace('https://res.cloudinary.com/yordar-p/', '');
  filteredID = filteredID.replace('image/upload', '');
  return `https://res.cloudinary.com/yordar-p/image/upload/w_${width},h_${height},c_${transform},q_auto,fl_lossy,f_auto/${filteredID}`;
};

export default getImagePath;
