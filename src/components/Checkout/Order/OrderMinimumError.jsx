import useCheckoutStore from 'store/useCheckoutStore';
import yordar from 'api/yordar';
import { DYNAMIC_UPDATE_ORDER_ENDPOINT } from 'api/endpoints';
import { Link } from 'components/Common';

const SupplierSpend = ({ supplierSpend }) => (
  <p>
    <strong>{supplierSpend.supplier_name}</strong>: {supplierSpend.minimum_spend} (remaining:{' '}
    {supplierSpend.remaining_spend})
  </p>
);

const OrderSpend = ({ orderSpend, isSingleOrder }) => {
  if (!orderSpend.is_under) return null;

  return (
    <>
      {!isSingleOrder && <p>Order for: {orderSpend.order_name.toUpperCase()}</p>}
      {orderSpend.supplier_spends.map((supplierSpend) =>
        supplierSpend.is_under ? <SupplierSpend supplierSpend={supplierSpend} /> : null
      )}
    </>
  );
};

const OrderMinimumError = ({ order, response, setModalOpen, moveToDetails, submitMealPlanOrder, awaitUserChoice }) => {
  const { suppliers_min_spends: supplierMinimumSpends, is_single_order: isSingleOrder } = response;
  const { updateTotals } = useCheckoutStore((state) => ({
    updateTotals: state.updateTotals,
  }));

  const mealUUID = order?.mealPlan?.uuid;

  const handleContinue = () => {
    setModalOpen(false);
    if (submitMealPlanOrder) {
      submitMealPlanOrder();
    } else if (awaitUserChoice) {
      awaitUserChoice('continue');
    } else {
      moveToDetails();
    }
  };

  const handleTopup = async () => {
    try {
      const {
        data: { totals },
      } = await yordar.put(DYNAMIC_UPDATE_ORDER_ENDPOINT(order.id), {
        charge_to_minimum: true,
      });
      updateTotals({
        orderId: order.id,
        totals,
      });
      alert(`Added a Top up of ${totals.topup}`);
      handleContinue();
    } catch (err) {
      // const { errors } = JSON.parse(err.request.response);
      alert('Something went wrong when adding supplier topup to order');
    }
  };

  const handleAddMoreItems = () => {
    setModalOpen(false);
    if (awaitUserChoice) {
      awaitUserChoice('addMoreItems');
    }
  };

  let addMoreItemsLink = supplierMinimumSpends[0]?.supplier_spends[0]?.menu_link;
  if (mealUUID) {
    addMoreItemsLink += `?mealUUID=${mealUUID}`;
  }
  if (supplierMinimumSpends[0]?.supplier_spends?.length > 1 || awaitUserChoice) {
    addMoreItemsLink = '';
  }

  return (
    <div>
      <h3 className="section-heading">Below Supplier Minimum Spend</h3>

      <p>
        You've ordered <strong>below the minimum order value</strong> for the following supplier(s):
      </p>
      {supplierMinimumSpends.map((orderSpend) => (
        <OrderSpend orderSpend={orderSpend} isSingleOrder={isSingleOrder} />
      ))}
      <p>
        We recommend you add more products to your cart. If you continue without meeting the minimum spend the supplier
        may reject the order.
      </p>

      <div className="button-row-full-width">
        {!!addMoreItemsLink && (
          <Link href={addMoreItemsLink} type="button" className="button primary">
            Add more items
          </Link>
        )}
        {!addMoreItemsLink && (
          <button type="button" className="button primary" onClick={handleAddMoreItems}>
            Add more items
          </button>
        )}
        <button type="button" className="button black" onClick={handleTopup}>
          Charge Me Remaining Necessary Spend
        </button>

        <button type="button" className="button grey" onClick={handleContinue}>
          Continue
        </button>
      </div>
    </div>
  );
};

export default OrderMinimumError;
