const MealPlanExplanationModal = ({ setModalOpen }) => (
  <div>
    <p>
      You can associate this order with a previously crafted Meal Plan. Once a Meal Plan is selected, you also have the
      option to import configuration from the set up Meal Plan.
    </p>
    <button type="button" className="button primary" onClick={() => setModalOpen(false)}>
      Ok
    </button>
  </div>
);

export default MealPlanExplanationModal;
