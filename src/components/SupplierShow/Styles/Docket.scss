.docket-container{
  width: 400px;
  border-radius: 3px;
  @include media-down(small-tablet) {
    width: auto;
  }
}
.docket {
  position: sticky;
  top: 60px;
  @include media-up(small-tablet) {
    margin-top: 3rem;
  }
}

.docket-open-btn {
  @include media-up (small-tablet) {
    display: none;
  }
}

.mobile-filters-heading {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  @include media-up(small-tablet) {
    display: none;
  }
}

.docket-editing-order-heading {
  padding: 6px 10px;
  border-bottom: 1px solid #e3e3e3;
}

.docket-wrapper {
  max-height: calc(100vh - 80px);
  flex-direction: column;
  overflow-y: scroll;
  padding-bottom: 10px;
  flex: 1;
  border-radius: 4px;
  @include media-down(small-tablet) {
    border-top: none;
  }
  &.with-meal-note {
    max-height: calc(100vh - 150px)
  }
}

.docket-internal {
  background-color: #ffffff;
  border: 1px solid #e3e3e3;
}

.docket-empty-cart-heading {
  text-align: center;
  color: #abadad;
  font-size: 18px;
  margin-top: 20px;
}

.location-options {
  span:first-of-type {
    margin-right: 8px;
  }
  .icon::before {
    width: 20px;
    height: 20px;
    margin-left: 4px;
  }
}
.locations-edit {
  &__input {
    flex-basis: 75%;
    height: 20px;
  }
  &__options {
    .icon::before {
      width: 25px;
      height: 25px;
      margin-left: 6px;
    }
  }
}

.docket-panel {
  padding: 0 10px;
  .docket-supplier-header {
    display: flex;
    justify-content: space-between;
    margin: 1rem 0;
    a {
      color: $primary;
      text-decoration: underline;
      font-size: 14px;
      white-space: nowrap;
      &:hover {
        color: darken($primary, 10%);
      }
    }
  }
}

.fetching-location {
  text-align: center;
  padding: 1rem;
  img {
    width: 50px;
  }
}

.add-service-point {
  padding: 12px 17px 12px 16px;
  cursor: pointer;
  &[aria-expanded="true"] {
    background: $black;
    color: white;
    &::before {
      background-image: url('../../../images/icons/minus-hollow-white.svg');
    }
  }
  &::before {
    content: '';
    display: inline-block;
    vertical-align: text-top;
    width: 16px;
    height: 16px;
    background-image: url('../../../images/icons/plus-hollow.svg');
    background-repeat: no-repeat;
    border: none;
    margin-right: 8px;
  }
}
.add-new-location {
  display: flex;
  padding: 16px 20px;
  background: white;
  input {
    height: 30px;
    width: 100%;
  }
}
.docket-pricing {
  padding: 16px;
  color: #5e5e5e;
  p, h4 {
    display: flex;
    justify-content: space-between;
  }
  p {
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 12px;
  }
}
.docket-checkout {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 28px;
  &-button {
    width: 100%;
    padding: 10px 0;
    margin-bottom: 8px;
    line-height: 1.5;
  }
}

.docket-meal-plan-accordion {
  font-family: $body-font;
  margin-bottom: 0.5rem;
}

.docket-meal-plan-toggle {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: transparent;
  border: 1px solid $primary;
  border-radius: 4px 4px 0 0;
  color: $primary;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
  font-family: $body-font;

  &:hover {
    background-color: rgba($primary, 0.05);
  }

  &.collapsed {
    border-radius: 4px;
    border-bottom: 1px solid $primary;
  }

  .accordion-icon {
    font-size: 18px;
    font-weight: bold;
    line-height: 1;
    transition: transform 0.2s ease;
  }
}

.docket-meal-plan-content {
  border: 1px solid $primary;
  border-top: none;
  border-radius: 0 0 4px 4px;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;

  &.collapsed {
    border: none;
  }
}

.docket-meal-plan-note {
  padding: 0.75rem;
  font-size: 14px;
  line-height: 1.5;
  color: $primary;
  margin: 0;
}
.docket-add-supplier {
  text-decoration: underline;
}

.team-order-details {
  border-bottom: 1px solid #d9d9d9;
  padding: 12px;
}

.team-order-name {
  margin: 12px 0;
  margin-bottom: 8px;
  font-size: 18px;
  font-weight: 900;
}

.docket-delivery-at {
  display: flex;
  align-items: center;
  font-weight: bold;
  line-height: 1;
  color: grey;
  margin-bottom: 8px;
  &::before {
    content: '';
    display: inline-block;
    background: url(../../../images/icons/calendar-filled.svg) no-repeat;
    width: 16px;
    height: 16px;
    background-size: contain;
    margin-right: 8px;
    filter: contrast(0.5)
  }
}

.team-budget {
  color: $primary;
}

.back-to-package {
  text-align: center;
  a {
    color: $primary;
    text-decoration: underline;
    font-weight: bold;
  }
}

.order-total {
  border-top: 2px solid #eaeaea;
  padding-top: 18px;
  line-height: 20px;
  font-family: $body-font;
  font-size: 20px;
  font-weight: 900;
  color: black;
  &.total-only {
    padding-top: 0;
    border: none;
  }
}


.docket {
  @include media-down(small-tablet) {
    &.closed {
      @include visibility();
      &:before {
        opacity: 0;
      }
    }
    &-open-btn  {
      position: fixed;
      bottom: 5%;
      left: 50%;
      transform: translate(-50%);
      background: black;
      color: white;
      font-weight: bold;
      padding: 10px 30px;
      border-radius: 20px;
      z-index: 1;
    }
    &-close-btn {
      // position: absolute;
      // right: 2px;
      display: inline-block;
      background: transparent;
      border: none;
      padding: 0;
      &::before {
        width: 30px;
        height: 30px;
      }

    }
    &.open {
      @include visibility(true);
      position: fixed;
      top: 0;
      left: 0;
      z-index: 9;
      background: white;
      width: 100%;
      height: 100vh;
      max-height: 100vh;
      overflow-y: scroll;
      -webkit-overflow-scrolling: touch;
      &:before {
        top: 0;
        opacity: 1;
      }
    }
  }
}

.button.over-budget {
  background: red !important;
}

.remaining-spend {
  font-weight: bold;
  margin-bottom: 0px;
  padding: 10px;
  padding-bottom: 0;
  color: grey;
}

.team-order-levels {
  padding: 0 12px;
  padding-bottom: 16px;
  margin-bottom: 8px;
  border-bottom: 1px solid #d9d9d9;
  > select {
    color: $primary;
    font-weight: bold;
    padding: 12px 16px;
  }
}

.team-level {
  padding: 12px 0 4px;
  margin-bottom: 0;
  font-size: 18px;
  font-weight: 900;
  margin-bottom: 8px;
}

.delivery-override {
  color: $primary;
  font-weight: bold;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}