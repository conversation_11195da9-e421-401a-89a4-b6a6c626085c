import { useState } from 'react';

import { Accordion, AddLocation, EditLocation, LocationHeading, LocationSuppliers } from 'components/SupplierShow';
import useDocketStore from 'store/useDocketStore';

const DocketLocations = ({ isTeamOrder }) => {
  const { activeLocationID, activeOrderID, orders } = useDocketStore((state) => ({
    activeLocationID: state.activeLocationID,
    activeOrderID: state.activeOrderID,
    orders: state.orders,
  }));

  const locations = orders?.[activeOrderID]?.locations;
  const locationDetails = Object.values(locations || {});

  const [changingLocationID, setChangingLocationID] = useState(null);

  const renderLocationHeader = ({ location, isEditingLocation, onlyOneLocation }) => {
    if (isEditingLocation) {
      return <EditLocation name={location.name} id={location.id} setChangingLocationID={setChangingLocationID} />;
    }
    return (
      <LocationHeading
        name={location.name}
        id={location.id}
        setChangingLocationID={setChangingLocationID}
        onlyOneLocation={onlyOneLocation}
      />
    );
  };

  if (isTeamOrder) {
    return locationDetails.map(
      (location) =>
        locations[location.id]?.suppliers && (
          <LocationSuppliers suppliers={Object.values(locations[location.id].suppliers)} isTeamOrder />
        )
    );
  }

  return (
    <>
      {locationDetails.map((location) => (
        <Accordion
          key={location.id}
          id={location.id}
          title={renderLocationHeader({
            location,
            isEditingLocation: changingLocationID === location.id,
            onlyOneLocation: locationDetails.length === 1,
          })}
          active={activeLocationID === location.id}
        >
          {locations[location.id]?.suppliers && (
            <LocationSuppliers suppliers={Object.values(locations[location.id].suppliers)} />
          )}
        </Accordion>
      ))}
      <Accordion title="ADD SERVICE POINT" id="add-location" active={activeLocationID === 'add-location'} addLocation>
        <AddLocation activeOrderID={activeOrderID} activeLocationID={activeLocationID} orderID={activeOrderID} />
      </Accordion>
    </>
  );
};

export default DocketLocations;
