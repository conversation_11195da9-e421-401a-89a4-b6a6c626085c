import { CheckoutField, OrderMealPlan } from 'components/Checkout';
import useCheckoutStore from 'store/useCheckoutStore';
import { shallow } from 'zustand/shallow';

const OrderDetails = ({ isEditPage }) => {
  const { order, checkout } = useCheckoutStore(
    (state) => ({
      order: state.order,
      checkout: state.checkout,
    }),
    shallow
  );

  return (
    <>
      <div className="between-flex heading-block">
        <h3 className="section-heading" style={{ marginBottom: 0 }}>
          Order Details
        </h3>
      </div>

      <div className={`order-details${isEditPage ? ' order-details--edit' : ''}`}>
        {checkout?.meal_plans?.length && order.isCateringOrder && <OrderMealPlan isEditPage={isEditPage} />}
        <CheckoutField title="Name For Order" type="name" placeholder="Name for your order" fullWidth />
        {order.isCateringOrder && (
          <CheckoutField title="Number Of People" type="numberOfPeople" placeholder="People" numberControls />
        )}
        <CheckoutField title="Name" type="contactName" placeholder="Contact name for your order" />
        <CheckoutField title="Contact Number" type="phone" placeholder="Contact Number" />
        <CheckoutField title="Company Name" type="companyName" placeholder="Contact company name" />
        {checkout?.pantry_managers && <CheckoutField title="Pantry Manager" type="pantryManager" />}
        {order.isWoolworthsOrder && order.status !== 'draft' && (
          <CheckoutField
            title="Woolworths Order ID"
            type="woolworthsOrderID"
            hint={`Attached to account: ${order.woolworthsAccount}`}
          />
        )}
      </div>
    </>
  );
};

export default OrderDetails;
