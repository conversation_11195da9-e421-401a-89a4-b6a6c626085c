import { components } from 'react-select';
import { DYNAMIC_CLOUDINARY_PATH } from 'api/endpoints';

const CustomSupplierSelect = (props) => {
  const {
    data: { label, image },
  } = props;

  return (
    <components.Option {...props}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        {image && (
          <img
            src={`${DYNAMIC_CLOUDINARY_PATH({ width: 100, height: 100 })}/${image}`}
            alt={label}
            style={{
              width: '30px',
              height: '30px',
              objectFit: 'cover',
              borderRadius: '4px',
            }}
          />
        )}
        {!image && (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '30px',
              height: '30px',
              color: 'white',
              background: 'black',
            }}
          >
            {label.charAt(0).toUpperCase()}
          </div>
        )}
        <span>{label}</span>
      </div>
    </components.Option>
  );
};

export default CustomSupplierSelect;
