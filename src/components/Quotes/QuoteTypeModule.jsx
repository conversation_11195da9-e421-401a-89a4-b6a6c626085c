import { Link } from 'components/Common';

const QuoteTypeModule = ({ img, type, description, uuid }) => (
  <Link className="quote-module" href={`/quotes/${type}${uuid ? `/${uuid}` : ''}`}>
    <div className="quote-image-container">
      <img src={img} alt={`${type} quote`} />
    </div>
    <div className="quote-module__text">
      <p className="quote-module__title">{type} Quote</p>
      <p className="quote-module__description">{description}</p>
    </div>
  </Link>
);
export default QuoteTypeModule;
