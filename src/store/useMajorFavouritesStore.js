import { create } from 'zustand';

const initialState = [];

const useMajorFavouritesStore = create((set) => ({
  favourites: initialState,
  setMajorFavourites: (favourites) => set({ favourites }),
  updateMajorFavourites: ({ type, favourite }) =>
    set((state) => ({
      favourites: type === 'add' ? [...state.favourites, favourite] : state.favourites.filter((id) => id !== favourite),
    })),
}));

export default useMajorFavouritesStore;
