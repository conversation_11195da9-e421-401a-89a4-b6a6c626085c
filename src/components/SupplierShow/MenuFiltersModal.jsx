import useFilterStore from 'store/useFilterStore';
import { dietaryCategories } from 'static/categories';

const MenuFiltersModal = () => {
  const { filters, clearFilters } = useFilterStore((store) => ({
    filters: store.filters,
    clearFilters: store.clearFilters,
  }));

  return (
    <div className="filters">
      <div>
        <div className="filters-section" style={{ padding: 0 }}>
          <h5 className="icon icon-large icon-right-spacer icon-dietary">Dietary Filters</h5>
          {dietaryCategories.sort().map((dietary) => (
            <Filter key={dietary} category={dietary} checked={filters.includes(dietary)} />
          ))}
        </div>
      </div>
      <div className="filters__buttons">
        <button type="button" onClick={() => clearFilters()} className="button black outline">
          Clear All
        </button>
      </div>
    </div>
  );
};

const Filter = ({ category, checked }) => {
  const toggleFilter = useFilterStore((state) => state.toggleFilter);

  return (
    <>
      <label htmlFor={`filter-${category}`} className={`filter ${checked ? 'checked' : ''}`}>
        <input
          id={`filter-${category}`}
          type="checkbox"
          onChange={(e) => handleFilter({ e, toggleFilter })}
          data-name={category}
          checked={checked}
        />
        {category}
      </label>
    </>
  );
};

const handleFilter = ({ e, toggleFilter }) => {
  const { name } = e.target.dataset;
  toggleFilter(name);
  window.scrollTo(0, 0);
};

export default MenuFiltersModal;
