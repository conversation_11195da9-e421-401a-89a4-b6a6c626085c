.number-input-with-controls {
  display: grid;
  grid-template-columns: 1fr 3fr 1fr;
  grid-column-gap: 6px;
  place-items: center;
  input[type=number] {
    padding: 0 12px;
    flex: 4;
    text-align: center;
    font-weight: 900;
    border: 1px solid rgb(170, 170, 170);
    font-size: 14px;
    align-self: stretch;
  }
  .number-control {
    background: white;
    box-shadow: none;
    outline: none;
    border: 1px solid rgb(170, 170, 170);
    aspect-ratio: 1 / 1;
    border-radius: 50%;
    width: 24px;
  }
}