import { useState } from 'react';
import slugify from 'slugify';

const ToggleQuestion = ({ question, setAnswer }) => {
  const [selectedOption, setSelectedOption] = useState();

  const handleClick = (event) => {
    setSelectedOption(event.target.value);
    setAnswer((state) => ({ ...state, value: event.target.value }));
  };

  return (
    <div className="single-select-question">
      {[...Array(2).keys()].map((num) => {
        const option = question.options[num];
        return (
          <button
            key={slugify(option)}
            onClick={handleClick}
            className={`button ${selectedOption === option ? 'selected' : ''} single-select-answer`}
            value={option}
          >
            {option}
          </button>
        );
      })}
    </div>
  );
};

export default ToggleQuestion;
