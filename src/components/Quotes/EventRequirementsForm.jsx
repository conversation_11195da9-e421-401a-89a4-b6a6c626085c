import useQuoteStore from 'store/useQuoteStore';
import QuoteCheckbox from './QuoteCheckbox';

const EventRequirementsForm = ({ eventRequirementsList, eventStylesList, serviceStylesList }) => {
  const { eventRequirements, eventStyles, serviceStyles, setMultiValueField, noEventRequirementsError } = useQuoteStore(
    (state) => ({
      eventRequirements: state.multiValueFields.eventRequirements,
      eventStyles: state.multiValueFields.eventStyles,
      serviceStyles: state.multiValueFields.serviceStyles,
      noEventRequirementsError: state.noEventRequirementsError,
      setMultiValueField: state.setMultiValueField,
    })
  );

  return (
    <>
      <p className="quote-label">Requirements</p>
      <div className="checkbox-grid">
        {eventRequirementsList.map((requirement) => (
          <QuoteCheckbox
            value={requirement}
            label={requirement}
            key={requirement}
            onClick={() => setMultiValueField('eventRequirements', requirement)}
            checked={eventRequirements.includes(requirement)}
          />
        ))}
      </div>
      <p className="quote-label">Event Style</p>
      <div className="checkbox-grid">
        {eventStylesList.map((eventStyle) => (
          <QuoteCheckbox
            value={eventStyle}
            label={eventStyle}
            key={eventStyle}
            onClick={() => setMultiValueField('eventStyles', eventStyle)}
            checked={eventStyles.includes(eventStyle)}
          />
        ))}
      </div>
      <p className="quote-label">Service Style</p>
      <div className="checkbox-grid">
        {serviceStylesList.map((service) => (
          <QuoteCheckbox
            value={service}
            label={service}
            key={service}
            onClick={() => setMultiValueField('serviceStyles', service)}
            checked={serviceStyles.includes(service)}
          />
        ))}
      </div>
      {noEventRequirementsError && <p style={{ color: 'red' }}>Please select at least one requirement for each</p>}
    </>
  );
};

export default EventRequirementsForm;
