/*=============================================>>>>>
= App Styles =
===============================================>>>>>*/
body {
  background: $white;
  color: $textcolor;
}
.wrapper {
  // max-width: 1400px;
  margin: 0 auto;
  padding: 0 60px;
  @include media-down(small-mobile) {
    padding: 0 10px;
  }
  &.dash {
    padding-left: 0;
  }
}

.main-content {
  min-height: 600px;
}

.hidden {
  display: none;
}


$break-points: (
  'giant',
  'large-desktop',
  'desktop',
  'small-desktop',
  'large-tablet',
  'tablet',
  'small-tablet',
  'large-mobile',
  'mobile',
  'small-mobile'
);

@each $breakpoint in $break-points {
  .#{$breakpoint}-down {
    @include media-up($breakpoint) {
      display: none;
    }
  }
  .#{$breakpoint}-up {
    @include media-down($breakpoint) {
      display: none;
    }
  }
}
