import { useContext } from 'react';

import { HostContext } from 'context/host';

const CancelChangesModal = ({ setModalOpen }) => {
  const { appURL } = useContext(HostContext);
  return (
    <>
      <h3>Cancelling Changes</h3>
      <p>
        Are you sure you want to cancel the changes? Please note that any changes to order lines will not be cancelled
      </p>
      <div className="cancel-buttons">
        <a className="button checkout-button confirm primary" href={`${appURL}/c_profile`}>
          Yes, remove changes to order information
        </a>
        <a className="button checkout-button confirm cancel" onClick={() => setModalOpen(false)}>
          No, I want to continue editing
        </a>
      </div>
    </>
  );
};

export default CancelChangesModal;
