import { useEffect } from 'react';
import { useRouter } from 'next/router';

function useSearchedAddress({ setValue, setAddress }) {
  const router = useRouter();

  useEffect(() => {
    const {
      query: { suburb, state, street_address: streetAddress },
    } = router;

    const localStorageAddress = localStorage.getItem('address');
    const suburbAndState = `${suburb?.humanize()} ${state}, Australia`;
    const newStreetAddress = streetAddress && `${streetAddress}, ${suburbAndState}`;
    const newSuburbAddress = suburb && !localStorageAddress?.includes(suburbAndState) && suburbAndState;
    const addressValue = newStreetAddress || newSuburbAddress || localStorageAddress;

    if (addressValue) {
      setValue(addressValue);
      setAddress(addressValue);
    }

    if (addressValue && addressValue !== localStorageAddress) {
      localStorage.setItem('address', addressValue);
    }

    if (suburb) {
      localStorage.setItem(
        'geo-location',
        JSON.stringify({
          suburb: suburb?.humanize(),
          state,
        })
      );
    }

    // Get values from local storage on mount and set to state
  }, [router.query]);
}

export default useSearchedAddress;
