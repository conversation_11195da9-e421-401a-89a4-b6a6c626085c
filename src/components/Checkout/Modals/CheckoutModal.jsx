import Modal from 'react-responsive-modal';

const CheckoutModal = ({ children, open, setModalOpen, dimensionOptions, noClose, showCloseIcon = false }) => (
  <Modal
    open={open}
    center
    styles={{
      modal: { padding: 20, borderRadius: '10px', ...dimensionOptions },
      closeButton: { cursor: 'pointer', marginLeft: '6px' },
    }}
    showCloseIcon={showCloseIcon}
    onClose={noClose || (() => setModalOpen(false))}
  >
    {children}
  </Modal>
);

export default CheckoutModal;
