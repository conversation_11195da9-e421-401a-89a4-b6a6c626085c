import moment from 'moment';
import { useState, useEffect } from 'react';
import Image from 'next/image';

import useDocketStore from 'store/useDocketStore';
import LoadingDots from 'images/icons/three-dots.svg';
import { DAYS } from 'utils/constants';
import { useRouter } from 'next/router';
import useDeliveryDateStore from 'store/useDeliveryDateStore';

const RecurringOrderForm = () => {
  const [recurring, setRecurring] = useState(false);
  const [loading, setLoading] = useState(false);
  const { deliveryDate } = useDeliveryDateStore((state) => ({
    deliveryDate: state.delivery_date,
  }));
  const { setRecurringOrder } = useDocketStore((state) => ({
    setRecurringOrder: state.setRecurringOrder,
  }));

  const [formFields, setFormFields] = useState({
    frequency: 'weekly',
    recurringDays: [],
    copyAll: true,
    skipPublic: true,
  });
  const { frequency, recurringDays, copyAll, skipPublic } = formFields;

  const handleRecurringCheckbox = () => setRecurring((state) => !state);

  const router = useRouter();
  const { mealUUID } = router.query;

  const postRecurringOrder = async (e) => {
    e.preventDefault();
    setLoading(true);
    setRecurringOrder({
      formFields,
      mealUUID,
    });
    setLoading(false);
  };

  useEffect(() => {
    if (deliveryDate) {
      const recurringDay = moment(deliveryDate, 'DD/MM/YYYY').format('ddd').toLowerCase();
      setFormFields((state) => ({
        ...state,
        recurringDays: [...state.recurringDays, recurringDay],
      }));
    }
  }, [deliveryDate]);

  return (
    <div className="docket-recurring-order">
      <label htmlFor="recurring-switch" className={`set-recurring ${recurring ? 'checked' : ''}`}>
        <input id="recurring-switch" checked={recurring} type="checkbox" onChange={handleRecurringCheckbox} />
        Set as recurring order
      </label>
      {recurring && (
        <form onSubmit={postRecurringOrder}>
          <h3 className="recurring-item-description">Recurring Order</h3>
          <span>Repeat this order weekly, fortnightly or monthly</span>
          <select
            value={frequency}
            onChange={(e) => setFormFields((state) => ({ ...state, frequency: e.target.value }))}
            name="recurring-frequency"
            id="recurring-frequency"
            className="recurring-frequency"
          >
            <option value="weekly">Weekly</option>
            <option value="fortnightly">Fortnightly</option>
            <option value="monthly">Same Day Every Month</option>
            <option value="bi_fortnightly">Every 4 Weeks</option>
          </select>
          <p className="recurring-item-description">Select Days:</p>
          <div className="recurring-days">
            {DAYS.map((day) => (
              <RecurringDay
                key={day}
                day={day}
                isSelected={formFields.recurringDays.includes(day)}
                setFormFields={setFormFields}
              />
            ))}
          </div>
          <label htmlFor="copy-all" className={`copy-all ${copyAll ? 'checked' : ''}`}>
            Copy orders to all selected days?
            <input
              id="copy-all"
              checked={copyAll}
              type="checkbox"
              onChange={() => setFormFields((state) => ({ ...state, copyAll: !state.copyAll }))}
            />
          </label>
          <p className="recurring-item-description">On Public Holidays:</p>
          <div>
            <input
              id="skip-true"
              type="radio"
              value="skip"
              className="recurring-public-holidays"
              checked={skipPublic}
              onChange={() => setFormFields((state) => ({ ...state, skipPublic: true }))}
            />
            <label htmlFor="skip-true">Skip the delivery</label>
          </div>
          <div>
            <input
              id="skip-next"
              type="radio"
              value="next"
              className="recurring-public-holidays"
              checked={!skipPublic}
              onChange={() => setFormFields((state) => ({ ...state, skipPublic: false }))}
            />
            <label htmlFor="skip-next">Delivery on next business day</label>
          </div>
          {!loading && (
            <input
              className={`submit-recurring ${recurringDays.length ? '' : 'disabled'}`}
              type="submit"
              value="Save"
            />
          )}
          {loading && (
            <div className="submit-recurring__loading">
              <Image src={LoadingDots} width={40} height={40} alt="Loading Indicator" />
            </div>
          )}
        </form>
      )}
    </div>
  );
};

const RecurringDay = ({ day, isSelected, setFormFields }) => {
  const handleChange = (e) => {
    const isChecked = e.target.checked;
    setFormFields((state) => ({
      ...state,
      recurringDays: isChecked
        ? [...state.recurringDays, day]
        : state.recurringDays.filter((recurringDay) => recurringDay !== day),
    }));
  };
  return (
    <label htmlFor={`recurring-day-${day}`} className={`recurring-day${isSelected ? ' checked' : ''}`}>
      {day.titleize()}
      <input id={`recurring-day-${day}`} type="checkbox" checked={isSelected} value={day} onClick={handleChange} />
    </label>
  );
};

export default RecurringOrderForm;
