import { QuoteImageSection, QuoteDetails } from 'components/Quotes';
import { Layout } from 'components/Common';
import { quotesSEO } from 'utils/seo';

const Quotes = ({ user }) => {
  const forDash = user?.type === 'customer';
  return (
    <Layout seo={quotesSEO} forDash={forDash} customClass={forDash ? 'quotes-dash' : ''} type="quotes-dash">
      <div className="quote-container">
        <div className="auth-container" style={{ padding: '' }}>
          <div className="auth-card">
            <QuoteImageSection type="start" />
            <QuoteDetails type="start" />
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Quotes;
