const QuoteCheckbox = ({ value, label, onClick, checked }) => {
  const handleClick = (e) => {
    e.preventDefault();
    onClick();
  };

  return (
    <div className="quote-checkbox-container" onClick={handleClick}>
      <input type="checkbox" id={value} value={value} checked={checked} onChange={(e) => e.stopPropagation()} />
      <span className="custom-checkbox" />
      <label htmlFor={value} className="quote-checkbox-label">
        {label || value}
      </label>
    </div>
  );
};

export default QuoteCheckbox;
