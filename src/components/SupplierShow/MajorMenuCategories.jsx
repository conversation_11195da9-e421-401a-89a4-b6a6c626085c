import { useState, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';

import { DropDown } from 'components/Common';
import useMenuNavDimensions from 'hooks/SupplierShow/useMenuNavDimensions';
import { fetchSupplierMenuSection } from 'actions';
import useSearchResultsStore from 'store/useSearchResultsStore';

const MajorMenuCategories = ({ menuSections, activeGroup, setAisleAndSection, isAisle = false }) => {
  const dispatch = useDispatch();
  const container = useRef(null);
  const categoryRefs = useRef(new Array(menuSections.length).fill(null));
  const [overflownCategories, setOverflownCategories] = useState([]);
  const [isResetting, setIsResetting] = useState(false); // New state
  const visibleMenuSection = menuSections[activeGroup];
  let { width } = useMenuNavDimensions(container, visibleMenuSection);
  const setShowInMenu = useSearchResultsStore((state) => state.setShowInMenu);

  const currentOverflownCategory = overflownCategories.find((cat) => cat.name === visibleMenuSection?.name);

  useEffect(() => handleOverflowingCategories(), [menuSections, visibleMenuSection, width]);

  useEffect(() => {
    fetchFirstSectionOnLoad();
  }, []);

  async function fetchFirstSectionOnLoad() {
    if (isAisle) {
      await dispatch(fetchSupplierMenuSection({ id: menuSections[0].menu_sections[0].id }));
    }
  }

  const dropdownOptions = overflownCategories.map((category) => ({
    href: `#${category.href}`,
    label: category.name,
    ...(category.name === visibleMenuSection?.name && { className: 'active' }),
    id: category.id,
    index: category.index,
  }));

  const handleOverflowingCategories = () => {
    if (!isAisle) {
      setIsResetting(true);
      setOverflownCategories([]);
    } else {
      updateOverflownCategories();
    }
  };

  useEffect(() => {
    if (isResetting) {
      updateOverflownCategories();
      setIsResetting(false);
    }
  }, [isResetting]);

  const updateOverflownCategories = () => {
    categoryRefs.current.forEach((item, index) => {
      if (!item?.clientWidth || width === null) return;

      width -= item.clientWidth;
      const currentCategory = {
        name: item.textContent,
        position: +item.dataset.position,
        href: `menu-section-${item.dataset.id}`,
        index,
        id: item.dataset.id,
      };
      const inOverflownArray = overflownCategories.find((cat) => cat.name === currentCategory.name);

      if (width < 0) {
        item.style.visibility = 'hidden';
        if (!inOverflownArray) {
          setOverflownCategories((arr) => [...arr, currentCategory]);
        }
      } else {
        item.style.visibility = 'visible';
        if (inOverflownArray) {
          setOverflownCategories(overflownCategories.filter((category) => category.name !== currentCategory.name));
        }
      }
    });
  };

  async function handleAisleAndSectionSelection({ e, sectionID, groupNum }) {
    e.preventDefault();
    setShowInMenu(false);
    if (isAisle) {
      setAisleAndSection({ activeAisle: groupNum, activeSection: 0 });
    } else {
      setAisleAndSection((state) => ({ ...state, activeSection: groupNum }));
    }
    await dispatch(fetchSupplierMenuSection({ id: sectionID }));
  }

  return (
    <>
      <div className="supplier-categories" ref={container}>
        {menuSections.map((group, i) => (
          <li
            key={group.name}
            ref={(el) => {
              categoryRefs.current[i] = el;
            }}
            className={group.name === visibleMenuSection?.name ? 'active-menu-section' : ''}
            data-position={group.position || i}
            data-id={isAisle ? group.menu_sections[0].id : group.id}
          >
            <a
              href={`#menu-section-${group.id}`}
              onClick={(e) =>
                handleAisleAndSectionSelection({
                  e,
                  groupNum: i,
                  sectionID: isAisle ? group.menu_sections[0].id : group.id,
                  setShowInMenu,
                })
              }
            >
              {group.name}
            </a>
          </li>
        ))}
      </div>
      {overflownCategories.length > 0 && (
        <DropDown
          label={currentOverflownCategory ? visibleMenuSection?.name : 'More'}
          options={dropdownOptions}
          dropdownClass={currentOverflownCategory ? 'active-menu-section' : ''}
          chevronColor={currentOverflownCategory && 'white'}
          scrollSection
          setAisleAndSection={setAisleAndSection}
          isAisle={isAisle}
        />
      )}
    </>
  );
};

export default MajorMenuCategories;
