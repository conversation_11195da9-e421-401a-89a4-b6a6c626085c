import { OrderLine } from 'components/Checkout';

import useCheckoutStore from 'store/useCheckoutStore';

const Supplier = ({ supplier }) => {
  const { orderSuppliers } = useCheckoutStore();
  const orderSupplier = orderSuppliers[supplier.id];

  return (
    <div>
      <p className="supplier-name">{orderSupplier.name}</p>
      {Object.values(supplier.order_lines).map((orderLine) => (
        <OrderLine key={orderLine.id} orderLine={orderLine} />
      ))}
    </div>
  );
};

const Location = ({ location }) => (
  <div className="customer-order__orders">
    <h6 className="customer-order__heading">{location.name}</h6>
    {Object.values(location.suppliers).map((supplier) => (
      <Supplier key={supplier.name} supplier={supplier} />
    ))}
  </div>
);

const CheckoutOrder = () => {
  const { orders, activeOrderID, setActiveOrderID } = useCheckoutStore();

  return (
    <div className="checkout-order">
      {Object.keys(orders)?.length > 1 && (
        <div className="recurring-day">
          {Object.values(orders).map((recurringOrder) => (
            <p
              key={recurringOrder.id}
              style={{ textTransform: 'uppercase', flex: 1 }}
              className={Number(activeOrderID) === recurringOrder.id ? 'active' : ''}
              onClick={() => setActiveOrderID(recurringOrder.id)}
            >
              {recurringOrder.name}
            </p>
          ))}
        </div>
      )}
      {Object.values(orders[activeOrderID]?.locations || {})?.map((locationData) => (
        <Location key={locationData.name} location={locationData} />
      ))}
    </div>
  );
};

export default CheckoutOrder;
