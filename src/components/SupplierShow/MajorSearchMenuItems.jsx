import { useEffect, useState } from 'react';
import DebounceInput from 'react-debounce-input';

import { OutsideAlerter, Spinner } from 'components/Common';
import { SearchResults } from 'components/SupplierIndex';
import useSearchResultsStore from 'store/useSearchResultsStore';

const MajorSearchMenuItems = ({ mobile, forDash }) => {
  const [searchValue, setSearchValue] = useState('');
  const [loading, setLoading] = useState(true);
  const { results, setShowInMenu, setSearchResults, resetSearchResults } = useSearchResultsStore((state) => ({
    results: state.results,
    setShowInMenu: state.setShowInMenu,
    setSearchResults: state.setSearchResults,
    resetSearchResults: state.resetSearchResults,
  }));

  function handleSearch(e) {
    setSearchValue(e.target.value);
  }

  useEffect(async () => {
    setShowInMenu(false);
    if (searchValue.length > 2) {
      setLoading(true);
      await setSearchResults(searchValue);
      setLoading(false);
    } else {
      resetSearchResults();
    }
  }, [searchValue]);

  return (
    <OutsideAlerter className={`elastic-search-group active ${forDash ? 'dash' : ''}${mobile ? 'mobile' : ''}`}>
      <DebounceInput
        type="text"
        placeholder="Item name, cuisines, snacks"
        value={searchValue}
        className="elastic-search"
        onChange={(e) => handleSearch(e)}
        debounceTimeout={600}
      />
      {searchValue?.length > 2 && (
        <div className={`elastic-search-results ${loading ? 'loading' : ''}`}>
          {!loading && (
            <SearchResults
              searchResults={{ menuItems: results, categories: [], suppliers: [] }}
              searchValue={searchValue}
              majorSupplier
            />
          )}
          {loading && <Spinner />}
        </div>
      )}
    </OutsideAlerter>
  );
};

export default MajorSearchMenuItems;
