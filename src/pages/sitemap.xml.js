import yordar from 'api/yordar';
import suburbList from 'static/suburbList';

import { mapSuppliers, mapSuburbs } from 'utils/sitemap';

const Sitemap = () => {};

export const getServerSideProps = async ({ res }) => {
  const { data: suppliers } = await yordar('/api/suppliers/cache-list');

  // eslint-disable-next-line
  const sitemap = 
  `<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      ${suppliers.map(mapSuppliers).join('')}
      ${suburbList.map(mapSuburbs).join('')}
    </urlset>
  `;

  res.setHeader('Content-Type', 'text/xml');
  res.write(sitemap);
  res.end();

  return {
    props: {},
  };
};

export default Sitemap;
