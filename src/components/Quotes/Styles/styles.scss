.quote-container {
  background: '#f9f9f9';
  padding: 6.5rem 0;
}
.auth-container {
	max-width: 1300px;
	padding: 0 50px;
	margin: auto;
  .button {
    width: 160px;
  }
  
}
.auth-card {
	display: flex;
	background: #FFFFFF;
	box-shadow: 0 0 10px 0 rgba(0,0,0,0.10);
	border-radius: 4px;
	@include media-down(hamburger) {
		flex-direction: column;
	}
	&__illustration {
		margin-right: 20px;
		padding: 35px;
		border-right: 1px solid #e0e0e0;
		width: 60%;
		&.quote {
			padding: 0;
			margin: 0;
			.quote-image {
				width: 450px;
				height: 300px;
				img {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}
			}
		}
		@include media-down(hamburger) {
			width: 100%;
			border: none;
			padding-bottom: 0;
		}
	}
	&__title {
		font-weight: bold;
		margin-top: 16px;
		font-size: 22px;
	}
	&__step {
		margin-top: 8px;
		color: #b3b3b3;
	}
  .button {
    font-family: $body-font;
    text-transform: none;
    font-size: 16px;
    font-weight: normal;
  }
}

.button-type-radios {
  display: flex;
  margin-bottom: 12px;
}

.authorization-module {
	background: white;
	border-radius: 4px;
	overflow: hidden;
	padding: 1.2rem 2.4rem;
	position: relative;	
	width: 100%;
}

.quote-panel-info {
  padding: 20px;
}

.quote-title {
  font-size: 24px;
  margin-bottom: 1.2rem;
  font-weight: 500;
}
.quote-module {
  display: flex;
  align-items: center;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  margin-bottom: 16px;
  cursor: pointer;
  &:hover {
    border-color: $primary;
    background: #f9f9f9;
    text-decoration: none;
  }
  &__text {
    padding: 0 10px;
  }
  &__title {
    font-weight: bold;
    margin-bottom: 8px;
    font-family: $heading-font;
    text-transform: capitalize;
  }
  &__description {
    margin-bottom: 0;
    color: #7c7c7c;
  }
  p {
    line-height: 22px;
    font-size: 14px;
  }
}

.quote-back-link {
  display: flex;
  align-items: center;
  color: $primary;
  font-weight: 900;
  margin-bottom: 16px;
  cursor: pointer;
  &:hover {
    color: #9f9f9f;
  }
  &::before {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    background-repeat: no-repeat;
    background-image: url('../../../images/icons/chevron-down-primary.svg');
    transform: rotate(90deg);
    background-size: contain;
    margin-right: 12px;
  }
}

.quote-image-container {
  min-width: 120px;
  height: 120px;
  img {
    object-fit: cover;
    width: 100%;
    height: 100%;
  }
}

.quote-label {
  color: #979797;
  text-transform: uppercase;
  font-size: 14px;
  margin-bottom: 0;
  font-weight: bold;
}

.quote-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.quote-checkbox-container {
  display: flex;
  align-items: center;
  input[type='checkbox'] {
    @include checkbox-style('margin-right', 12px);
  }
}

.category-select {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: 16px;
  margin-bottom: 20px;
  .quote-category {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #dcdcdc;
    padding: 4px 8px;
    border-radius: 4px;
    margin-bottom: 8px;
    cursor: pointer;
    &:hover {
      background: #fbfbfb;
      border-color: $primary;
    }
    p {
      display: flex;
      align-items: center;
      margin-bottom: 0;
    }
    p::before {
      content: '';
      display: inline-block;
      width: 22px;
      height: 22px;
      background-repeat: no-repeat;
      background-size: contain;
      margin-right: 16px;
    }
    input {
      margin-bottom: 0;
    }
    input[type='checkbox'] {
      @include checkbox-style;
    }
  }
}


.budget-radios {
  display: flex;
  margin: 4px 0 16px;
  label {
    margin: 0;
    &:first-of-type {
      margin-right: 10px;
    }
  }
}

.quote-sub-heading {
  font-size: 18px;
  font-weight: 500;
  margin: 16px 0;
}

.quote-textarea, .quote-textarea:focus {
  background: white;
  border: 1px solid #dcdcdc;
  height: 100px;
  font-family: $body-font;
  width: 100%;
  box-sizing: border-box;
  padding: 8px;
  font-size: 14px;
  border-radius: 4px;
  box-shadow: none;
}

input[type="radio"] {
  opacity: 0;
  position: absolute;
}

input[type="radio"] + label:before {
  position: absolute;
  left: 0px;
  top: 4px;
  text-align: center;
  line-height: 1;
  border: 1px solid #bababa;
  width: 14px;
  height: 14px;
  margin-right: 0.8rem;
  color: white;
  background: white;
}

input[type="radio"] + label:before {
  content: "";
  border-radius: 50%;
}

input[type="radio"]:checked + label:after {
  content: "";
  width: 8px;
  height: 8px;
  background: #1f9e86;
  border-radius: 50%;
  position: absolute;
  left: 4px;
  top: 8px;
}

input[type="radio"] + label {
  position: relative;
  padding-left: 1.8rem;
}

.budget-radios label {
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.8;
}

[type='checkbox'] + label, [type='radio'] + label {
  display: inline-block;
  vertical-align: baseline;
}

.quote-details-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 4px 16px;
  label {
    text-transform: uppercase;
    color: #838383;
    font-size: 14px;
  }
  .form-input {
    width: 100%;
    background-color: white;
    border: 1px solid #dde3e8;
    border-radius: 4px;
    box-shadow: none;
    padding: 10px 6px;
    margin: 0;
    margin-bottom: 0.5rem;
    box-sizing: border-box;
    font-size: 16px;
    &.error {
      border-color: red;
    }
  }
}

.checkbox-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 8px;
  margin: 12px 0;
}

.quote-option-text {
  margin-bottom: 12px;
}

.checkbox-flex {
  display: flex;
  margin: 8px 0;
  label + label {
    margin-left: 20px;
  }
}

.quote-checkbox-label {
  text-transform: capitalize;
  font-size: 14px;
}

.progress-bar-container {
  padding: 10px 20px 0;
  .progress-tag {
    margin-bottom: 0;
    color:rgb(124, 124, 124);
    font-size: 14px;
  }
  progress {
    appearance: none;
    -webkit-appearance: none;
    width: 100%;
    height: 12px;
    border: 1px solid rgb(124, 124, 124);
    border-radius: 10px;
    background-color: white;
    color: #1f9e86;
    vertical-align: super;

    &::-webkit-progress-bar {
        background-color: white;
        border-radius: 10px;
    }
  
    &::-webkit-progress-value {
        background-color: #1f9e86;
        border-radius: 8px;
    }
  
    &::-moz-progress-bar {
        background-color: #1f9e86;
        border-radius: 8px;
    }
  
    &::-ms-fill {
        border: 1px solid black;
        border-radius: 8px;
        background: #1f9e86;
    }
  }
}

.quote-recaptcha {
  margin: 12px 0;
}



@include media-down(small-tablet) {
  .quote-container {
    padding: 0;
  }
  .auth-container {
    padding: 0;
  }
  .auth-card {
    flex-direction: column;
  }
  .auth-card__illustration {
    width: 100%;
  }
  .auth-card__illustration.quote .quote-image {
    width: 100%;
  }
  .authorization-module {
    padding: 10px 20px;
  }
  .quote-module {
    flex-direction: column;
  }
  .quote-module__text {
    padding: 10px;
  }
  .quote-module__description {
    font-size: 14px;
  }
  .quote-image-container {
    min-width: 100%;
    height: 150px;
  }
  .category-select {
    grid-template-columns: 1fr;
  }
  .quote-input {
    grid-column: 1/3;
  }
  .button {
    margin-top: 20px;
    width: 100%;
  }
  .quote-back-link {
    font-size: 20px;
  }
  .quote-panel-info {
    display: none;
  }
}

