import useQuoteStore from 'store/useQuoteStore';
import RadioButtonGroup from './RadioButtonGroup';

const SnacksRequirementsForm = () => {
  const { setBoolField, boolFields } = useQuoteStore((state) => ({
    setBoolField: state.setBoolField,
    boolFields: state.boolFields,
  }));

  return (
    <>
      <p>Do you have a current product list of items you require?</p>
      <div className="checkbox-flex">
        <RadioButtonGroup
          name="currentProductList"
          options={[
            { id: 'true', label: 'Yes' },
            { id: 'false', label: 'No' },
          ]}
          value={boolFields.currentProductList.toString()}
          onChange={(selectedOption) => setBoolField('currentProductList', selectedOption === 'true')}
        />
      </div>

      <p>Do you require merchandising equipment such as snack racks, baskets etc.</p>
      <div className="checkbox-flex">
        <RadioButtonGroup
          name="needsMerchandisingEquipment"
          options={[
            { id: 'true', label: 'Yes' },
            { id: 'false', label: 'No' },
          ]}
          value={boolFields.needsMerchandisingEquipment.toString()}
          onChange={(selectedOption) => setBoolField('needsMerchandisingEquipment', selectedOption === 'true')}
        />
      </div>
    </>
  );
};

export default SnacksRequirementsForm;
