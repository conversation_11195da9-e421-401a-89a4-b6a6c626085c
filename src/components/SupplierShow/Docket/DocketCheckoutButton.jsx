import { Link } from 'components/Common';
import { DYNAMIC_ORDER_EDIT_PAGE, CHECKOUT_PAGE } from 'api/endpoints';
import { HostContext } from 'context/host';

const DocketCheckoutButton = ({ isActive, editingOrder, orderID }) => {
  const href = (editingOrder && DYNAMIC_ORDER_EDIT_PAGE(orderID)) || (isActive && CHECKOUT_PAGE) || '#';

  return (
    <Link className={`docket-checkout-button button ${isActive ? '' : 'disabled'}`} href={href} isExternal>
      {editingOrder ? 'Save' : 'Go to Checkout'}
    </Link>
  );
};

export default DocketCheckoutButton;
