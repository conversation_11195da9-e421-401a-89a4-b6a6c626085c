import { useState } from 'react';
import Select from 'react-select';
import axios from 'axios';

import { FilePreview } from 'components/LoadingDock';

const codeLimit = 50;
const initialLoadingDock = (customer) => ({
  id: null,
  code: null,
  customer_profile_id: customer.id,
  saved_for_future: false,
  file_url: '',
});

const Form = ({ order, customer, loadingDock, handleSubmit }) => {
  const loadingDockOptions = customer.loading_docks.map(({ id: value, code, file_url, created_at: createdAt }) => {
    let label = code ? `${code.substring(0, codeLimit)}${code.length > codeLimit ? '...' : ''}` : '';
    if (file_url) {
      if (code) {
        label += ' (with attached-file)';
      } else {
        label += 'attached-file';
      }
    }

    return {
      value,
      label,
      createdAt,
    };
  });

  const [uploadedFile, setUploadedFile] = useState(null);
  const [fileIsUploading, setFileIsUploading] = useState(false);
  const [localLoadingDock, setLocalLoadingDock] = useState(loadingDock || initialLoadingDock(customer));
  const [selectedLoadingDock, setSelectedLoadingDock] = useState(
    loadingDockOptions.find((option) => option.value === localLoadingDock.id)
  );

  const handleFileChange = async (e) => {
    setFileIsUploading(true);
    const file = e.target.files[0];
    const formData = new FormData();

    formData.append('file', file);
    formData.append('upload_preset', process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET);

    try {
      const { data } = await axios.post(process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PATH, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      setLocalLoadingDock((state) => ({ ...state, id: null, file_url: data.secure_url }));
      setSelectedLoadingDock(null);
      setUploadedFile({ url: data.secure_url, name: `${data.original_filename}.${data.format}` });
    } catch (error) {
      console.error('Upload failed:', error);
    }
    setFileIsUploading(false);
  };

  const handleChange = (e) => {
    const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
    setLocalLoadingDock((state) => ({
      ...state,
      id: null,
      [e.target.name]: value,
      ...(e.target.name !== 'saved_for_future' && { saved_for_future: false }),
      ...(!uploadedFile && state.file_url && { file_url: '' }),
    }));
    setSelectedLoadingDock(null);
  };

  const handleDockSelection = (selectedOption) => {
    if (!selectedOption) {
      setLocalLoadingDock(initialLoadingDock(customer));
      setSelectedLoadingDock(null);
      setUploadedFile(null);
      return;
    }

    if (localLoadingDock.id === selectedOption.value) return;

    const newLoadingDock = customer.loading_docks.find(
      (customerLoadingDock) => customerLoadingDock.id === selectedOption.value
    );
    setLocalLoadingDock(newLoadingDock);
    setSelectedLoadingDock(selectedOption);
    setUploadedFile(null);
  };

  let buttonText = 'Attach Document';

  if (fileIsUploading) {
    buttonText = 'Uploading...';
  }

  if ((localLoadingDock.file_url || uploadedFile) && !fileIsUploading) {
    buttonText = 'Change Document';
  }

  return (
    <>
      <p>
        <label className="bold">Code/Details</label>
        {!!customer.loading_docks.length && (
          <Select
            options={loadingDockOptions}
            value={selectedLoadingDock}
            onChange={handleDockSelection}
            isClearable
            isSearchable={false}
            classNamePrefix="react-select"
            getOptionLabel={(e) => (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ marginLeft: 5 }}>{e.label}</span>
                {e.createdAt && <span style={{ marginLeft: 5, fontSize: '12px' }}>(first used: {e.createdAt})</span>}
              </div>
            )}
            placeholder="Select Existing Loading Docks"
          />
        )}
        <textarea
          name="code"
          value={localLoadingDock.code || ''}
          onChange={handleChange}
          placeholder="Add loading dock code or related delivery information"
        />

        {!!localLoadingDock.file_url && !uploadedFile && (
          <p>
            <strong>Loading Dock File</strong>
            <br />
            <FilePreview fileURL={localLoadingDock.file_url} name={`loading-dock-${order.id}`} />
          </p>
        )}

        <label>
          <input
            type="checkbox"
            name="saved_for_future"
            checked={localLoadingDock.saved_for_future}
            onClick={handleChange}
          />
          Save for future
        </label>
      </p>
      <label htmlFor="file-upload" className="loading-dock-upload button black icon icon-extra-large icon-upload-white">
        {buttonText}
      </label>
      <input style={{ display: 'none' }} id="file-upload" type="file" onChange={handleFileChange} />
      {!!uploadedFile && (
        <p>
          <strong>Uploaded File</strong>
          <br />
          <FilePreview fileURL={uploadedFile.url} name={uploadedFile.name} />
        </p>
      )}

      <a className="loading-dock-submit button" onClick={() => handleSubmit(localLoadingDock)}>
        Save
      </a>
    </>
  );
};

export default Form;
