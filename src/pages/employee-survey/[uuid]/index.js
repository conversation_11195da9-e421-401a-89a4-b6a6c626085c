import { useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import yordar from 'api/yordar';
import Image from 'next/image';
import { DYNAMIC_EMPLOYEE_SURVEY_ENDPOINT } from 'api/endpoints';
import { FullHeightPage, Layout } from 'components/Common';
import { ReloadingSurvey, Survey } from 'components/EmployeeSurvey';
import { employeeSurveySEO } from 'utils/seo';
import { defaultQuestions } from 'components/EmployeeSurvey/utils/defaultQuestions';
import useEmployeeSurveyStore from 'store/useEmployeeSurveyStore';
import { shallow } from 'zustand/shallow';
import { HostContext } from 'context/host';

const EmployeeSurvey = ({ survey }) => {
  const { setSurvey, step, updateStep, isSubmitted } = useEmployeeSurveyStore(
    (state) => ({
      setSurvey: state.setSurvey,
      updateStep: state.updateStep,
      step: state.step,
      isSubmitted: state.isSubmitted,
    }),
    shallow
  );

  const router = useRouter();

  const {
    query: { repeatable },
  } = router;

  const { marketingURL } = useContext(HostContext);

  const [startedSurvey, setStartedSurvey] = useState(false);
  const stepsCount = survey.questions.length - 1;

  useEffect(() => {
    setSurvey({ survey });
  }, [survey]);

  function goHome() {
    window.location = marketingURL;
  }

  function handleButtonClick() {
    if (isSubmitted) {
      goHome();
    } else if (startedSurvey) {
      updateStep();
    } else {
      setStartedSurvey(true);
    }
  }

  function getSurveyButtonText() {
    if (isSubmitted) return 'Yordar Home Page';
    if (!startedSurvey) return "Let's Go!";
    if (step === stepsCount) return 'Submit';
    return 'Next';
  }

  return (
    <Layout seo={employeeSurveySEO} hideHeaderAndFooter>
      <FullHeightPage />
      <div className={`survey-screen${!startedSurvey ? ' justify' : ''}`}>
        {!startedSurvey && (
          <div>
            <div className="logo-container">
              <Image src="/yo.svg" width={300} height={300} />
            </div>
            <div className="survey-welcome">
              <h1 className="survey-welcome__heading">We'd love to hear from you!</h1>
              <p>Spill the beans on your Yordar experience with this custom survey:</p>
              <p className="survey-welcome__name">{survey.name}</p>
            </div>
          </div>
        )}
        {startedSurvey && (
          <>
            <div>
              <p className="survey-welcome__name">{survey.name}</p>
            </div>
            <div className="question-container">
              <Survey />
            </div>
          </>
        )}
        {isSubmitted && repeatable && <ReloadingSurvey />}
        <div className="survey-button-container">
          {(!repeatable || !isSubmitted) && (
            <button type="button" className="survey-button" onClick={handleButtonClick}>
              {getSurveyButtonText()}
            </button>
          )}
          {startedSurvey && !isSubmitted && (
            <button
              type="button"
              onClick={() => (step === 0 ? setStartedSurvey(false) : updateStep(false))}
              className="survey-button back"
            >
              Back
            </button>
          )}
        </div>
      </div>
    </Layout>
  );
};

export const getServerSideProps = async ({ params: { uuid } }) => {
  try {
    const { data: survey } = await yordar.get(DYNAMIC_EMPLOYEE_SURVEY_ENDPOINT(uuid));
    return {
      props: {
        survey: { ...survey, questions: [...survey.questions, ...defaultQuestions] },
      },
    };
  } catch {
    return {
      notFound: true,
    };
  }
};

export default EmployeeSurvey;
