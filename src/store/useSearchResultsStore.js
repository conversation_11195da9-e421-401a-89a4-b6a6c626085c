import { create } from 'zustand';
import yordar from 'api/yordar';
import { SEARCH_MENU_ITEMS_ENDPOINT } from 'api/endpoints';
import { WOOLWORTHS_SUPPLIER_ID } from 'utils/constants';

const initialState = { results: [], showInMenu: false, searchValue: '' };

let currentAbortController = null;

const useSearchResultsStore = create((set) => ({
  ...initialState,
  setShowInMenu: (showInMenu) => set({ showInMenu }),
  setSearchResults: async (searchValue) => {
    // Cancel the previous request if there is one
    if (currentAbortController) {
      currentAbortController.abort();
    }

    // Create a new AbortController for the new request
    currentAbortController = new AbortController();

    try {
      const { data: results } = await yordar.get(SEARCH_MENU_ITEMS_ENDPOINT, {
        params: {
          query: searchValue,
          thorough_search: true,
          supplier_ids: [WOOLWORTHS_SUPPLIER_ID],
          for_supplier_menu: true,
          page: 1,
          limit: 100,
        },
        withCredentials: true,
        signal: currentAbortController.signal, // Pass the AbortController signal to Axios
      });

      // Check if the request was the one associated with the latest signal to avoid race conditions
      if (currentAbortController.signal.aborted) {
        return;
      }
      set({ results, searchValue });
    } catch (error) {
      // Check if the error was due to the request being cancelled
      if (error.name === 'CanceledError') {
        console.log('Request canceled:', error.message);
      } else {
        // Handle other errors
        console.error('Request failed:', error);
      }
    }
  },
  resetSearchResults: () => set({ ...initialState }),
}));

export default useSearchResultsStore;
