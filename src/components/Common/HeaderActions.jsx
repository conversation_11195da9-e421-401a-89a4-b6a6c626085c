import { useContext, useState, useEffect } from 'react';
import Modal from 'react-responsive-modal';
import { Link } from 'components/Common';
import { CHECKOUT_PAGE } from 'api/endpoints';
import { UserContext } from 'context/user';
import { HostContext } from 'context/host';
import useDeliveryDateStore from 'store/useDeliveryDateStore';
import useDocketStore from 'store/useDocketStore';

const HeaderActions = () => {
  const { user, cart } = useContext(UserContext);
  const { clearOrder, zustandCartCount, teamAttendee } = useDocketStore((state) => ({
    clearOrder: state.clearOrder,
    zustandCartCount: state.orders[state?.activeOrderID]?.totals?.order_line_count,
    teamAttendee: state.teamAttendee,
  }));
  const [showModal, setShowModal] = useState(false);
  const { appURL } = useContext(HostContext);
  const clearDate = useDeliveryDateStore((state) => state.clearDate);

  const [cartCount, setCartCount] = useState(cart?.count);

  useEffect(() => {
    setCartCount(zustandCartCount || cart?.count);
  }, [zustandCartCount]);

  async function handleClearCart() {
    try {
      clearOrder();
      clearDate();
      setShowModal(false);
      setCartCount(0);
    } catch (err) {
      console.log(err);
    }
  }

  const accountLinkOptions = () => {
    switch (user?.type) {
      case 'admin':
        return ['admin', 'Admin Panel'];
      case 'supplier':
        return ['s_profile', 'Dashboard'];
      default:
        return ['c_profile', 'My Account'];
    }
  };

  const [userLink, userText] = accountLinkOptions();

  const truncateLongFirstName = (firstName) => {
    if (firstName.length > 12) {
      return `${firstName.slice(0, 12)}...`;
    }
    return firstName;
  };

  const LogoutLink = () => {
    const logout = (e) => {
      e.preventDefault();
      document.getElementById('logout-form-dropdown').submit();
    };
    return (
      <form id="logout-form-dropdown" action={`${appURL}/logout`} method="post">
        <input name="_method" type="hidden" value="delete" />
        <a className="icon icon-logout" href="logout" onClick={logout}>
          Logout
        </a>
      </form>
    );
  };

  if (!user && teamAttendee?.name) {
    return (
      <div className="header-actions">
        <p className="header-actions-link" style={{ marginBottom: 0 }}>
          Hi {truncateLongFirstName(teamAttendee.name.split(' ')[0])}
        </p>
      </div>
    );
  }

  return (
    <div className="header-actions">
      <Link href="/quotes" className="button black outline small compact">
        Get A Quote
      </Link>
      {user && (
        <div className="auth-dropdown">
          <Link
            className={`header-actions-link ${user?.signed_in_as_admin ? 'masquerade' : ''}`}
            href={`${appURL}/${userLink}`}
          >{`Hi ${truncateLongFirstName(user.first_name)}`}</Link>
          <div className="auth-dropdown-content">
            {user?.signed_in_as_admin && (
              <Link className="icon icon-login" href={`${appURL}/admin`}>
                Admin
              </Link>
            )}
            <Link className="icon icon-user" href={`${appURL}/${userLink}`}>
              {userText}
            </Link>
            <LogoutLink />
          </div>
        </div>
      )}
      {!user && (
        <>
          <Link className="header-actions-link" href={`${appURL}/login`}>
            Login
          </Link>
          <Link className="header-actions-link" href={`${appURL}/register`}>
            Register
          </Link>
        </>
      )}
      <Link className="header-actions-link icon icon-shopcart-dark" href={CHECKOUT_PAGE}>
        {!!cartCount && <span className="badge">{cartCount}</span>}
      </Link>
      {!!cartCount && (
        <a className="clear-cart" onClick={() => setShowModal(true)}>
          x
        </a>
      )}
      <Modal
        open={showModal}
        center
        styles={{
          modal: { maxWidth: '500px', padding: '20px', borderRadius: '10px' },
          closeButton: { cursor: 'pointer', marginLeft: '6px' },
        }}
        showCloseIcon={false}
        onClose={() => {}}
      >
        <p className="clear-cart-confirmation">Are you sure you want to clear the cart?</p>
        <div className="clear-cart-btns">
          <button type="button" className="button black clear-cart" onClick={() => setShowModal(false)}>
            No
          </button>
          <button type="button" className="button clear-cart" onClick={handleClearCart}>
            Yes
          </button>
        </div>
      </Modal>
    </div>
  );
};

export default HeaderActions;
