name: Lint

on:
  # Trigger the workflow on push or pull request,
  # but only for the master branch
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  run-linting:
    name: ESLint
    runs-on: ubuntu-latest

    steps:
      - name: Check out Git repository
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v1
        with:
          node-version: '14.x'
          registry-url: 'https://registry.npmjs.org'

      - name: Install dependencies
        run: yarn
      - name: Run ESLint
        run: yarn lint