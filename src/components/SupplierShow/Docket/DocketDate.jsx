import useDeliveryDateStore from 'store/useDeliveryDateStore';

const DocketDate = () => {
  const { setOpenDeliveryModal, deliveryAt, setModalIsClosable } = useDeliveryDateStore((state) => ({
    deliveryAt: state.delivery_at,
    setOpenDeliveryModal: state.setOpenDeliveryModal,
    setModalIsClosable: state.setModalIsClosable,
  }));

  return (
    <div className="docket-time">
      <p>{deliveryAt}</p>
      <button
        type="button"
        className="button"
        onClick={() => {
          setOpenDeliveryModal(true);
          setModalIsClosable(true);
        }}
      >
        Change
      </button>
    </div>
  );
};

export default DocketDate;
