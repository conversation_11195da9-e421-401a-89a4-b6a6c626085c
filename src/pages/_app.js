import App from 'next/app';

import { wrapper } from 'utils/store';
import yordar from 'api/yordar';
import 'utils/methods';
import UserProvider from 'context/user';
import CategoryProvider from 'context/category';
import HostProvider from 'context/host';
import { CART_ENDPOINT, DYNAMIC_LOGIN_ENDPOINT } from 'api/endpoints';
import LogRocketModule from 'logrocket';

import 'react-toastify/dist/ReactToastify.css';
import '../sass/global/styles.scss';
import '../components/Common/Styles/styles.scss';
import '../components/SupplierIndex/Styles/styles.scss';
import '../components/SupplierShow/Styles/styles.scss';
import '../components/Quotes/Styles/styles.scss';
import '../components/Checkout/Styles/styles.scss';
import '../components/EmployeeSurvey/Styles/styles.scss';
import '../components/TeamOrder/Styles/styles.scss';
import '../components/LoadingDock/Styles/styles.scss';
import './styles.scss';
import getBaseURL from 'utils/getBaseURL';
import ErrorBoundary from 'components/Common/ErrorBoundary';

const initializeLogrocket = (user) => {
  if (process.env.NODE_ENV === 'development') return null;
  LogRocketModule.init('ojq7n8/yordar', {
    rootHostname: 'yordar.com.au',
  });
  if (user) {
    LogRocketModule.identify(user.id, {
      name: `${user.first_name} ${user.last_name}`,
      email: user.email,
    });
  }
};

const YordarApp = ({ Component, pageProps, user, cart, notifications = [], baseURLs, locality }) => {
  initializeLogrocket(user);
  const allProps = { ...pageProps, user };
  return (
    <ErrorBoundary>
      <HostProvider baseURLs={baseURLs} locality={locality}>
        <CategoryProvider>
          <UserProvider user={user} cart={cart} notifications={notifications}>
            <Component {...allProps} />
          </UserProvider>
        </CategoryProvider>
      </HostProvider>
    </ErrorBoundary>
  );
};

YordarApp.getInitialProps = async (appContext) => {
  const appProps = await App.getInitialProps(appContext);
  const host = appContext?.ctx?.req?.headers?.host;
  const baseURLs = {
    appURL: getBaseURL({ reqHost: host, type: 'app' }),
    marketingURL: getBaseURL({ reqHost: host, type: 'marketing' }),
    orderURL: getBaseURL({ reqHost: host, type: 'order' }),
  };

  let locality;
  if (host?.includes('.nz')) {
    locality = 'nz';
  } else {
    locality = 'au';
  }

  let param;
  let needsContactDetails;
  let cookie;
  if (typeof window === 'undefined') {
    cookie = appContext?.ctx?.req?.headers?.cookie;
  } else {
    // client side
    cookie = document.cookie;
  }
  const path = appContext?.ctx?.pathname;
  if (appContext.ctx?.query?.suburb) {
    const streetParams = appContext.ctx?.asPath.match(/[&?]street_address=(.*)(&|$)/);
    const streetParam = streetParams ? `&street_address=${streetParams[1]}` : '';
    param = `wants_customer_suppliers=true&state=${appContext.ctx.query.state}&suburb=${appContext.ctx.query.suburb}${streetParam}`;
  } else if (path.includes('checkout/edit')) {
    param = `clear_session_order_id=${appContext.ctx.query.id}`; // clear session order for order being edited
  } else if (path.includes('/quotes')) {
    needsContactDetails = true;
  }
  const {
    data: { user, notifications },
    headers: { 'set-cookie': resCookies },
  } = await yordar.get(DYNAMIC_LOGIN_ENDPOINT(param), {
    withCredentials: true,
    ...(!!cookie && { headers: { Cookie: cookie } }),
    ...(needsContactDetails && { params: { needs_contact_details: true } }),
    baseURL: baseURLs.appURL,
  });
  if (resCookies?.length) {
    appContext.ctx.res.setHeader('set-cookie', resCookies);
  }
  const {
    data: { cart },
  } = await yordar.get(CART_ENDPOINT, { withCredentials: true, ...(!!cookie && { headers: { Cookie: cookie } }) });

  return { ...appProps, user, cart, notifications, baseURLs, locality };
};

export default wrapper.withRedux(YordarApp);
