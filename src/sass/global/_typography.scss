/*=============================================>>>>>
= Default typography styles =
===============================================>>>>>*/

body {
  @include body-font();
  font-size: 16px;
  font-size-adjust: auto;
  line-height: 1.6em;
  text-align: left;
  word-wrap: break-word;
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-shadow: 0 0 1px transparent;

  @include media-down(mobile) {
    font-size: 14px;
  }
}


// --------------------------------------------------------------------------
//   Headings
// --------------------------------------------------------------------------

h1 + h2,
h2 + h3,
h3 + h4,
h5 + h6 {
  margin-top: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  @include heading-font();
  line-height: 1.2em;
  &:first-child {
    margin-top: 0;
  }
}

h1 { font-size: 36px; line-height: 1.1em; }
h2 { font-size: 32px; line-height: 1.1em; }
h3 { font-size: 24px; }
h4 { font-size: 16px; }
h5 { font-size: 16px; }
h6 { font-size: 14px; }

@include media-down(mobile) {

  h1 { font-size: 24px; }
  h2 { font-size: 22px; }
  h3 { font-size: 20px; }
  h4 { font-size: 14px; }
  h5 { font-size: 14px; }
  h6 { font-size: 14px; }

}

// --------------------------------------------------------------------------
//   Content Elements
// --------------------------------------------------------------------------

a {
  text-decoration: none;
  color: $textcolor;
  &:hover {
    text-decoration: underline;
  }
}

p {
  margin-top: 0;
  margin-bottom: 20px;
}

strong {
  font-weight: $font-weight-bold;
}

figure {
  margin: 0;
  padding: 0;
}

img {
  max-width: 100%;
  height: auto;
}

hr {
  display: block;
  margin: 20px 0;
  border: 0;
}

blockquote {
  margin: 0 0 20px;
  font-family: cambria, georgia, times, serif;
  font-style: italic;
  p {
    &:last-child {
      margin-bottom: 0;
    }
  }
}

table {
  width: 100%;
}
