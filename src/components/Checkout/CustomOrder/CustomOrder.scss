// Custom Order Styles
.custom-order-add-btn {
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(31, 158, 134, 0.2);
  }
  
  &:active {
    transform: translateY(0);
  }
}

.custom-order-line {
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
}

.custom-order-line-actions {
  button {
    transition: all 0.2s ease;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
      transform: scale(1.1);
    }
  }
}

// Custom Item Modal Styles
.custom-item-modal {
  .menu-modal {
    padding: 32px;

    .menu-modal__heading {
      font-size: 26px;
      font-weight: 600;
      color: #333;
      margin-bottom: 32px;
      text-align: center;
      border-bottom: 2px solid #eaeaea;
      padding-bottom: 20px;
    }
  }
  .has-servings {
    font-weight: bold;
    color: $primary;
  }
}

// Two-column modal layout
.modal-content-grid {

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

.pricing-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;

  @media (min-width: 769px) {
    grid-template-columns: 1fr 1fr;
  }
}

.custom-item-section {
  margin-bottom: 28px;

  h4 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 18px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eaeaea;
  }

  h5 {
    font-size: 16px;
    font-weight: 600;
    color: #555;
    margin: 16px 0 12px 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
  
  label {
    display: block;
    margin-bottom: 16px;
    font-weight: 500;
    color: #555;
    
    input, select, textarea {
      display: block;
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      margin-top: 6px;
      transition: border-color 0.2s ease;
      
      &:focus {
        outline: none;
        border-color: #1f9e86;
        box-shadow: 0 0 0 2px rgba(31, 158, 134, 0.1);
      }
      
      &:disabled, &[readonly] {
        background-color: #f5f5f5;
        cursor: not-allowed;
      }
    }
    
    textarea {
      resize: vertical;
      min-height: 60px;
    }
  }
}

// Form Layout Styles
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  align-items: start;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

.form-col {
  display: flex;
  flex-direction: column;
}

.form-col-small {
  display: flex;
  flex-direction: column;
  grid-column: span 1;

  @media (min-width: 769px) {
    max-width: 200px;
  }
}

.form-col-large {
  display: flex;
  flex-direction: column;
  grid-column: span 1;

  @media (min-width: 769px) {
    flex: 1;
  }
}

.pricing-section {
  .form-row {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
}

.checkbox-container {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 10px 14px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;

  input[type="checkbox"] {
    margin-right: 10px;
    width: auto;
    transform: scale(1.1);
  }

  label {
    margin-bottom: 0;
    font-weight: 500;
    color: #555;
    cursor: pointer;
    font-size: 14px;
  }
}

.custom-item-modal-footer {
  margin-top: 40px;
  padding-top: 24px;
  border-top: 2px solid #eaeaea;

  .button {
    width: 100%;
    padding: 16px 24px;
    background-color: #1f9e86;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 17px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    box-shadow: 0 2px 4px rgba(31, 158, 134, 0.2);

    &:hover:not(.disabled) {
      background-color: #17856f;
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(31, 158, 134, 0.3);
    }

    &:active:not(.disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(31, 158, 134, 0.3);
    }

    &.disabled {
      background-color: #ccc;
      cursor: not-allowed;
      opacity: 0.6;
      box-shadow: none;
    }

    span {
      font-weight: 600;
      font-size: 17px;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .custom-item-modal {
    .menu-modal {
      padding: 20px;

      .menu-modal__heading {
        font-size: 22px;
        margin-bottom: 24px;
      }
    }
  }

  .custom-item-section {
    margin-bottom: 24px;

    h4 {
      font-size: 18px;
      margin-bottom: 16px;
    }
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .form-col-small,
  .form-col-large {
    max-width: none;
  }
}

// Custom Order Panel specific styles
.checkout-container {
  .custom-order-panel {
    max-width: 100%;
  }
}

.location-section {
  border: 1px solid #eaeaea;
  border-radius: '8px';
  padding: 16px;
  .heading-block {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: -16px -16px 16px -16px;
    padding: 16px;
    background: #e9e9e9;
  }
  h5 {
    margin: 0;
    font-size: 18px;
    color: #333;
  }
}

.custom-supplier-select {
  input {
    box-shadow: none;
  }
}

.supplier-section {
  margin-bottom: 16px;
}

.supplier-image-banner {
  height: 80px;
  width: 100%;
  position: relative;
  img {
    object-fit: contain;
  }
}

.supplier-menu-accordion {
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  font-weight: bold;
  &::after {
    content: '';
    display: inline-block;
    width: 14px;
    height: 14px;
    background: url('../../../images/icons/chevron-right.svg') center no-repeat;
    background-size: contain;
    transition: transform 0.6s;
  }
  &.open::after {
    transform: rotate(90deg);
  }
}

.serving-sizes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  grid-gap: 2px;
  margin: 0 12px 8px 50px;
  button {
    width: 100%;
    font-family: $body-font;
    cursor: pointer;
  }
}
