body.nav-open {
  @include media-down(large-tablet) {
    position: fixed;
    width: 100%;
    overflow: hidden;
  }
}

.header-navigation {
  width: 100%;
  justify-self: center;
  max-width: 600px;
  &-logout {
    background: none;
    border: none;
    padding: 0;
    box-shadow: none;
  }
}

input[type="text"].elastic-search, input[type="text"]#search-menu-items {
  background: url('../../../images/icons/search-grey.svg') no-repeat 12px 50%;
  background-size: 20px;
  padding: 10px;
  padding-left: 40px;
  background-color: #0000000a;
  box-shadow: none;
  font-family: $heading-font;
  &:focus {
    background-color: white;
    border: 1px solid black;
  }
}

.header-navigation-parent-nav {
  grid-template-columns: 1fr 1fr;
  @include media-down(large-tablet) {
    display: block;
    transition: opacity 0.2s;

    &:before {
      content: '';
      width: 60px;
      height: 60px;
      display: block;
      background: url('../../../images/yo.svg') center no-repeat;
      background-size: contain;
      position: relative;
      top: -10px;
      opacity: 0;
      transition: opacity 0.3s 0.5s, top 0.3s 0.5s;
    }
  }

  &.closed {
    @include media-down(large-tablet) {
      @include visibility();
      &:before {
        opacity: 0;
      }
    }
  }

  &.open {
    @include media-down(large-tablet) {
      @include visibility(true);
      position: fixed;
      top: 0;
      left: 0;
      z-index: 9;
      background: $white;
      width: 100%;
      height: 100vh;
      max-height: 100vh;
      overflow-y: scroll;
      -webkit-overflow-scrolling: touch;
      padding: 25px 20px 30px;
      &:before {
        top: 0;
        opacity: 1;
      }
    }
    @include media-down(large-mobile) {
      padding: 18px 20px 30px;
    }
    .header-navigation-parent-link {
      @include media-down(large-tablet) {
        opacity: 1;
        top: 0;
      }
    }
  }
}

// Parent link (office catering/kitchen supplies)
// ------------------------------------
.header-navigation-parent-link {
  position: relative;
  display: block;
  &.large-hidden {
    @include media-up(small-tablet) {
      display: none;
    }
  }

  @include media-down(large-tablet) {
    padding: 0;
    opacity: 0;
    top: -15px;
    transition: top 0.3s 0.2s, opacity 0.3s 0.2s;
  }
}

// element with down chevron
.header-navigation-parent-link-toggle {
  @include heading-font();
  font-weight: $font-weight-bold;
  align-items: center;
  font-size: 14px;
  text-transform: uppercase;
  cursor: pointer;
  @include media-down(large-tablet) {
    font-size: 17px;
    padding-top: 25px;
  }
  &.large-hidden {
    display: none;
  }
  &:after {
    @include media-up(large-tablet) {
      content: '';
      display: inline-block;
      width: 14px;
      height: 14px;
      background: url('../../../images/icons/expand-arrow.svg') center no-repeat;
      background-size: contain;
      margin-left: 6px;
      transition: transform 0.3s;
    }
  }
}

// Hover state
.header-navigation-parent-link {
  &:hover {
    .header-navigation-parent-link-toggle {
      &:after {
        @include media-up(large-tablet) {
          transform: rotate(-180deg);
        }
      }
    }
  }
}

// Header nav dropdown
// ------------------------------------
.header-navigation-dropdown {
  font-size: 15px;
  @include media-up(large-tablet) {
    opacity: 0;
    visibility: hidden;
    height: 0;
    overflow: hidden;
    position: absolute;
    top: 35px;
    left: -25px;
    background: $white;
    width: 550px;
    padding: 35px 40px 20px;
    box-shadow: 0 0 10px 0 rgba($black, 0.10);
    border-radius: 5px;
    transition: transform 0.3s;
  }
  @include media-down(large-tablet) {
    color: $white;
    padding: 8px 0 0;
  }
}

// Two column links
.header-navigation-dropdown-links {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  flex-wrap: wrap;
  padding-bottom: 15px;
  @include media-down(large-tablet) {
    padding-bottom: 0;
  }
  @include media-down(mobile) {
    grid-template-columns: 1fr;
  }
  li {
    a {
      display: flex;
      align-items: center;
      padding: 8px 0;
      @include media-down(large-tablet) {
        padding: 8px 0;
      }
      &.icon {
        &:before {
          margin-right: 12px;
          width: 22px;
          height: 22px;
          transition: transform 0.3s;
          @include media-down(large-tablet) {
            width: 22px;
            height: 22px;
            margin-right: 10px;
          }
        }
      }
      &:hover {
        text-decoration: underline;
        &.icon {
          &:before {
            transform: translateY(-2px);
          }
        }
      }
    }
  }
}

.elastic-search-group {
    &.dash {
      @include media-down(large-tablet) {
      display: none;
    }
  }
  &.active .elastic-search-results {
    display: block;
    max-height: 500px;
    overflow: scroll;
  }
  &.active .elastic-search-results.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
  }
  &.mobile  > input {
    width: 100%;
  }
  &.dash {
    position: relative;
    input {
      font-size: 14px;
    }
  }
}

.search-result-no-results {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}


.elastic-search-results {
  display: none;
  position: absolute;
  background: white;
  width: 100%;
  box-shadow: 0 6px 20px 0 rgba($black, 0.10);
  padding: 10px;
  z-index: 9999;
  .search-result-container {
    display: flex;
    align-items: center;
    padding: 8px 0;
    span {
      flex-shrink: 0;
    }
  }
  .search-result-title {
    margin: 6px 0;
    font-weight: bold;
    display: flex;
    align-items: center;
  }
  .search-result-info {
    line-height: 24px;
    cursor: pointer;
    margin-left: 10px;
  }
  .search-result-name {
    margin-bottom: 0;
  }
  .search-result-label {
    font-size: 14px;
    color: rgb(97, 97, 97);
    margin-bottom: 0;
    > a {
      color: currentColor;
    }
  }
  .search-result-category {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding-bottom: 6px;
    &:hover {
      text-decoration: underline;
    }
    &::before {
      content: '';
      display: inline-block;
      background: url('../../../images/icons/search.svg') no-repeat;
      width: 16px;
      height: 16px;
      margin-right: 10px;
    }
  }
  .in-menu-button {
    color: white;
    margin-left: 16px;
    font-size: 12px;
    cursor: pointer;
    background: #ffffff;
    padding: 0px 6px;
    background: black;
    line-height: 20px;
  }
}

.circle-icon {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 10px;
  &.no-image {
    display: flex;
    align-items: center;
    justify-content: center;
    background: black;
    color: white;
  }
  &.no-margin {
    margin: 0;
  }
  &.dash {
    min-width: 32px;
    min-height: 32px;
    max-height: 32px;
    max-width: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &.tooltip {
    min-width: 40px;
    min-height: 40px;
    max-height: 40px;
    max-width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
  }
}
