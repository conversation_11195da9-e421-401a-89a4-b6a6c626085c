const baseUrl = {
  development: 'http://localhost:8080',
  production: 'https://order.yordar.com.au',
}[process.env.NODE_ENV];

export const mapSuppliers = (supplier) =>
  `
    <url>
      <loc>${baseUrl}/show/${supplier}</loc>
      <changefreq>monthly</changefreq>
      <priority>1.0</priority>
    </url>
  `;

export const mapSuburbs = (location) => {
  const [state, suburbs] = location;
  return suburbs.map(
    (suburb) =>
      `
        <url>
          <loc>${baseUrl}/search/office-catering/${state}/${suburb}</loc>
          <changefreq>monthly</changefreq>
          <priority>1.0</priority>
        </url>
        <url>
          <loc>${baseUrl}/search/office-snacks/${state}/${suburb}</loc>
          <changefreq>monthly</changefreq>
          <priority>1.0</priority>
        </url>
      `
  );
};
