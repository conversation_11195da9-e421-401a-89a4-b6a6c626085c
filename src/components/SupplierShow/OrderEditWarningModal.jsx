import { useState, useEffect } from 'react';

import Modal from 'react-responsive-modal';
import useDocketStore from 'store/useDocketStore';

const OrderEditWarningModal = ({ editingOrder, order }) => {
  const { clearOrder } = useDocketStore((state) => ({
    clearOrder: state.clearOrder,
  }));
  const [openWarning, setOpenWarning] = useState(editingOrder);

  useEffect(() => {
    setOpenWarning(editingOrder);
  }, [editingOrder]);

  return (
    <Modal
      open={openWarning}
      center
      styles={{
        modal: { maxWidth: '500px', padding: 0, borderRadius: '10px' },
      }}
      showCloseIcon={false}
      onClose={() => {}}
    >
      <div className="menu-modal">
        <h3 className="menu-modal__heading">You are currently editing an Order!</h3>
        <p>
          You're editing an order in the cart named:
          <br />
          <strong>{order.name}</strong> (#{order.id})
        </p>
        <p>Do you wish to continue editing the order?</p>
        <a
          className="modal-button"
          onClick={() => {
            clearOrder();
            setOpenWarning(false);
          }}
        >
          Start New Order
        </a>
        <a className="modal-button primary" onClick={() => setOpenWarning(false)}>
          Yes Continue
        </a>
      </div>
    </Modal>
  );
};

export default OrderEditWarningModal;
