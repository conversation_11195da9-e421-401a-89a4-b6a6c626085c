import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';

import useDeliveryDateStore from 'store/useDeliveryDateStore';
import { formatCategory } from 'utils/format';
import { FilterTags, GeneralSearch, MealFilters, SupplierGrid, SupplierHeader } from 'components/SupplierIndex';
import { filterSuppliers } from 'utils/filtering';
import useFilterStore from 'store/useFilterStore';
import { mealPlanFilters } from 'utils/constants';

const SuppliersDisplay = ({ toggleNav }) => {
  const filters = useFilterStore((state) => state.filters);
  const suppliers = useSelector((state) => state.suppliers.index);
  const { deliveryDate } = useDeliveryDateStore((state) => ({
    deliveryDate: state.delivery_date,
  }));

  const router = useRouter();
  const {
    query: { category: categoryPage, mealUUID },
  } = router;
  const filteredSuppliers = filterSuppliers(suppliers, filters, !!mealUUID, deliveryDate);
  const mealFilters = filters.filter((filter) => !mealPlanFilters.includes(filter));

  return (
    <div>
      <SupplierHeader
        formatCategory={formatCategory}
        hasFilters={filters.length || suppliers.length !== filteredSuppliers.length}
        suppliersCount={{ filtered: filteredSuppliers.length, total: suppliers.length }}
      />
      <div className="mobile-search-and-filters">
        <GeneralSearch categoryPage={categoryPage} mobile />
        <button
          type="button"
          onClick={toggleNav}
          className="icon icon-filters mobile-filters-open-btn"
          aria-label="Open Filters"
        />
      </div>
      {mealUUID && <MealFilters />}
      <div className="suppliers-list">
        {!!filters.length && !!mealFilters.length && <FilterTags filters={mealUUID ? mealFilters : filters} />}
        {filteredSuppliers?.length === 0 && <h3>No Suppliers Found</h3>}
        {!!filteredSuppliers.length && <SupplierGrid suppliers={filteredSuppliers} />}
      </div>
    </div>
  );
};

export default SuppliersDisplay;
