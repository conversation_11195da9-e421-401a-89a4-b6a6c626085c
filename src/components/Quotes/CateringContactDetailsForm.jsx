import { useContext, useEffect } from 'react';
import { UserContext } from 'context/user';

import { InputField, QuoteRecaptcha } from 'components/Quotes';
import useQuoteStore from 'store/useQuoteStore';

const CateringContactDetailsForm = () => {
  const { formFields, setFormField } = useQuoteStore((state) => ({
    formFields: state.formFields,
    setFormField: state.setFormField,
  }));

  const { user } = useContext(UserContext);

  useEffect(() => {
    const fieldMap = {
      fullName: user?.first_name && user?.last_name ? `${user.first_name} ${user.last_name}` : null,
      email: user?.email,
      company: user?.company,
      phone: user?.phone,
    };
    Object.entries(fieldMap).forEach(([key, value]) => {
      if (value) {
        setFormField(key, value);
      }
    });
  }, []);

  const inputFields = [
    { type: 'text', label: 'Full Name', field: 'fullName', className: 'icon icon-user icon-large icon-right-spacer' },
    { type: 'tel', label: 'Phone Number', field: 'phone', className: 'icon icon-phone icon-large icon-right-spacer' },
    { type: 'email', label: 'Email', field: 'email', className: 'icon icon-email icon-large icon-right-spacer' },
    {
      type: 'text',
      label: 'Company Name',
      field: 'company',
      className: 'icon icon-company icon-large icon-right-spacer',
    },
  ];

  return (
    <>
      <div className="quote-details-form">
        {inputFields.map(({ type, label, field, className }) => (
          <InputField
            key={field}
            type={type}
            label={label}
            className={className}
            isError={formFields[field].isError}
            onChange={(value) => setFormField(field, value)}
            value={formFields[field].value}
          />
        ))}
      </div>
      <h3 className="quote-sub-heading">Did we miss something?</h3>
      <textarea
        className="quote-textarea"
        placeholder="Please let us know if there's any more information that will help us in setting up your catering quote"
        value={formFields.extraContactInformation.value}
        onChange={(e) => setFormField('extraContactInformation', e.target.value)}
      />
      {!user && <QuoteRecaptcha />}
    </>
  );
};

export default CateringContactDetailsForm;
