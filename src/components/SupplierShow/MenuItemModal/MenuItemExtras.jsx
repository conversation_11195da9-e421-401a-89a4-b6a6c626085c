import { useState } from 'react';
import { MenuItemExtra } from 'components/SupplierShow';

const MenuItemExtras = ({ section, extras, setExtras }) => {
  const [extraCount, setExtraCount] = useState(0);
  const extraDetails = { extras, setExtras, extraCount, setExtraCount };
  return (
    <>
      <h4 className="menu-item-modal-extra-title">{section.name}</h4>
      <div className="menu-item-modal-extras-container">
        {section.menu_extras.map((extra) => (
          <MenuItemExtra extraDetails={{ extra, ...extraDetails }} key={extra.id} maxLimit={section.max_limit} />
        ))}
      </div>
    </>
  );
};

export default MenuItemExtras;
