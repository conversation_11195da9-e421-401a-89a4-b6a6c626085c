.header-actions {
  display: flex;
  align-items: center;
  margin-left: 12px;
  > * + * {
    padding-left: 12px;
  }
  @include media-down(large-tablet) {
    display: none;
  }
}

.header-actions-link {
  display: block;
  @include heading-font();
  font-weight: $font-weight-bold;
  font-size: 14px;
  text-transform: uppercase;
  max-width: 200px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  &.icon {
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
    &:before {
      margin: 0 0 0 8px;
      width: 18px;
      height: 18px;
    }
  }
  .badge {
    @include ellipsis();
    display: flex;
    align-items: center;
    justify-content: center;
    flex-grow: 0;
    flex-shrink: 0;
    background: $highlight;
    color: $white;
    margin: 0 0 0 8px;
    width: 20px;
    height: 20px;
    font-size: 12px;
    line-height: 1em;
    border-radius: 50%;
  }

  &:hover {
    .badge {
      text-decoration: none;
    }
  }
}


.auth-dropdown {
  position: relative;
  display: inline-block;
  .header-actions-link {
    &::after {
      content: '';
      background: url('../../../images/icons/chevron-down.svg');
      width: 12px;
      height: 12px;
      display: inline-block;
      vertical-align: middle;
      margin-left: 10px;
      transition: transform 0.2s;
    }
  }
  &:hover {
    .header-actions-link::after {
      transform: rotate(-180deg);
    }
    .auth-dropdown-content {
      display: block;
    }
  }
}

.auth-dropdown-content {
  display: none;
  position: absolute;
  background-color: $white;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.1);
  z-index: 1;
  padding: 10px;
  a {
    display: block;
    padding: 8px 4px;
    font-family: $heading-font;
    font-weight: bold;
    font-size: 14px;
    &::before {
      margin-right: 6px;
    }
  }
}

.clear-cart {
  cursor: pointer;
  &:hover {
    text-decoration: none;
  }
}
.clear-cart-confirmation {
  font-size: 18px;
  padding-bottom: 10px;
}

.clear-cart-btns {
  text-align: right;
  button:first-of-type {
    margin-right: 6px;
  }
}

.masquerade {
  display: flex !important;
  align-items: center;
  &::before {
    content: '';
    margin-right: 8px;
    width: 24px;
    height: 24px;
    background-image: url('../../../images/icons/eye.svg');
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
  }
}