export const allergyDietaryOptions = {
  potentialAllergies: ['Dairy', 'Fish', 'Gluten', 'Nuts', 'Poultry/Eggs'],
  preferences: ['Dairy Free', 'Gluten Free', 'Vegan', 'Vegetarian'],
};

export const eventOptions = {
  requirements: ['Food', 'Alcoholic Drinks', 'Non Alcoholic Drinks', 'Event Staffing', 'Equipment Hire'],
  styles: ['BBQ', 'Buffet', 'Cocktail Party', 'Grazing Platters', 'Plated Catering'],
  serviceStyles: ['Cocktail Tray Service', 'Cook & Serve', 'Buffet Staff Service', 'Staff Service'],
};

export const IconClasses = {
  BREAKY: 'icon-toaster',
  MORNING_TEA: 'icon-teapot',
  LUNCH: 'icon-restaurant',
  AFTERNOON_TEA: 'icon-teacup',
  FINGER_FOOD: 'icon-hand-right',
  BUFFET: 'icon-buffet',
  OFFICE_SNACKS: 'icon-chocolate-bar',
  OFFICE_DRINKS: 'icon-solo-cup',
  OFFICE_COFFEE: 'icon-coffee',
  OFFICE_FRUIT: 'icon-apple',
  OFFICE_MILK: 'icon-milk',
  PANTRY_MANAGEMENT: 'icon-shopping-basket',
};

export const quoteCategories = {
  catering: [
    {
      name: 'Breaky',
      class: IconClasses.BREAKY,
    },
    {
      name: 'Morning Tea',
      class: IconClasses.MORNING_TEA,
    },
    {
      name: 'Lunch',
      class: IconClasses.LUNCH,
    },
    {
      name: 'Afternoon Tea',
      class: IconClasses.AFTERNOON_TEA,
    },
    {
      name: 'Finger Food',
      class: IconClasses.FINGER_FOOD,
    },
    {
      name: 'Buffet',
      class: IconClasses.BUFFET,
    },
  ],
  snacks: [
    {
      name: 'Office Snacks',
      class: IconClasses.OFFICE_SNACKS,
    },
    {
      name: 'Office Drinks',
      class: IconClasses.OFFICE_DRINKS,
    },
    {
      name: 'Office Coffee',
      class: IconClasses.OFFICE_COFFEE,
    },
    {
      name: 'Office Fruit',
      class: IconClasses.OFFICE_FRUIT,
    },
    {
      name: 'Office Milk',
      class: IconClasses.OFFICE_MILK,
    },
    {
      name: 'Pantry Management',
      class: IconClasses.PANTRY_MANAGEMENT,
    },
  ],
};
