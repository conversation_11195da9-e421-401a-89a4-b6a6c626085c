import { useRef, useEffect } from 'react';

function useOutsideAlerter(ref) {
  useEffect(() => {
    function handleClick(event) {
      if (ref.current && !ref.current.contains(event.target)) {
        ref.current.classList.remove('active');
      }
      if (ref.current && ref.current.contains(event.target)) {
        ref.current.classList.add('active');
      }
    }
    document.addEventListener('mousedown', handleClick);
    return () => {
      document.removeEventListener('mousedown', handleClick);
    };
  }, [ref]);
}

export default function OutsideAlerter({ className, children }) {
  const wrapperRef = useRef(null);
  useOutsideAlerter(wrapperRef);

  return (
    <div ref={wrapperRef} className={className}>
      {children}
    </div>
  );
}
