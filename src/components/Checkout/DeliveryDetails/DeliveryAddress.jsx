import useCheckoutStore from 'store/useCheckoutStore';

const DeliveryAddress = ({ isWoolworthsOrder, handleButtonClick }) => {
  const { deliveryAddressLevel, deliverySuburbLabel, deliveryAddress, deliveryAddressValidation } = useCheckoutStore(
    (state) => ({
      deliveryAddressLevel: state.order.deliveryAddressLevel,
      deliverySuburbLabel: state.order.deliverySuburbLabel,
      deliveryAddress: state.order.deliveryAddress,
      deliveryAddressValidation: state.validations.deliveryAddress,
    })
  );

  return (
    <div className={`modal-section${deliveryAddressValidation === false ? ' invalid' : ''}`}>
      {deliveryAddress && (
        <div className="checkout-row icon icon-extra-large icon-marker-filled">
          <div>
            <span className="bold">
              {deliveryAddressLevel ? `${deliveryAddressLevel}/` : ''}
              {deliveryAddress}
            </span>{' '}
            <p style={{ lineHeight: '16px' }}>{deliverySuburbLabel}</p>
          </div>
        </div>
      )}
      {!deliveryAddress && (
        <p className="checkout-row icon icon-extra-large icon-marker-filled bold">Please set a street address</p>
      )}
      <button
        type="button"
        className={`checkout-button button ${isWoolworthsOrder ? 'disabled' : ''} ${
          deliveryAddress ? 'white' : 'black'
        } rounded ${deliveryAddressValidation === false ? 'invalid--button' : ''}`}
        onClick={() => !isWoolworthsOrder && handleButtonClick('address')}
      >
        {deliveryAddress ? 'Change' : 'Add'}
      </button>
    </div>
  );
};

export default DeliveryAddress;
