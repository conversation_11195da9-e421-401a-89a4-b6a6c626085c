import create from 'zustand';
import yordar from 'api/yordar';

import { DYNAMIC_EMPLOYEE_SURVEY_SUBMISSION_ENDPOINT } from 'api/endpoints';

const initialState = {
  step: 0,
  survey: null,
  answers: [],
  employee_survey_submission: {
    name: null,
    overall_rating: null,
  },
  isSubmitted: false,
  captchaResponse: null,
};

const useEmployeeSurveyStore = create((set, get) => ({
  ...initialState,
  setSurvey: ({ survey }) => {
    set({
      survey,
      answers: new Array(survey.questions.length + 2).fill(null),
    });
  },
  updateStep: (increment = true) => {
    const newStep = get().step + (increment ? 1 : -1);
    if (increment && newStep >= get().survey.questions.length) {
      get().submitSurvey();
    } else {
      set({ step: newStep });
    }
  },
  updateSubmission: (event) => {
    set((state) => ({
      employee_survey_submission: { ...state.employee_survey_submission, [event.target.name]: event.target.value },
    }));
  },
  saveAnswer: ({ step, answer }) => {
    set((state) => ({
      answers: state.answers.map((stateAnswer, idx) => {
        if (idx === step) return answer;

        return stateAnswer;
      }),
    }));
  },
  submitSurvey: async () => {
    const surveyAnswers = get().answers.filter((answer) => answer?.survey_question_id && answer?.value);
    const employeeSurveySubmission = get().employee_survey_submission;
    const { uuid } = get().survey;
    try {
      await yordar({
        method: 'POST',
        url: DYNAMIC_EMPLOYEE_SURVEY_SUBMISSION_ENDPOINT(uuid),
        data: { employee_survey_submission: { ...employeeSurveySubmission, survey_answers: surveyAnswers } },
      });
      set({ isSubmitted: true });
    } catch (error) {
      console.log(error);
    }
  },
  setCaptchaResponse: (response) => set(() => ({ captchaResponse: response })),
}));

export default useEmployeeSurveyStore;
